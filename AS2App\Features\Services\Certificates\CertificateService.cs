using System.Security.Cryptography.X509Certificates;
using AS2Test.Models;
using Microsoft.Extensions.Options;
using Azure.Identity;
using Azure.Security.KeyVault.Certificates;
using System.Security.Cryptography;
using AS2App.Models.ConfigModels;

namespace AS2Test_master.Features.Services.Certificates
{
    /// <summary>
    /// Service to provide X509 Certificates 
    /// </summary>
    public class CertificateService : ICertificateService
    {
        private readonly CertificateClient _certificateClient;
        private readonly ILogger<CertificateService> _logger;

        public CertificateService(IOptions<KeyVaultConfig> keyVaultConfig, ILogger<CertificateService> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            var config = keyVaultConfig?.Value ?? throw new ArgumentNullException(nameof(keyVaultConfig));
            var credential = new ClientSecretCredential(config.TenantId, config.ClientId, config.ClientSecret);
            _certificateClient = new CertificateClient(new Uri(config.VaultUri), credential);
        }

        private byte[] GetCertificateBytes(string base64OrPem)
        {
            if (string.IsNullOrWhiteSpace(base64OrPem))
                throw new ArgumentException("Certificate is required and cannot be null or empty", nameof(base64OrPem));

            if (base64OrPem.StartsWith("-----BEGIN CERTIFICATE-----"))
            {
                // PEM format
                return Convert.FromBase64String(base64OrPem
                    .Replace("-----BEGIN CERTIFICATE-----", "")
                    .Replace("-----END CERTIFICATE-----", "")
                    .Replace("\n", "")
                    .Replace("\r", ""));
            }
            else
            {
                // Base64 format
                return Convert.FromBase64String(base64OrPem);
            }
        }
        public Task<X509Certificate2> ParseSenderCertificate(string base64OrPem, string password = null)
        {
            if (string.IsNullOrWhiteSpace(base64OrPem))
                throw new ArgumentException("Certificate is required and cannot be null or empty", nameof(base64OrPem));

            try
            {
                var rawData = GetCertificateBytes(base64OrPem);
                
                var certificate = string.IsNullOrEmpty(password)
                    ? new X509Certificate2(rawData)
                    : new X509Certificate2(rawData, password, X509KeyStorageFlags.Exportable);
                return Task.FromResult(certificate);
            }
            catch (CryptographicException ex)
            {
                _logger.LogError(ex, "Error loading partner certificate.");
                throw new InvalidOperationException("Error loading partner certificate.", ex);
            }
            catch (FileNotFoundException ex)
            {
                _logger.LogError(ex, "Partner certificate file not found.");
                throw new InvalidOperationException("Partner certificate file not found.", ex);
            }
        }


        public Task<X509Certificate2> GetSenderCertificateAsync(string signerId)
        {
            if (string.IsNullOrWhiteSpace(signerId))
                throw new ArgumentException("SignerId is required and cannot be null or empty", nameof(signerId));

            try
            {
                var certificate = new X509Certificate2("./Cert/PrivateKeyCert.pfx", "1234", X509KeyStorageFlags.Exportable);
                return Task.FromResult(certificate);
            }
            catch (CryptographicException ex)
            {
                _logger.LogError(ex, "Error loading sender certificate.");
                throw new InvalidOperationException("Error loading sender certificate.", ex);
            }
            catch (FileNotFoundException ex)
            {
                _logger.LogError(ex, "Sender certificate file not found.");
                throw new InvalidOperationException("Sender certificate file not found.", ex);
            }
        }

        public Task<X509Certificate2> GetPartnerCertificate(string certificateBase64)
        {
            try
            {
                var rawBytes = Convert.FromBase64String(certificateBase64);

                // Try as DER or PEM first (public cert, no password needed)
                var cert = new X509Certificate2(rawBytes);

                if (!cert.HasPrivateKey)
                {
                    return Task.FromResult(cert);
                }

                throw new InvalidOperationException("Partner certificate unexpectedly contains a private key.");
            }
            catch (CryptographicException ex)
            {
                _logger.LogError(ex, "Invalid certificate format for partner certificate.");
                throw new InvalidOperationException("Error parsing partner certificate. Ensure it's a .cer/.crt format.", ex);
            }
        }

        public Task<X509Certificate2> GetSenderCertificate(string certificateBase64, string password)
        {
            try
            {
                var rawBytes = Convert.FromBase64String(certificateBase64);
                var cert = new X509Certificate2(rawBytes, password, X509KeyStorageFlags.Exportable | X509KeyStorageFlags.PersistKeySet);

                if (!cert.HasPrivateKey)
                {
                    throw new InvalidOperationException("Sender certificate must contain a private key.");
                }

                return Task.FromResult(cert);
            }
            catch (CryptographicException ex)
            {
                _logger.LogError(ex, "Invalid sender certificate or password.");
                throw new InvalidOperationException("Error parsing sender certificate. Ensure it's a .pfx with the correct password.", ex);
            }
        }

        public async Task<X509Certificate2> GetSenderCertificateFromKeyVaultAsync(string senderId)
        {
            if (string.IsNullOrWhiteSpace(senderId))
                throw new ArgumentException("SenderId is required.", nameof(senderId));

            var certificateWithPolicy = await _certificateClient.GetCertificateAsync(senderId);
            return new X509Certificate2(certificateWithPolicy.Value.Cer);
        }

        public async Task<X509Certificate2> GetReceiverCertificateFromKeyVaultAsync(string receiverId)
        {
            if (string.IsNullOrWhiteSpace(receiverId))
                throw new ArgumentException("ReceiverId is required.", nameof(receiverId));

            var certificateWithPolicy = await _certificateClient.GetCertificateAsync(receiverId);
            return new X509Certificate2(certificateWithPolicy.Value.Cer);
        }
    }
}