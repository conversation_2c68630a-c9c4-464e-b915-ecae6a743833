﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;
using static AzureEbrModels.ProductNumber;

namespace AzureEbrModels
{
    public enum VMOrderTypeEnum
    {
        VERIFICATION,
        REPACK_BUFFER
        //DECOMMISSION
    }

    public enum VMOrderStatusEnum
    {
        /// <summary>
        /// Just created
        /// </summary>
        NEW,

        /// <summary>
        /// Approved for scanning
        /// </summary>
        APPROVED,

        /// <summary>
        /// Scanning and adding SN
        /// </summary>
        PROCESSING,

        /// <summary>
        /// Send to EU Hub
        /// </summary>
        VERIFYING,

        /// <summary>
        /// Received response from EU Hub
        /// </summary>
        VERIFIED,

        /// <summary>
        /// Failed verification from EU Hub
        /// </summary>
        FAILED,

        CANCELED
    }

    public class VMOrderStatusHelperClass
    {
        public VMOrderStatusHelperClass(AzureEbrModels.VMOrderStatusEnum status, object icon, string color)
        {
            this.Title = status.ToString();
            this.Status = status;
            this.Icon = icon;
            this.Color = color;
        }

        public string Title { get; set; }
        public AzureEbrModels.VMOrderStatusEnum Status { get; set; }
        public object Icon { get; set; }
        public string Color { get; set; }
    }

    [Table("VMOrder")]
    public partial class VMOrder
    {
        public VMOrder()
        {
            Items = new HashSet<VMOrderItem>();
        }

        [Key]
        [DisplayName("ID")]
        public int IDVMOrder { get; set; }

        public VMOrderTypeEnum VMOrderType { get; set; }

        public int? IDBatch { get; set; }

        [StringLength(50, ErrorMessage = "The field Request GUID must be a string with a maximum length of 50.")]
        [DisplayName("Request GUID")]
        public string RequestGUID { get; set; }

        [Required(ErrorMessage = "The Code Type field is required.")]
        [StringLength(10, ErrorMessage = "The field Code Type must be a string with a maximum length of 10.")]
        [DisplayName("Code Type")]
        public string ProductCodeType { get; set; }

        [StringLength(14, ErrorMessage = "The field Product Code must be a string with a maximum length of 14.")]
        [DisplayName("Product Code")]
        [CodeValidation(ErrorMessage = "Invalid product code for selected type")]
        [Required]
        public string ProductCode { get; set; }

        [StringLength(20)]
        [DisplayName("Batch ID")]
        [RegularExpression(@"^([\u0021;\u0022;\u0025;\u0026;\u0027;\u0028;\u0029;\u002A;\u002B;\u002C;\u002D;\u002E;\u002F;\u003A;\u003B;\u003C;\u003D;\u003E;\u003F;\u005F;a-zA-Z0-9]{1,20})$",
            ErrorMessage = "Batch ID is expected to be alphanumeric values of up to 20 characters, limited to those characters defined in the GS1 General Specifications.")]
        public string BatchID { get; set; }

        [DisplayName("Expiry Date")]
        [DisplayFormat(DataFormatString = "{0:dd-MM-yyyy}", ApplyFormatInEditMode = true)]
        [MinTomorrowDate(ErrorMessage = $"Invalid value for Expiry date. The field has no value or is in the past.")]
        public DateTime? ExpiryDate { get; set; }

        [DisplayName("Expiry Date Zero Day")]
        [DisplayFormat(DataFormatString = "{0:MM-yyyy}", ApplyFormatInEditMode = true)]
        public bool ExpiryDateZeroDay { get; set; }

        public VMOrderStatusEnum Status { get; set; }

        [StringLength(255, ErrorMessage = "The field Product Name must be a string with a maximum length of 255.")]
        [DisplayName("Product Name")]
        public string ProductName { get; set; }

        [StringLength(255, ErrorMessage = "The field Transport must be a string with a maximum length of 255.")]
        public string Transport { get; set; }

        [StringLength(255, ErrorMessage = "The field Purchase ID must be a string with a maximum length of 255.")]
        [DisplayName("Purchase ID")]
        public string PurchaseID { get; set; }

        [StringLength(255, ErrorMessage = "The field Item Number must be a string with a maximum length of 255.")]
        [DisplayName("Item Number")]
        public string ItemNumber { get; set; }

        [StringLength(255, ErrorMessage = "The field Order Number must be a string with a maximum length of 255.")]
        [DisplayName("Order Number")]
        public string OrderNumber { get; set; }

        [DisplayName("Publish Date")]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}")]
        public DateTime? PublishTimestamp { get; set; }

        [Required]
        [DisplayName("Timestamp Created")]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        [DisplayName("User Created")]
        public string UserCreated { get; set; }

        [DisplayName("Timestamp Updated")]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}")]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        [DisplayName("User Updated")]
        public string UserUpdated { get; set; }

        [InverseProperty("Order")]
        public ICollection<VMOrderItem> Items { get; set; }

        [InverseProperty("VMOrders")]
        public Batch Batch { get; set; }


        public class CodeValidationAttribute : ValidationAttribute
        {
            public override bool IsValid(object value)
            {
                var code = (string)value;

                try
                {
                    if (GTIN.Check(code))
                        return true;
                    else if (PPN.Check(code))
                        return true;
                    else
                        return false;
                }
                catch (Exception e)
                {
                    var debug = e.Message;
                    return false;
                }

            }


        }

        public class MinTomorrowDateAttribute : ValidationAttribute
        {
            public override bool IsValid(object value)
            {
                if (value == null) 
                    return false;

                if (value is DateTime dateValue && dateValue.Date <= DateTime.Now.Date.AddDays(1))
                    return false;

                return true;
            }
        }
    }
}

