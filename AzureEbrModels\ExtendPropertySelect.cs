﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEbrModels
{
    [Table("ExtendPropertySelect")]
    public class ExtendPropertySelect
    {
        [Key]
        public int IDExtendPropertySelect { get; set; }

        [Required]
        public int IDExtendProperty { get; set; }

        [Required]
        [StringLength(100)]
        public string Key { get; set; }


        [Required]
        [StringLength(100)]
        public string Value { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        [InverseProperty("ExtendPropertySelects")]
        public ExtendProperty ExtendProperty { get; set; }
    }
}
