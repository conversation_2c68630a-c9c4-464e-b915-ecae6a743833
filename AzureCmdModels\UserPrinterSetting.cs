﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureCmdModels
{
    [Table("UserPrinterSetting")]
    public class UserPrinterSetting
    {
        [Key]
        [StringLength(50)]
        public string LgnName { get; set; }

        public bool UseNetworkPrinter { get; set; }

        [StringLength(20)]
        [RegularExpression("^(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$")]
        public string NetworkPrinterIP { get; set; }

        public int? NetworkPrinterPort { get; set; }

        [StringLength(250)]
        public string PrinterDriverName { get; set; }

        [StringLength(8)]
        public string PrinterLanguageType { get; set; }

        public DateTime TimestampCreated { get; set; }

        public DateTime? TimestampUpdated { get; set; }
        public string UserCreated { get; set; }
        public string UserUpdated { get; set; }

    }
}
