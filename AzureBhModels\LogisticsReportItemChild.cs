﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureBhModels
{
    [Table("LogisticsReportItemChild")]
    public class LogisticsReportItemChild
    {
        [Key]
        public int IDLogisticsReportItemChild { get; set; }

        public int IDLogisticsReportItem { get; set; }

        [StringLength(50)]
        public string SerialType { get; set; }

        [StringLength(14)]
        public string Code { get; set; }

        [Required]
        [StringLength(100)]
        public string SerialNumber { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [InverseProperty("LogisticsReportItemChilds")]
        public LogisticsReportItem LogisticsReportItem { get; set; }
    }
}
