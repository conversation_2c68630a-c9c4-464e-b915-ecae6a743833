using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc.Filters;

namespace AS2Test.ErrorHandling
{
    public class ModelValidationFilter : IActionFilter
    {
        public void OnActionExecuted(ActionExecutedContext context)
        {
            
            
        }

        public void OnActionExecuting(ActionExecutingContext context)
        {
           if(!context.ModelState.IsValid)
            {
                var errors = context.ModelState
                .Where(e => e.Value.Errors.Count >  0)
                .Select(e => new
                {
                    Name = e.Key,
                    Message = e.Value.Errors.First().ErrorMessage,
                }).ToList();
                var errorMessages = string.Join("; ", errors.Select(e => $"{e.Name}: {e.Message}"));
                throw new ValidationException(errorMessages);
            }
        }
    }}
