﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureAmModels
{
    [Table("OrderStatusError")]
    public class OrderStatusError
    {
        [Key]
        public int IDOrderError { get; set; }

        [Required]
        public int IDOrder { get; set; }

        public string ExceptionMessage { get; set; }

        public string InnerException { get; set; }

        [Required]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }
    }
}
