using System.Diagnostics;
using System.Security.Cryptography;
using System.Text;
using AS2Test_master.Models;
using MimeKit;

public class MimeMessageBuilder
{
    private readonly MimeMessage _mimeMessage;
    private readonly MessageRequest _messageRequest;

    /// <summary>
    /// Initializes a new instance of the <see cref="MimeMessageBuilder"/> class.
    /// </summary>
    /// <param name="messageRequest">The message request containing the details for the MIME message.</param>
    /// <param name="encryptionService">The encryption service used for encrypting the MIME message.</param>
    /// <exception cref="ArgumentNullException">
    /// Thrown when <paramref name="messageRequest"/> or <paramref name="encryptionService"/> is null.
    /// </exception>
    public MimeMessageBuilder(MessageRequest messageRequest)
    {
        _mimeMessage = new MimeMessage();
        _messageRequest = messageRequest ?? throw new ArgumentNullException(nameof(messageRequest));
    }

    /// <summary>
    /// Adds common headers to the MIME message, such as AS2-From, AS2-To, Subject, Date, and MessageId.
    /// </summary>
    /// <returns>The current instance of <see cref="MimeMessageBuilder"/> for method chaining.</returns>
    public MimeMessageBuilder AddCommonHeaders()
    {
        _mimeMessage.Headers.Remove(HeaderId.From);
        _mimeMessage.Headers.Add("AS2-From", _messageRequest.AsFrom);
        _mimeMessage.Headers.Add("AS2-To", _messageRequest.AsTo);
        _mimeMessage.Subject = _messageRequest.Subject;
        _mimeMessage.Date = DateTimeOffset.Now;
        _mimeMessage.MessageId = _messageRequest.MessageId;
        //_mimeMessage.Headers.Add(HeaderId.ContentTransferEncoding, 
        //_messageRequest.TransferEncoding.ToString().ToLower());

        return this;
    }

    /// <summary>
    /// Adds MDN (Message Disposition Notification) options to the MIME message.
    /// </summary>
    /// <returns>The current instance of <see cref="MimeMessageBuilder"/> for method chaining.</returns>
    public MimeMessageBuilder AddMdnOptions()
    {
        if (!string.IsNullOrEmpty(_messageRequest.MdnOption?.MessageIntegrityCheckAlgorithm.ToString()))
        {
            var mdnHeaderBytes = Encoding.ASCII.GetBytes(
                $"signed-receipt-micalg=optional,{_messageRequest.SigningAlgorithm.ToString().ToLower()}");
            var mdnHeader = Convert.ToBase64String(mdnHeaderBytes);
            //_mimeMessage.Headers.Add(HeaderId.DispositionNotificationOptions, mdnHeader);
        }
        return this;
    }

    /// <summary>
    /// Adds a single content part to the MIME message as an attachment.
    /// </summary>
    /// <returns>The current instance of <see cref="MimeMessageBuilder"/> for method chaining.</returns>
    /// <exception cref="ArgumentException">Thrown when the message content is null or empty.</exception>
    public MimeMessageBuilder AddSingleContent()
    {
        if (string.IsNullOrEmpty(_messageRequest.Message))
        {
            throw new ArgumentException("Message cannot be null or empty");
        }

        var content = new MemoryStream((Convert.FromBase64String(_messageRequest.Message)));
        var attachmentPart = new MimePart("application", "xml")
        {
            IsAttachment = true,
            Content = new MimeContent(content, GetContentEncoding()),
            ContentTransferEncoding = GetContentEncoding(),
            //ContentDisposition = new MimeKit.ContentDisposition($"filename={_messageRequest.FileName}")
        };
        
        attachmentPart.Headers.Remove("Content-Disposition");
        var contentDisposition = new ContentDisposition(ContentDisposition.Attachment);
        contentDisposition.Parameters.Add("filename", _messageRequest.FileName + ".xml");
        attachmentPart.ContentDisposition = contentDisposition;
        _mimeMessage.Body = attachmentPart;

        return this;
    }

    /// <summary>
    /// Adds multiple content parts to the MIME message as a multipart/mixed structure.
    /// </summary>
    /// <returns>The current instance of <see cref="MimeMessageBuilder"/> for method chaining.</returns>
    public MimeMessageBuilder AddMultipartContent()
    {
        var multipartReport = new Multipart("mixed");
        
        foreach (var part in _messageRequest.MimeMessageParts)
        {
            var partContentBytes = Encoding.UTF8.GetBytes(part.Message);
            var partContentStream = new MemoryStream(partContentBytes);

            multipartReport.Add(new MimePart(part.PartType.Key, part.PartType.Value)
            {
                IsAttachment = part.IsAttachment,
                ContentTransferEncoding = GetContentEncoding(),
                Content = new MimeContent(partContentStream, GetContentEncoding()),
                ContentDisposition = new MimeKit.ContentDisposition($"filename={_messageRequest.FileName}")
            });
        }

        _mimeMessage.Body = multipartReport;
        return this;
    }

    private ContentEncoding GetContentEncoding()
    {
        return _messageRequest.TransferEncoding.ToString() switch
        {
            "Base64" => ContentEncoding.Base64,
            "SevenBit" => ContentEncoding.SevenBit,
            "EightBit" => ContentEncoding.EightBit,
            _ => ContentEncoding.Default
        };
    }

    /// <summary>
    /// Builds the MIME message asynchronously by applying security settings such as encryption and signing.
    /// </summary>
    /// <returns>A task that represents the asynchronous operation. The task result contains the built <see cref="MimeMessage"/>.</returns>
    public async Task<MimeMessage> BuildAsync()
    {
        return await ApplySecuritySettings();
    }

    private async Task<MimeMessage> ApplySecuritySettings()
    {
        var result = _mimeMessage;

        // Handle Message Integrity Check
        if (!string.IsNullOrEmpty(_messageRequest.MdnOption?.MessageIntegrityCheckAlgorithm.ToString()))
        {
            await HandleMessageIntegrityCheck(result);
        }

        //// Handle Encryption and Signing
        //if (_messageRequest.IsEncrypted && _messageRequest.IsSigned)
        //{
        //    result = await _encryptionService.SignAndEncryptMime(
        //        result, 
        //        _messageRequest.SigningAlgorithm.Value,
        //        _messageRequest.EncryptionAlgorithm.Value,
        //        _messageRequest);
        //}
        //else if (_messageRequest.IsEncrypted)
        //{
        //    result = await _encryptionService.EncryptMime(
        //        result,
        //        _messageRequest.EncryptionAlgorithm.Value,
        //        _messageRequest);
        //}
        //else if (_messageRequest.IsSigned)
        //{
        //    result = await _encryptionService.SignMime(
        //        result,
        //        _messageRequest.SigningAlgorithm.Value,
        //        _messageRequest);
        //}

        // Save the message to file
        await SaveMessageToFile(result);

        return result;
    }

    private async Task HandleMessageIntegrityCheck(MimeMessage message)
    {
        var hashAlgorithm = CreateHashAlgorithm();
        var messageIntegrityCheck = GenerateMic(message, hashAlgorithm);
      //  _encryptionService.StroreMicInCache(message.MessageId, messageIntegrityCheck);
    }

    private HashAlgorithm CreateHashAlgorithm()
    {
        return _messageRequest.MdnOption.MessageIntegrityCheckAlgorithm.ToString().ToUpper() switch
        {
            "SHA1" => SHA1.Create(),
            "SHA256" => SHA256.Create(),
            "SHA384" => SHA384.Create(),
            "SHA512" => SHA512.Create(),
            "MD5" => MD5.Create(),
            _ => throw new ArgumentException("Unsupported hash algorithm")
        };
    }

    private static string GenerateMic(MimeMessage message, HashAlgorithm hashAlgorithm)
    {
        try
        {
            var messageBytes = System.Text.Encoding.UTF8.GetBytes(message.Body.ToString());
            var hashBytes = hashAlgorithm.ComputeHash(messageBytes);
            return Convert.ToBase64String(hashBytes);
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Error generating MIC: {ex.Message}");
            throw;
        }
    }

    private async Task SaveMessageToFile(MimeMessage message)
    {
        var prefix = DetermineFilePrefix();
       // var path = Path.Combine(".\\Output\\", $"{_messageRequest.MessageId}{prefix}.eml");
       
        await message.WriteToAsync(".\\Output\\ + _messageRequest.MessageId + prefix .eml");
    }

    private string DetermineFilePrefix()
    {
        if (_messageRequest.IsEncrypted && _messageRequest.IsSigned)
            return "_AtachmentSignedEncryptedMimeMessage";
        if (_messageRequest.IsEncrypted)
            return "_AtachmentEncryptedMimeMessage";
        if (_messageRequest.IsSigned)
            return "_AtachmentSignedMimeMessage";
        return string.Empty;
    }
}