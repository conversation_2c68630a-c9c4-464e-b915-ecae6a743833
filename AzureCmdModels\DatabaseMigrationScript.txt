--------------START CMD RouterConfig table 28-05-2025 Joro, Eli--------------------------

CREATE TABLE [dbo].[RouterConfig](
	[IDRouterConfig] [int] IDENTITY(1,1) NOT NULL,
	[IsActive] [bit] NOT NULL,
	[IDCustomer] [int] NOT NULL,
	[ElementName] [nvarchar](255) NULL,
	[ProcessorType] [int] NOT NULL,
	[SourceFolderAccess] [int] NOT NULL,
	[SourceUrl] [nvarchar](255) NULL,
	[SourcePort] [int] NULL,
	[SourceUser] [nvarchar](128) NULL,
	[SourcePassword] [nvarchar](128) NULL,
	[SourceFolder] [nvarchar](255) NULL,
	[TargetFolderAccess] [int] NOT NULL,
	[TargetUrl] [nvarchar](255) NULL,
	[TargetPort] [int] NULL,
	[TargetUser] [nvarchar](128) NULL,
	[TargetPassword] [nvarchar](128) NULL,
	[TargetFolder] [nvarchar](255) NOT NULL,
	[AlternateTargetFolder] [nvarchar](255) NULL,
	[IsFileArchived] [bit] NOT NULL,
	[FileDelay] [int] NOT NULL,
	[TimestampCreated] [datetime] NOT NULL,
	[UserCreated] [nvarchar](128) NOT NULL,
	[TimestampUpdated] [datetime] NULL,
	[UserUpdated] [nvarchar](128) NULL,
 CONSTRAINT [PK_RouterConfig] PRIMARY KEY CLUSTERED 
(
	[IDRouterConfig] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[RouterConfig] ADD  CONSTRAINT [DF_RouterConfig_TimestampCreated]  DEFAULT (getdate()) FOR [TimestampCreated]
GO

ALTER TABLE [dbo].[RouterConfig] ADD  CONSTRAINT [DF_RouterConfig_UserCreated]  DEFAULT (suser_sname()) FOR [UserCreated]
GO



CREATE TABLE [dbo].[RouterElement](
	[IDRouterElement] [int] IDENTITY(1,1) NOT NULL,
	[IDRouterConfig] [int] NOT NULL,
	[Value] [nvarchar](255) NOT NULL,
	[TimestampCreated] [datetime] NOT NULL,
	[UserCreated] [nvarchar](128) NOT NULL,
	[TimestampUpdated] [datetime] NULL,
	[UserUpdated] [nvarchar](128) NULL,
 CONSTRAINT [PK_RouterElement] PRIMARY KEY CLUSTERED 
(
	[IDRouterElement] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[RouterElement]  WITH CHECK ADD  CONSTRAINT [FK_RouterElement_RouterConfig] FOREIGN KEY([IDRouterConfig])
REFERENCES [dbo].[RouterConfig] ([IDRouterConfig])
GO

ALTER TABLE [dbo].[RouterElement] CHECK CONSTRAINT [FK_RouterElement_RouterConfig]
GO
--------------END CMD RouterConfig table 28-05-2025 Joro, Eli--------------------------


--------------START CMD FileWatcherConfig table 29-05-2025 Joro, Eli--------------------------

CREATE TABLE [dbo].[FileWatcherConfig](
	[IDFileWatcherConfig] [int] IDENTITY(1,1) NOT NULL,
	[FolderID] [nvarchar](128) NOT NULL,
	[FolderEnabled] [bit] NOT NULL,
	[FolderDescription] [nvarchar](255) NULL,
	[FolderFilter] [nvarchar](128) NULL,
	[FolderPath] [nvarchar](255) NULL,
	[FolderIncludeSub] [bit] NOT NULL,
	[ExecutableFile] [nvarchar](255) NOT NULL,
	[ExecutableArguments] [nvarchar](500) NULL,
	[TimestampCreated] [datetime] NOT NULL,
	[UserCreated] [nvarchar](128) NULL,
	[TimestampUpdated] [datetime] NULL,
	[UserUpdated] [nvarchar](128) NULL,
 CONSTRAINT [PK_FileWatcherConfig] PRIMARY KEY CLUSTERED 
(
	[IDFileWatcherConfig] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[FileWatcherConfig] ADD  CONSTRAINT [DF_FileWatcherConfig_TimestampCreated]  DEFAULT (getdate()) FOR [TimestampCreated]
GO

ALTER TABLE [dbo].[FileWatcherConfig] ADD  CONSTRAINT [DF_FileWatcherConfig_UserCreated]  DEFAULT (suser_sname()) FOR [UserCreated]
GO
--------------END CMD FileWatcherConfig table 29-05-2025 Joro, Eli--------------------------