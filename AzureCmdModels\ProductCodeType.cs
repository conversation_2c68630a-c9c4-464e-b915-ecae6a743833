﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SGAuditTrail;

namespace AzureCmdModels
{
    [Table("ProductCodeType")]
    [Audit(DisplayName = "ProductCodeType")]
    public partial class ProductCodeType
    {
        public ProductCodeType()
        {
            Products = new HashSet<Product>();
        }

        [Key]
        [StringLength(10)]
        public string CodeType { get; set; }


        [InverseProperty("ProductCodeType")]
        public ICollection<Product> Products { get; set; }
    }
}
