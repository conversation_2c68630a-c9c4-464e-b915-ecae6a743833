﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEbrModels
{
    [Table("TemplateLine")]
    public class TemplateLine
    {
        [Key]
        public int IDTemplateLine { get; set; }

        [Required]
        public int? IDTemplate { get; set; }

        [Required]
        public int? IDLine { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [InverseProperty("TemplateLines")]
        public Template Template { get; set; }

        [InverseProperty("LineTemplates")]
        public Line Line { get; set; }


    }
}
