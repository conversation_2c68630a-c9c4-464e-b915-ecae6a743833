using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using System.Text.Json.Serialization;
using AS2Test.Models;

namespace AS2Test_master.Models
{
    /// <summary>
    /// Represents a message request.
    /// </summary>
    public class MessageRequest
    {
        public MimeTypes MimeType { get; set; }// not used for current implementation
        [Required]
        public bool IsSigned { get; set; } = true;
        public SigningAlgorithms? SigningAlgorithm { get; set; }
        public bool IsEncrypted { get; set; } = true;
        public EncryptionAlgorithms? EncryptionAlgorithm { get; set; }
        public bool IsCompressed { get; set; } = false;// not used for current implementation
        public CompressionAlgorithms? CompressionType { get; set; }// not used for current implementation
        [Required]
        [EnumDataType(typeof(TransferEncodings))]
        public TransferEncodings TransferEncoding { get; set; } = TransferEncodings.Base64;
        public MdnOptions MdnOption { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the message is multipart.
        /// </summary> 
        /// <value>false</value>
        /// <param name="IsMultipart">Indicates whether the message is multipart.</param>
        [Required]
        public bool IsMultipart { get; set; } = false; 

        /// <summary>
        /// Gets or sets the list of MIME message parts.
        /// </summary>
        /// <value>The list of MIME message parts.</value>
        [AllowNull]
        public List<MimeMessagePart> MimeMessageParts { get; set; }// not used for current implementation

        [Required]
        public string AsFrom { get; set; } 

        [Required]
        public string AsTo { get; set; } 

        [Required]
        public string Subject { get; set; } 

        [Required]
        public string Message { get; set; }

        public string MessageId { get; set; }

        [Required]
        public int? IDCustomer { get; set; }

        [Required]
        public int? IDReport { get; set; }

        public string FileName { get; set; } 

        /// <summary>
        /// Gets or sets the content type of the message.
        /// </summary>
        /// <value>The content type of the message.</value>
        public string ContentType { get; set; } // not used for current implementation

        [AllowNull]
        [Base64String]            
        public string SenderCert { get; set; }

        [AllowNull]
        [Base64String]
        public string ReceiverCert { get; set; }

        [Required]
        public Uri ServerUri { get; set; }

        [AllowNull]
        [Base64String]
        public string PrivateCertificatePassword { get; set; }
    }

    public enum EncryptionAlgorithms
    {
        [Display(Name = "3DES")]
        DES3 = 1,

        [Display(Name = "RC2")]
        RC2 = 2,

        [Display(Name = "AES-128")]
        AES128 = 3,

        [Display(Name = "AES-256")]
        AES256 = 4
    }

    /// <summary>
    /// The compression algo to compress the MimeMessage
    /// </summary> 
    /// <param name="CompressionAlgorithms" example="[GZip]"></param> 
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public enum CompressionAlgorithms
    {
        [Display(Name = "GZip")]
        GZip = 1,
        [Display(Name = "Deflate")]
        Deflate = 2
    }
    /// <summary>
    /// Represents the signing algorithms for message requests.
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public enum MessageIntegrityCheckAlgorithms
    {

        [Display(Name = "sha1")]
        SHA1 = 1,
        [Display(Name = "sha56")]
        SHA256 = 2,
        [Display(Name = "sha384")]
        SHA384 = 3,
        [Display(Name = "sha512")]
        SHA512 = 4,
        [Display(Name = "md5")]
        MD5 = 5
    }

    [Flags]
    public enum SigningAlgorithms
    {
        [Display(Name = "SHA1")]
        SHA1 = 1,
        [Display(Name = "SHA256")]
        SHA256 = 2,
        [Display(Name = "SHA384")]
        SHA384 = 3,
        [Display(Name = "SHA512")]
        SHA512 = 4
    }

    /// <summary>
    /// 
    /// </summary>
    [Flags]
    public enum MimeTypes
    {
        [Display(Name = "application/EDI-X12")]
        EDIX12 = 1,
        [Display(Name = "application/EDIFACT")]
        EDIFACT = 2,
        [Display(Name = "application/XML")]
        XML = 3,
        [Display(Name = "application/JSON")]
        JSON = 4,
        [Display(Name = "application/octet-stream")]
        OctetStream = 5
    }

    /// <summary>
    /// The type of encoding
    /// </summary>
    /// <example>Base64</example>
    [Flags]
    public enum TransferEncodings
    {
        [Display(Name = "7bit")]
        SevenBit = 3,
        [Display(Name = "8bit")]
        EightBit = 2,
        [Display(Name = "base64")]
        Base64 = 1,
        [Display(Name = "quoted-printable")]
        QuotedPrintable = 4
    }
}