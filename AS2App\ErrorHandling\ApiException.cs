using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AS2Test.ErrorHandling
{
    public abstract class ApiException : Exception
    {
        public virtual int StatusCode { get; set; } = StatusCodes.Status500InternalServerError;
        public virtual string Type { get; set; } = "Unknown";

        /// <summary>
        /// Initializes a new instance of the <see cref="ApiException"/> class with a specified error message.
        /// </summary>
        /// <param name="message">The message that describes the error.</param>
        protected ApiException(string message) : base(message)
        {
        }
    }
}