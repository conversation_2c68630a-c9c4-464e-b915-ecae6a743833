﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace AzureEbrModels
{


    public enum ExtendPropertyEnum
    {
        STRING,
        INT,
        DECIMAL,
        DATETIME,
        BOOLEAN,
        DATE
    }

    public enum TableValuesEnum
    {
        BATCH,
        SERIALIZATION,
        AGGREGATION
    }

    public enum SerialStateEnum
    {
        ASSIGNED,
        ALLOCATED,
        COMMISSIONED,
        DESTROYED,
        RETURNED,
        SAMPLE,
        UNKNOWN,
        UNASSIGNED
    }

    public enum TemplateLevelFileTypeEnum
    {
        PREVIEW,
        LABEL,
        PRINTER,
        CAMERA,
        DOCUMENT
    }

    public enum AggregationLevelSource
    {
        EBR,
        CMD
    }

    public class StateCounter
    {
        public SerialStateEnum State { get; set; }

        public int Count { get; set; }

        public bool Returned { get; set; }
    }

    public class SerialStateCounter
    {
        public int Order { get; set; }

        public int Count { get; set; }

        public string State { get; set; }
    }

    public class CustomerAggregationLevelView
    {
        public int IDCustomerAggregationLevel { get; set; }

        public string Name { get; set; }

    }

    public class EbrLevels
    {
        public int ID { get; set; }
        public string Name { get; set; }
        public string GTIN { get; set; }
        public string ItemName { get; set; }
        public string SSCCPattern { get; set; }
    }

    public class TemplateLevelFileNoData
    {
        public int IDTemplateLevelFile { get; set; }

        public int? IDTemplate { get; set; }

        public int? IDTemplateLevel { get; set; }

        public string FileName { get; set; }

        public TemplateLevelFileTypeEnum? Type { get; set; }

        public string FileType { get; set; }

        public int? FileSize { get; set; }

        public string FileSizeText
        {
            get
            {
                if (!FileSize.HasValue)
                    return "N/A";
                else if (FileSize.Value < 1024)
                    return $"{FileSize.Value} B";
                else
                    return $"{FileSize.Value / 1024} KB";
            }
        }
    }

    public class AuditTrailItemExport
    {
        public AuditTrailItemExport()
        {

        }

        public AuditTrailItemExport(DateTime timestamp,
                                    int idAuditTrail,
                                    string action,
                                    string displayName,
                                    string oldValue,
                                    string newValue,
                                    string user)
        {
            Timestamp = timestamp;
            Action = action;
            DisplayName = displayName;
            OldValue = oldValue;
            NewValue = newValue;
            User = user;
        }

        public int IDAuditTrail { get; set; }
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime Timestamp { get; set; }
        public string FieldName { get; set; }
        public string Action { get; set; }
        public string OldValue { get; set; }
        public string NewValue { get; set; }
        public string DisplayName { get; set; }
        public string User { get; set; }

    }

    public class UnassignedSerialsListJson
    {
        [JsonPropertyName("serialsList")]
        public List<UnassingedSerialNumber> SerialsList { get; set; }


    }

    public class UnassingedSerialNumber
    {
        [JsonPropertyName("serialNumber")]
        public string SerialNumber { get; set; }

        [JsonPropertyName("requestID")]
        public string RequestID { get; set; }

        [JsonPropertyName("poolCode")]
        public string PoolCode { get; set; }

        [JsonPropertyName("idBatchSerialsRequest")]
        public int IDBatchSerialsRequest { get; set; }
    }


    public class LevelCapacityAndGTIN
    {
        public int Capacity { get; set; }

        public string GTIN { get; set; }

        public short? SSCCExtensionDigit { get; set; }

        public string SSCCCompanyPrefix { get; set; }

        public string DisplayField { get; set; }
    }
}
