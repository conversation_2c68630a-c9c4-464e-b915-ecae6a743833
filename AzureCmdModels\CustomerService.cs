﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureCmdModels
{
    [Table("CustomerService")]
    public partial class CustomerService
    {
        [Key]
        public int IDCustomerService { get; set; }
        public int IDService { get; set; }

        public int IDCustomer { get; set; }

        public bool IsDatabaseCustomerSeparation { get; set; }

        public string DatabaseConnectionString { get; set; }

        [InverseProperty("CustomerServices")]
        public Service Service { get; set; }

        [InverseProperty("CustomerServices")]
        public Customer Customer { get; set; }
    }
}
