﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEbrModels
{
    [Table("LineRequest")]
    public class LineRequest
    {
        [Key]
        public int IDLineRequest { get; set; }

        public int? IDBatchSerialization { get; set; }

        public int? IDBatchAggregation { get; set; }

        [Required]
        public string RequestId { get; set; }

        [Required]
        public int RequestedQuantity { get; set; }

        [Required]
        public int AllocatedQuantity { get; set; }

        public string ResponseID { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [InverseProperty("LineRequests")]
        public BatchSerialization BatchSerialization { get; set; }

        [InverseProperty("LineRequests")]
        public BatchAggregation BatchAggregation { get; set; }
    }
}
