﻿<?xml version="1.0" encoding="utf-8"?>
<Schema Namespace="CloudMasterData.Store" Provider="System.Data.SqlClient" ProviderManifestToken="2012.Azure" Alias="Self" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
  <EntityType Name="AdditionalCodeType">
    <Key>
      <PropertyRef Name="Name" />
    </Key>
    <Property Name="Name" Type="nvarchar" MaxLength="50" Nullable="false" />
  </EntityType>
  <EntityType Name="AuditTrail">
    <Key>
      <PropertyRef Name="IDAuditTrail" />
    </Key>
    <Property Name="IDAuditTrail" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
    <Property Name="Action" Type="nvarchar" MaxLength="100" />
    <Property Name="IDTable" Type="nvarchar" MaxLength="50" />
    <Property Name="TableName" Type="nvarchar" MaxLength="100" />
    <Property Name="Timestamp" Type="datetime" />
    <Property Name="User" Type="nvarchar" MaxLength="128" />
    <Property Name="DisplayName" Type="nvarchar" MaxLength="100" />
    <Property Name="IDTableDetail" Type="nvarchar" MaxLength="50" />
  </EntityType>
  <EntityType Name="AuditTrailItem">
    <Key>
      <PropertyRef Name="IDAuditTrailItem" />
    </Key>
    <Property Name="IDAuditTrailItem" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
    <Property Name="IDAuditTrail" Type="int" Nullable="false" />
    <Property Name="FieldName" Type="nvarchar" MaxLength="100" />
    <Property Name="OldValue" Type="nvarchar(max)" />
    <Property Name="NewValue" Type="nvarchar(max)" />
    <Property Name="DisplayName" Type="nvarchar" MaxLength="100" />
  </EntityType>
  <EntityType Name="BusinessSegment">
    <Key>
      <PropertyRef Name="IDBusinessSegment" />
    </Key>
    <Property Name="IDBusinessSegment" Type="int" Nullable="false" />
    <Property Name="Segment" Type="nvarchar" MaxLength="50" />
  </EntityType>
  <EntityType Name="CommunicationChannel">
    <Key>
      <PropertyRef Name="Type" />
    </Key>
    <Property Name="Type" Type="nvarchar" MaxLength="50" Nullable="false" />
  </EntityType>
  <EntityType Name="Contract">
    <Key>
      <PropertyRef Name="IDContract" />
    </Key>
    <Property Name="IDContract" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
    <Property Name="IDCustomer" Type="int" Nullable="false" />
    <Property Name="Name" Type="nvarchar" MaxLength="100" Nullable="false" />
    <Property Name="StartDate" Type="date" Nullable="false" />
    <Property Name="EndDate" Type="date" Nullable="false" />
    <Property Name="IDContractStatus" Type="int" Nullable="false" />
    <Property Name="IDContractType" Type="int" Nullable="false" />
    <Property Name="MaxUsers" Type="int" Nullable="false" />
    <Property Name="ExpirationWarningDays" Type="int" Nullable="false" />
    <Property Name="SerialNumberCount" Type="int" Nullable="false" />
    <Property Name="ContractNumber" Type="nvarchar" MaxLength="100" />
    <Property Name="TimestampCreated" Type="datetime" Nullable="false" />
    <Property Name="UserCreated" Type="nvarchar" MaxLength="128" Nullable="false" />
    <Property Name="TimestampUpdated" Type="datetime" />
    <Property Name="UserUpdated" Type="nvarchar" MaxLength="128" />
  </EntityType>
  <EntityType Name="ContractStatus">
    <Key>
      <PropertyRef Name="IDContractStatus" />
    </Key>
    <Property Name="IDContractStatus" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
    <Property Name="Name" Type="nvarchar" MaxLength="100" Nullable="false" />
  </EntityType>
  <EntityType Name="ContractType">
    <Key>
      <PropertyRef Name="IDContractType" />
    </Key>
    <Property Name="IDContractType" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
    <Property Name="Name" Type="nvarchar" MaxLength="128" Nullable="false" />
  </EntityType>
  <EntityType Name="Country">
    <Key>
      <PropertyRef Name="Code" />
    </Key>
    <Property Name="Code" Type="char" MaxLength="2" Nullable="false" />
    <Property Name="Name" Type="nvarchar" MaxLength="50" Nullable="false" />
    <Property Name="NameSecLng" Type="nvarchar" MaxLength="50" />
    <Property Name="GS1RNCode" Type="int" />
  </EntityType>
  <EntityType Name="Customer">
    <Key>
      <PropertyRef Name="IDCustomer" />
    </Key>
    <Property Name="IDCustomer" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
    <Property Name="Name" Type="nvarchar" MaxLength="255" />
    <Property Name="TimestampCreated" Type="datetime" Nullable="false" />
    <Property Name="UserCreated" Type="nvarchar" MaxLength="128" Nullable="false" />
    <Property Name="TimestampUpdated" Type="datetime" />
    <Property Name="UserUpdated" Type="nvarchar" MaxLength="128" />
    <Property Name="GS1CompanyPrefix" Type="varchar" MaxLength="100" />
    <Property Name="SGLN" Type="varchar" MaxLength="20" />
    <Property Name="GLN" Type="varchar" MaxLength="13" />
    <Property Name="CustomerStreet" Type="nvarchar" MaxLength="255" />
    <Property Name="CustomerStreet2" Type="nvarchar" MaxLength="255" />
    <Property Name="CustomerCity" Type="nvarchar" MaxLength="255" />
    <Property Name="CustomerPostCode" Type="nvarchar" MaxLength="30" />
    <Property Name="CustomerCountryCode" Type="char" MaxLength="2" />
    <Property Name="Code" Type="nvarchar" MaxLength="50" />
    <Property Name="IDBusinessSegment" Type="int" Nullable="false" />
  </EntityType>
  <EntityType Name="CustomerAggregationLevel">
    <Key>
      <PropertyRef Name="IDCustomerAggregationLevel" />
    </Key>
    <Property Name="IDCustomerAggregationLevel" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
    <Property Name="IDCustomer" Type="int" Nullable="false" />
    <Property Name="IDProduct" Type="int" />
    <Property Name="Name" Type="nvarchar" MaxLength="20" Nullable="false" />
    <Property Name="Type" Type="nvarchar" MaxLength="20" Nullable="false" />
    <Property Name="SerialType" Type="nvarchar" MaxLength="10" Nullable="false" />
    <Property Name="UnitCapacity" Type="int" />
    <Property Name="SubLevel" Type="nvarchar" MaxLength="20" Nullable="false" />
    <Property Name="SubLevelItemType" Type="nvarchar" MaxLength="20" Nullable="false" />
    <Property Name="TimestampCreated" Type="datetime" Nullable="false" />
    <Property Name="UserCreated" Type="nvarchar" MaxLength="128" Nullable="false" />
    <Property Name="TimestampUpdated" Type="datetime" />
    <Property Name="UserUpdated" Type="nvarchar" MaxLength="128" />
  </EntityType>
  <EntityType Name="CustomerContract">
    <Key>
      <PropertyRef Name="IDCustomerContract" />
    </Key>
    <Property Name="IDCustomerContract" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
    <Property Name="IDCustomer" Type="int" Nullable="false" />
    <Property Name="Name" Type="nvarchar" MaxLength="100" Nullable="false" />
    <Property Name="StartDate" Type="date" Nullable="false" />
    <Property Name="EndDate" Type="date" />
    <Property Name="IsTrial" Type="bit" Nullable="false" />
    <Property Name="TrialEndDate" Type="datetime" />
    <Property Name="IsContract" Type="bit" Nullable="false" />
    <Property Name="ContractTerm" Type="date" />
    <Property Name="ContractData" Type="nvarchar" MaxLength="100" />
    <Property Name="ContractNotifyEndDays" Type="int" Nullable="false" />
    <Property Name="IsPayed" Type="bit" Nullable="false" />
    <Property Name="PaymentTerm" Type="date" />
    <Property Name="PaymentNotifyDays" Type="int" Nullable="false" />
    <Property Name="InvoiceData" Type="nvarchar" MaxLength="100" />
    <Property Name="IsCanceled" Type="bit" Nullable="false" />
    <Property Name="CancelReason" Type="nvarchar" MaxLength="200" />
    <Property Name="TimestampCreated" Type="datetime" Nullable="false" />
    <Property Name="UserCreated" Type="nvarchar" MaxLength="128" Nullable="false" />
    <Property Name="TimestampUpdated" Type="datetime" />
    <Property Name="UserUpdated" Type="nvarchar" MaxLength="128" />
  </EntityType>
  <EntityType Name="CustomerLabelDataSource">
    <Key>
      <PropertyRef Name="IDCustomerDataSource" />
    </Key>
    <Property Name="IDCustomerDataSource" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
    <Property Name="IDCustomer" Type="int" Nullable="false" />
    <Property Name="Name" Type="nvarchar" MaxLength="100" Nullable="false" />
    <Property Name="DataSource" Type="nvarchar(max)" />
    <Property Name="TimestampCreated" Type="datetime" Nullable="false" />
    <Property Name="UserCreated" Type="nvarchar" MaxLength="128" Nullable="false" />
    <Property Name="TimestampUpdated" Type="datetime" />
    <Property Name="UserUpdated" Type="nvarchar" MaxLength="128" />
    <Property Name="SrcSystemName" Type="nvarchar" MaxLength="200" />
    <Property Name="SrcSystem" Type="nvarchar" MaxLength="2" />
    <Property Name="Unit" Type="nvarchar" MaxLength="100" />
    <Property Name="SerialType" Type="nvarchar" MaxLength="20" />
    <Property Name="Item" Type="nvarchar" MaxLength="100" />
    <Property Name="ItemSerialType" Type="nvarchar" MaxLength="20" />
  </EntityType>
  <EntityType Name="CustomerPartner">
    <Key>
      <PropertyRef Name="IDCustomerPartner" />
    </Key>
    <Property Name="IDCustomerPartner" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
    <Property Name="IDCustomer" Type="int" Nullable="false" />
    <Property Name="Name" Type="nvarchar" MaxLength="255" Nullable="false" />
    <Property Name="SystemName" Type="nvarchar" MaxLength="255" Nullable="false" />
    <Property Name="SNXRequest" Type="nvarchar" MaxLength="50" />
    <Property Name="SSCCRequest" Type="nvarchar" MaxLength="50" />
    <Property Name="EBRImport" Type="nvarchar" MaxLength="50" />
    <Property Name="BatchReport" Type="nvarchar" MaxLength="50" />
    <Property Name="ShipmentReport" Type="nvarchar" MaxLength="50" />
    <Property Name="IsActive" Type="bit" Nullable="false" />
  </EntityType>
  <EntityType Name="CustomerPartnerParameter">
    <Key>
      <PropertyRef Name="IDCustomerPartnerParameter" />
    </Key>
    <Property Name="IDCustomerPartnerParameter" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
    <Property Name="IDCustomerPartner" Type="int" Nullable="false" />
    <Property Name="Name" Type="nvarchar" MaxLength="255" Nullable="false" />
    <Property Name="Value" Type="nvarchar" MaxLength="255" Nullable="false" />
    <Property Name="TimestampCreated" Type="datetime" Nullable="false" />
    <Property Name="UserCreated" Type="nvarchar" MaxLength="128" Nullable="false" />
  </EntityType>
  <EntityType Name="CustomerService">
    <Key>
      <PropertyRef Name="IDCustomerService" />
    </Key>
    <Property Name="IDCustomerService" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
    <Property Name="IDCustomer" Type="int" Nullable="false" />
    <Property Name="IDService" Type="int" Nullable="false" />
    <Property Name="IsDatabaseCustomerSeparation" Type="bit" Nullable="false" />
    <Property Name="DatabaseConnectionString" Type="nvarchar(max)" />
  </EntityType>
  <EntityType Name="InternalCodeType">
    <Key>
      <PropertyRef Name="Name" />
    </Key>
    <Property Name="Name" Type="nvarchar" MaxLength="50" Nullable="false" />
  </EntityType>
  <EntityType Name="LabelTemplate">
    <Key>
      <PropertyRef Name="IDLabelTemplate" />
    </Key>
    <Property Name="IDLabelTemplate" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
    <Property Name="IDCustomer" Type="int" Nullable="false" />
    <Property Name="Unit" Type="nvarchar" MaxLength="50" />
    <Property Name="Label" Type="nvarchar(max)" Nullable="false" />
    <Property Name="LabelName" Type="nvarchar" MaxLength="255" Nullable="false" />
    <Property Name="TimestampCreated" Type="datetime" Nullable="false" />
    <Property Name="UserCreated" Type="nvarchar" MaxLength="128" Nullable="false" />
  </EntityType>
  <EntityType Name="MahType">
    <Key>
      <PropertyRef Name="IDMahType" />
    </Key>
    <Property Name="IDMahType" Type="int" Nullable="false" />
    <Property Name="Type" Type="nvarchar" MaxLength="50" />
  </EntityType>
  <EntityType Name="Manufacturer">
    <Key>
      <PropertyRef Name="IDMah" />
    </Key>
    <Property Name="IDMah" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
    <Property Name="IDCustomer" Type="int" Nullable="false" />
    <Property Name="IDMahType" Type="int" />
    <Property Name="MahID" Type="nvarchar" MaxLength="50" />
    <Property Name="MahName" Type="nvarchar" MaxLength="100" Nullable="false" />
    <Property Name="MahStreet" Type="nvarchar" MaxLength="255" />
    <Property Name="MahStreet2" Type="nvarchar" MaxLength="255" />
    <Property Name="MahCity" Type="nvarchar" MaxLength="255" />
    <Property Name="MahPostCode" Type="nvarchar" MaxLength="30" />
    <Property Name="MahCountryCode" Type="char" MaxLength="2" />
    <Property Name="TimeStampCreated" Type="datetime" Nullable="false" />
    <Property Name="UserCreated" Type="nvarchar" MaxLength="128" Nullable="false" />
    <Property Name="TimeStampUpdated" Type="datetime" />
    <Property Name="UserUpdated" Type="nvarchar" MaxLength="128" />
    <Property Name="GS1CompanyPrefix" Type="varchar" MaxLength="100" />
    <Property Name="GLN" Type="varchar" MaxLength="13" />
    <Property Name="SGLN" Type="varchar" MaxLength="20" />
    <Property Name="IsActive" Type="bit" Nullable="false" />
  </EntityType>
  <EntityType Name="Product">
    <Key>
      <PropertyRef Name="IDProduct" />
    </Key>
    <Property Name="IDProduct" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
    <Property Name="IDServiceMasterRecord" Type="int" Nullable="false" />
    <Property Name="IDCustomer" Type="int" Nullable="false" />
    <Property Name="CodeType" Type="nvarchar" MaxLength="10" Nullable="false" />
    <Property Name="Code" Type="nvarchar" MaxLength="14" Nullable="false" />
    <Property Name="InternalCodeType" Type="nvarchar" MaxLength="50" />
    <Property Name="InternalCode" Type="nvarchar" MaxLength="50" />
    <Property Name="Name" Type="nvarchar" MaxLength="255" Nullable="false" />
    <Property Name="CommonName" Type="nvarchar" MaxLength="255" />
    <Property Name="Form" Type="nvarchar" MaxLength="100" />
    <Property Name="PackType" Type="nvarchar" MaxLength="50" />
    <Property Name="PackSize" Type="int" />
    <Property Name="Strength" Type="nvarchar" MaxLength="100" />
    <Property Name="IsActive" Type="bit" Nullable="false" />
    <Property Name="TimestampCreated" Type="datetime" Nullable="false" />
    <Property Name="UserCreated" Type="nvarchar" MaxLength="128" Nullable="false" />
    <Property Name="TimestampUpdated" Type="datetime" />
    <Property Name="UserUpdated" Type="nvarchar" MaxLength="128" />
    <Property Name="SerialNumberSourceType" Type="nvarchar" MaxLength="50" />
    <Property Name="SerializationType" Type="varchar" MaxLength="128" />
    <Property Name="IDCustomerPartner" Type="int" />
  </EntityType>
  <EntityType Name="ProductAdditionalCode">
    <Key>
      <PropertyRef Name="IDProductAdditionalCode" />
    </Key>
    <Property Name="IDProductAdditionalCode" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
    <Property Name="IDProduct" Type="int" Nullable="false" />
    <Property Name="CodeType" Type="nvarchar" MaxLength="50" Nullable="false" />
    <Property Name="Code" Type="nvarchar" MaxLength="50" Nullable="false" />
    <Property Name="UserCreated" Type="nvarchar" MaxLength="128" Nullable="false" />
    <Property Name="TimestampCreated" Type="datetime" Nullable="false" />
    <Property Name="UserUpdated" Type="nvarchar" MaxLength="128" />
    <Property Name="TimestampUpdated" Type="datetime" />
  </EntityType>
  <EntityType Name="ProductCodeType">
    <Key>
      <PropertyRef Name="CodeType" />
    </Key>
    <Property Name="CodeType" Type="nvarchar" MaxLength="10" Nullable="false" />
    <Property Name="Length" Type="int" />
    <Property Name="Regex" Type="nvarchar" MaxLength="128" />
  </EntityType>
  <EntityType Name="ProductLabel">
    <Key>
      <PropertyRef Name="IDProductLabel" />
    </Key>
    <Property Name="IDProductLabel" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
    <Property Name="IDCustomer" Type="int" Nullable="false" />
    <Property Name="IDProduct" Type="int" Nullable="false" />
    <Property Name="LabelName" Type="nvarchar" MaxLength="255" Nullable="false" />
    <Property Name="Label" Type="nvarchar(max)" Nullable="false" />
    <Property Name="Level" Type="nvarchar" MaxLength="20" Nullable="false" />
    <Property Name="IsDefault" Type="bit" Nullable="false" />
    <Property Name="IDTargetMarket" Type="int" />
    <Property Name="TimestampCreated" Type="datetime" Nullable="false" />
    <Property Name="UserCreated" Type="nvarchar" MaxLength="128" Nullable="false" />
    <Property Name="PrintingSrcSystem" Type="nvarchar" MaxLength="2" />
    <Property Name="PrintingSrcSystemName" Type="nvarchar" MaxLength="100" />
  </EntityType>
  <EntityType Name="SerializationType">
    <Key>
      <PropertyRef Name="IDSerializationType" />
    </Key>
    <Property Name="IDSerializationType" Type="nvarchar" MaxLength="30" Nullable="false" />
    <Property Name="SerializationType" Type="nvarchar" MaxLength="50" Nullable="false" />
    <Property Name="Regex" Type="nvarchar" MaxLength="250" />
    <Property Name="OrderDisplayList" Type="int" />
    <Property Name="IsActive" Type="bit" Nullable="false" />
    <Property Name="IsCarton" Type="bit" Nullable="false" />
    <Property Name="IsCase" Type="bit" Nullable="false" />
    <Property Name="IsPallet" Type="bit" Nullable="false" />
    <Property Name="IsSerialsExpected" Type="bit" />
  </EntityType>
  <EntityType Name="SerialNumberSourceType">
    <Key>
      <PropertyRef Name="IDSerialNumberSourceType" />
    </Key>
    <Property Name="IDSerialNumberSourceType" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
    <Property Name="IDService" Type="int" Nullable="false" />
    <Property Name="Code" Type="varchar" MaxLength="50" Nullable="false" />
    <Property Name="Name" Type="varchar" MaxLength="50" />
  </EntityType>
  <EntityType Name="Service">
    <Key>
      <PropertyRef Name="IDService" />
    </Key>
    <Property Name="IDService" Type="int" Nullable="false" />
    <Property Name="Name" Type="varchar" MaxLength="255" Nullable="false" />
    <Property Name="SrcSystem" Type="nvarchar" MaxLength="2" />
    <Property Name="SerializationType" Type="nvarchar" MaxLength="30" />
    <Property Name="IsCMD" Type="bit" Nullable="false" />
    <Property Name="IsServiceOwnProduct" Type="bit" Nullable="false" />
    <Property Name="IsServiceOwnCustomer" Type="bit" Nullable="false" />
    <Property Name="ADURI" Type="nvarchar" MaxLength="255" />
    <Property Name="ProductUpdateSql" Type="nvarchar" MaxLength="255" />
    <Property Name="CustomerUpdateSql" Type="nvarchar" MaxLength="255" />
    <Property Name="IsServiceOwnManufacturer" Type="bit" Nullable="false" />
    <Property Name="ManufacturerUpdateSql" Type="nvarchar" MaxLength="255" />
    <Property Name="UserUpdateSql" Type="nvarchar" MaxLength="255" />
    <Property Name="IsExternalCodeAccepted" Type="bit" Nullable="false" />
    <Property Name="ProductRegex" Type="nvarchar" MaxLength="500" />
    <Property Name="IsUserDeleteAllowed" Type="bit" Nullable="false" />
    <Property Name="IsServiceOwnLabel" Type="bit" Nullable="false" />
  </EntityType>
  <EntityType Name="ServiceContract">
    <Key>
      <PropertyRef Name="IDServiceContract" />
    </Key>
    <Property Name="IDServiceContract" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
    <Property Name="IDContract" Type="int" Nullable="false" />
    <Property Name="IDService" Type="int" Nullable="false" />
  </EntityType>
  <EntityType Name="ServiceEnvironment">
    <Key>
      <PropertyRef Name="IDServiceEnvironment" />
    </Key>
    <Property Name="IDServiceEnvironment" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
    <Property Name="IDService" Type="int" Nullable="false" />
    <Property Name="Name" Type="varchar" MaxLength="255" />
  </EntityType>
  <EntityType Name="ServiceRole">
    <Key>
      <PropertyRef Name="IDServiceRole" />
    </Key>
    <Property Name="IDServiceRole" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
    <Property Name="RoleName" Type="nvarchar" MaxLength="128" Nullable="false" />
    <Property Name="GroupName" Type="nvarchar" MaxLength="128" />
  </EntityType>
  <EntityType Name="ServiceUserLogin">
    <Key>
      <PropertyRef Name="IDServiceUserLogin" />
    </Key>
    <Property Name="IDServiceUserLogin" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
    <Property Name="IDService" Type="int" Nullable="false" />
    <Property Name="LgnName" Type="nvarchar" MaxLength="50" Nullable="false" />
    <Property Name="ExternalSenderCode" Type="nvarchar" MaxLength="250" />
    <Property Name="ExternalRecieverCode" Type="nvarchar" MaxLength="250" />
    <Property Name="IDManufacturer" Type="int" />
  </EntityType>
  <EntityType Name="TargetMarket">
    <Key>
      <PropertyRef Name="IDTargetMarket" />
    </Key>
    <Property Name="IDTargetMarket" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
    <Property Name="Name" Type="nvarchar" MaxLength="128" Nullable="false" />
    <Property Name="ShortName" Type="nvarchar" MaxLength="4" Nullable="false" />
    <Property Name="GS1RNCode" Type="int" />
    <Property Name="TimestampCreated" Type="datetime" Nullable="false" />
    <Property Name="UserCreated" Type="nvarchar" MaxLength="128" Nullable="false" />
    <Property Name="TimestampUpdated" Type="datetime" />
    <Property Name="UserUpdated" Type="nvarchar" MaxLength="128" />
    <Property Name="SerializationType" Type="nvarchar" MaxLength="50" />
    <Property Name="SrcSystem" Type="nvarchar" MaxLength="2" />
  </EntityType>
  <EntityType Name="UserLogin">
    <Key>
      <PropertyRef Name="LgnName" />
    </Key>
    <Property Name="LgnName" Type="nvarchar" MaxLength="50" Nullable="false" />
    <Property Name="IDCustomer" Type="int" Nullable="false" />
    <Property Name="IDManufacturer" Type="int" />
    <Property Name="IsADUser" Type="bit" Nullable="false" />
    <Property Name="TimestampCreated" Type="datetime" Nullable="false" />
    <Property Name="UserCreated" Type="nvarchar" MaxLength="128" Nullable="false" />
    <Property Name="TimestampUpdated" Type="datetime" />
    <Property Name="UserUpdated" Type="nvarchar" MaxLength="128" />
  </EntityType>
  <EntityType Name="UserLoginServiceRole">
    <Key>
      <PropertyRef Name="IDUserLoginServiceRole" />
    </Key>
    <Property Name="IDUserLoginServiceRole" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
    <Property Name="LgnName" Type="nvarchar" MaxLength="50" Nullable="false" />
    <Property Name="IDServiceRole" Type="int" Nullable="false" />
  </EntityType>
  <EntityType Name="UserPrinterSetting">
    <Key>
      <PropertyRef Name="LgnName" />
    </Key>
    <Property Name="LgnName" Type="nvarchar" MaxLength="50" Nullable="false" />
    <Property Name="UseNetworkPrinter" Type="bit" Nullable="false" />
    <Property Name="NetworkPrinterIP" Type="nvarchar" MaxLength="20" />
    <Property Name="NetworkPrinterPort" Type="int" />
    <Property Name="PrinterDriverName" Type="nvarchar" MaxLength="250" />
    <Property Name="PrinterLanguageType" Type="nvarchar" MaxLength="8" />
    <Property Name="TimestampCreated" Type="datetime" Nullable="false" />
    <Property Name="TimestampUpdated" Type="datetime" />
    <Property Name="UserCreated" Type="nvarchar" MaxLength="50" />
    <Property Name="UserUpdated" Type="nvarchar" MaxLength="50" />
  </EntityType>
  <Association Name="FK_CustomerAggregationLevel_Customer">
    <End Role="Customer" Type="Self.Customer" Multiplicity="1" />
    <End Role="CustomerAggregationLevel" Type="Self.CustomerAggregationLevel" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="Customer">
        <PropertyRef Name="IDCustomer" />
      </Principal>
      <Dependent Role="CustomerAggregationLevel">
        <PropertyRef Name="IDCustomer" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Association Name="FK_CustomerAggregationLevel_Product">
    <End Role="Product" Type="Self.Product" Multiplicity="0..1" />
    <End Role="CustomerAggregationLevel" Type="Self.CustomerAggregationLevel" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="Product">
        <PropertyRef Name="IDProduct" />
      </Principal>
      <Dependent Role="CustomerAggregationLevel">
        <PropertyRef Name="IDProduct" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Association Name="FK_ProductLabel_Customer">
    <End Role="Customer" Type="Self.Customer" Multiplicity="1" />
    <End Role="ProductLabel" Type="Self.ProductLabel" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="Customer">
        <PropertyRef Name="IDCustomer" />
      </Principal>
      <Dependent Role="ProductLabel">
        <PropertyRef Name="IDCustomer" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Association Name="FK_ProductLabel_Product">
    <End Role="Product" Type="Self.Product" Multiplicity="1" />
    <End Role="ProductLabel" Type="Self.ProductLabel" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="Product">
        <PropertyRef Name="IDProduct" />
      </Principal>
      <Dependent Role="ProductLabel">
        <PropertyRef Name="IDProduct" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Association Name="FK_ProductLabel_TargetMarket">
    <End Role="TargetMarket" Type="Self.TargetMarket" Multiplicity="0..1" />
    <End Role="ProductLabel" Type="Self.ProductLabel" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="TargetMarket">
        <PropertyRef Name="IDTargetMarket" />
      </Principal>
      <Dependent Role="ProductLabel">
        <PropertyRef Name="IDTargetMarket" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <EntityContainer Name="CloudMasterDataStoreContainer">
    <EntitySet Name="AdditionalCodeType" EntityType="Self.AdditionalCodeType" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="AuditTrail" EntityType="Self.AuditTrail" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="AuditTrailItem" EntityType="Self.AuditTrailItem" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="BusinessSegment" EntityType="Self.BusinessSegment" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="CommunicationChannel" EntityType="Self.CommunicationChannel" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="Contract" EntityType="Self.Contract" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="ContractStatus" EntityType="Self.ContractStatus" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="ContractType" EntityType="Self.ContractType" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="Country" EntityType="Self.Country" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="Customer" EntityType="Self.Customer" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="CustomerAggregationLevel" EntityType="Self.CustomerAggregationLevel" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="CustomerContract" EntityType="Self.CustomerContract" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="CustomerLabelDataSource" EntityType="Self.CustomerLabelDataSource" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="CustomerPartner" EntityType="Self.CustomerPartner" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="CustomerPartnerParameter" EntityType="Self.CustomerPartnerParameter" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="CustomerService" EntityType="Self.CustomerService" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="InternalCodeType" EntityType="Self.InternalCodeType" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="LabelTemplate" EntityType="Self.LabelTemplate" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="MahType" EntityType="Self.MahType" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="Manufacturer" EntityType="Self.Manufacturer" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="Product" EntityType="Self.Product" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="ProductAdditionalCode" EntityType="Self.ProductAdditionalCode" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="ProductCodeType" EntityType="Self.ProductCodeType" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="ProductLabel" EntityType="Self.ProductLabel" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="SerializationType" EntityType="Self.SerializationType" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="SerialNumberSourceType" EntityType="Self.SerialNumberSourceType" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="Service" EntityType="Self.Service" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="ServiceContract" EntityType="Self.ServiceContract" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="ServiceEnvironment" EntityType="Self.ServiceEnvironment" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="ServiceRole" EntityType="Self.ServiceRole" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="ServiceUserLogin" EntityType="Self.ServiceUserLogin" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="TargetMarket" EntityType="Self.TargetMarket" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="UserLogin" EntityType="Self.UserLogin" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="UserLoginServiceRole" EntityType="Self.UserLoginServiceRole" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="UserPrinterSetting" EntityType="Self.UserPrinterSetting" Schema="dbo" store:Type="Tables" />
    <AssociationSet Name="FK_CustomerAggregationLevel_Customer" Association="Self.FK_CustomerAggregationLevel_Customer">
      <End Role="Customer" EntitySet="Customer" />
      <End Role="CustomerAggregationLevel" EntitySet="CustomerAggregationLevel" />
    </AssociationSet>
    <AssociationSet Name="FK_CustomerAggregationLevel_Product" Association="Self.FK_CustomerAggregationLevel_Product">
      <End Role="Product" EntitySet="Product" />
      <End Role="CustomerAggregationLevel" EntitySet="CustomerAggregationLevel" />
    </AssociationSet>
    <AssociationSet Name="FK_ProductLabel_Customer" Association="Self.FK_ProductLabel_Customer">
      <End Role="Customer" EntitySet="Customer" />
      <End Role="ProductLabel" EntitySet="ProductLabel" />
    </AssociationSet>
    <AssociationSet Name="FK_ProductLabel_Product" Association="Self.FK_ProductLabel_Product">
      <End Role="Product" EntitySet="Product" />
      <End Role="ProductLabel" EntitySet="ProductLabel" />
    </AssociationSet>
    <AssociationSet Name="FK_ProductLabel_TargetMarket" Association="Self.FK_ProductLabel_TargetMarket">
      <End Role="TargetMarket" EntitySet="TargetMarket" />
      <End Role="ProductLabel" EntitySet="ProductLabel" />
    </AssociationSet>
  </EntityContainer>
</Schema>