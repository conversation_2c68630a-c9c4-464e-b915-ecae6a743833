﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SGAuditTrail;

namespace AzureCmdModels
{
    [Table("SerialNumberSourceType")]
    [Audit(DisplayName = "SerialNumberSourceType")]
    public partial class SerialNumberSourceType
    {

        [Key]
        public int IDSerialNumberSourceType { get; set; }

        public int IDService { get; set; }

        [Required]
        [StringLength(50)]
        public string Code { get; set; }

        [Required]
        [StringLength(50)]
        public string Name { get; set; }

        [InverseProperty("SerialNumberSourceType")]
        public ICollection<Service> Services { get; set; }
    }
}
