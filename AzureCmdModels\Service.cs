﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SGAuditTrail;

namespace AzureCmdModels
{
    [Table("Service")]
    [Audit(DisplayName = "Service")]
    public class Service
    {
        public Service()
        {
            ServiceEnvironments = new HashSet<ServiceEnvironment>();
            CustomerServices = new HashSet<CustomerService>();
            ServiceContracts = new HashSet<ServiceContract>();
            Products = new HashSet<Product>();
            ServiceUserLogins = new HashSet<ServiceUserLogin>();
        }

        [Key]
        public int IDService { get; set; }

        [StringLength(255)]
        [Audit(DisplayName = "Name")]
        public string Name { get; set; }

        [StringLength(2)]
        public string SrcSystem { get; set; }

        [StringLength(30)]
        public string SerializationType { get; set; }

        public bool IsCMD { get; set; }

        public bool IsServiceOwnProduct { get; set; }

        public bool IsServiceOwnCustomer { get; set; }

        public bool IsServiceOwnManufacturer { get; set; }

        public bool IsExternalCodeAccepted { get; set; }

        public bool IsUserDeleteAllowed { get; set; }

        public bool IsServiceOwnLabel { get; set; }

        [StringLength(255)]
        public string ADURI { get; set; }

        [StringLength(255)]
        public string ProductUpdateSql { get; set; }

        [StringLength(255)]
        public string ManufacturerUpdateSql { get; set; }

        [StringLength(255)]
        public string CustomerUpdateSql { get; set; }

        [StringLength(255)]
        public string UserUpdateSql { get; set; }

        [StringLength(500)]
        public string ProductRegex { get; set; }

        [InverseProperty("Services")]
        public SerialNumberSourceType SerialNumberSourceType { get; set; }


        [InverseProperty("Service")]
        public ICollection<CustomerService> CustomerServices { get; set; }


        [InverseProperty("Service")]
        public ICollection<ServiceEnvironment> ServiceEnvironments { get; set; }

        [InverseProperty("Service")]
        public ICollection<ServiceContract> ServiceContracts { get; set; }

        [InverseProperty("Service")]
        public ICollection<Product> Products { get; set; }

        [InverseProperty("Service")]
        public ICollection<ServiceUserLogin> ServiceUserLogins { get; set; }

    }
}
