﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEbrModels
{
    [Table("Template")]
    public class Template
    {
        public Template()
        {
            TemplateExtendProperties = new HashSet<TemplateExtendProperty>();
            TemplateLevels = new HashSet<TemplateLevel>();
            TemplateLines = new HashSet<TemplateLine>();
            TemplateSrcSystems = new HashSet<TemplateSrcSystem>();
            TemplateTargetMarkets = new HashSet<TemplateTargetMarket>();
            TemplateLevelFiles = new HashSet<TemplateLevelFile>();
        }

        [Key]
        public int IDTemplate { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        public bool IsActive { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        [InverseProperty("Template")]
        public ICollection<TemplateExtendProperty> TemplateExtendProperties { get; set; }

        [InverseProperty("Template")]
        public ICollection<TemplateLevel> TemplateLevels { get; set; }

        [InverseProperty("Template")]
        public ICollection<TemplateLine> TemplateLines { get; set; }

        [InverseProperty("Template")]
        public ICollection<TemplateSrcSystem> TemplateSrcSystems { get; set; }

        [InverseProperty("Template")]
        public ICollection<TemplateTargetMarket> TemplateTargetMarkets { get; set; }

        [InverseProperty("Template")]
        public ICollection<TemplateLevelFile> TemplateLevelFiles { get; set; }

        public override bool Equals(object obj)
        {
            if (obj == null)
                return false;

            if (obj.GetType() == this.GetType())
            {
                if (((Template)obj).IDTemplate == this.IDTemplate)
                    return true;
                else
                    return false;
            }
            else
                return false;

        }

        public string LineList
        {
            get
            {
                try
                {
                    if (TemplateLines != null && TemplateLines.Count > 0)
                    {
                        if (TemplateLines.FirstOrDefault().Line != null)
                            return string.Join(", ", TemplateLines.Select(l => l.Line.Code).ToList());
                        else
                            return string.Empty;
                    }
                    else
                        return string.Empty;

                }
                catch (Exception e)
                {
                    return $"Error. {e.Message} {e.InnerException?.Message}";
                }
            }
        }

        public string TargetMarketList
        {
            get
            {
                try
                {
                    if (TemplateTargetMarkets != null && TemplateTargetMarkets.Count > 0)
                    {
                        return string.Join(", ", TemplateTargetMarkets.Select(tm => tm.CountryCode).ToList());
                    }
                    else
                        return string.Empty;

                }
                catch (Exception e)
                {
                    return $"Error. {e.Message} {e.InnerException?.Message}";
                }
            }
        }

       
    }
}
