﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureBhModels
{
    [Table("ValidationStep")]
    public class ValidationStep
    {
        [Key]
        public int IDValidationStep { get; set; }

        [Required]
        [StringLength(50)]
        public string Name { get; set; }

        [Required]
        [StringLength(50)]
        public string MethodName { get; set; }

        [Required]
        public bool IsActiveImport { get; set; }

        [Required]
        public bool IsActiveReport { get; set; }
    }
}
