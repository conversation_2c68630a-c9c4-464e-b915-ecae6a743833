﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEbrModels
{

    public static class ProductNumber
    {
        /// <summary>
        /// GTIN, PPN or MATNO code utility.
        /// parameter type = GTIN, PPN or MATNO
        /// code = code
        /// </summary>
        public static bool Check(string type, string code)
        {
            try
            {
                if (string.IsNullOrEmpty(type) || string.IsNullOrEmpty(code))
                    return false;

                if (type == "PPN")
                    return PPN.Check(code);
                else if (type == "GTIN")
                    return GTIN.Check(code);
                else if (type == "MATNO")
                    return MATNO.Check(code);
                else if (type == "PCODE")
                    return PCODE.Check(code);
                else
                    return false;

            }
            catch (Exception)
            {
                return false;
            }
        }


        /// <summary>
        /// GTIN, PPN or MATNO code utility.
        /// Parameter code = code to be checked. 
        /// Result GTIN or PPN string
        /// </summary>
        public static string GetCodeType(string code)
        {
            if (GTIN.Check(code))
                return "GTIN";
            else if (PPN.Check(code))
                return "PPN";
            else if (MATNO.Check(code))
                return "MATNO";
            else if (PCODE.Check(code))
                return "PCODE";
            else
                throw new Exception("Error! Unknown code type or invalid check sum.");

        }

        /// <summary>
        /// GTIN code utility.
        /// </summary>
        public static class GTIN
        {

            /// <summary>
            /// return GTIN14 code.
            /// </summary>
            /// <param name="gtin">GTIN code to be converted</param>
            /// <returns>
            /// GTIN14 code
            /// </returns>
            public static string ToGTIN14(string code)
            {
                return ("00000000000000" + code).Substring(code.Length);

            }

            /// <summary>
            /// Check for correct checksum in GTIN code.
            /// </summary>
            /// <param name="gtin">GTIN code to be verrified</param>
            /// <returns>
            /// True or false if code is or not correct.
            /// </returns>
            public static bool Check(string gtin)
            {
                try
                {
                    List<int> GTINSizes = new List<int> { 8, 12, 13, 14 };
                    if (GTINSizes.Contains(gtin.Length))
                    {
                        int check;
                        int sum = 0;
                        int mult = 3;
                        for (int i = gtin.Length - 2; i >= 0; i--)
                        {
                            int x = Int32.Parse(gtin[i].ToString());
                            sum += x * mult;
                            if (mult == 3) mult = 1;
                            else mult = 3;
                        }
                        if (sum < 10)
                        {
                            if (sum == 0)
                                check = 0;
                            else
                                check = 10 - sum;
                        }
                        else
                        {
                            check = 10 - (sum % 10);
                            if (check == 10)
                                check = 0;
                        }
                        if (check.ToString() == gtin[gtin.Length - 1].ToString())
                            return true;
                        else
                            return false;
                    }
                    return false;
                }
                catch (Exception ex)
                {
                    return false;
                }
            }

            /// <summary>
            /// Calculate correct checksum GTIN code.
            /// </summary>
            /// <param name="gtin">GTIN code without checksum. Accepeted sizes - 7, 11, 12 or 13 simbols </param>
            /// <param name="gtinWithCheckDigit" >Output parameter - GTIN code with checksum</param>
            /// <returns>
            /// True or false if calculation is or not correct.
            /// </returns>
            public static bool GetCheckDigit(string gtin, out string gtinWithCheckDigit)
            {
                try
                {
                    List<int> GTINSizes = new List<int> { 7, 11, 12, 13 };
                    if (GTINSizes.Contains(gtin.Length))
                    {
                        int check;
                        int sum = 0;
                        int mult = 3;
                        for (int i = gtin.Length - 1; i >= 0; i--)
                        {
                            int x = Int32.Parse(gtin[i].ToString());
                            sum += x * mult;
                            if (mult == 3) mult = 1;
                            else mult = 3;
                        }
                        if (sum < 10)
                        {
                            if (sum == 0)
                                check = 0;
                            else
                                check = 10 - sum;
                        }
                        else
                        {
                            check = 10 - (sum % 10);
                            if (check == 10)
                                check = 0;
                        }
                        gtinWithCheckDigit = gtin + check.ToString();
                        return true;
                    }
                    gtinWithCheckDigit = "";
                    return false;
                }
                catch (Exception)
                {
                    gtinWithCheckDigit = "";
                    return false;
                }
            }

        }
    }

    /// <summary>
    /// PPN code utility.
    /// </summary>
    public static class PPN
    {
        /// <summary>
        /// PPN code utility.
        /// parameter code = string(12), code to be checked
        /// result true or false depend of checksum recalculating
        /// </summary>
        public static bool Check(string code)
        {
            try
            {
                if (code.Trim().Length == 12)
                {
                    string checksum = code.Substring(10, 2);
                    byte[] bytes = Encoding.ASCII.GetBytes(code);
                    int sum = (int)bytes[0] * 2
                        + (int)bytes[1] * 3
                        + (int)bytes[2] * 4
                        + (int)bytes[3] * 5
                        + (int)bytes[4] * 6
                        + (int)bytes[5] * 7
                        + (int)bytes[6] * 8
                        + (int)bytes[7] * 9
                        + (int)bytes[8] * 10
                        + (int)bytes[9] * 11;

                    int intCheckSum = sum % 97;
                    string strCheckSum = intCheckSum.ToString("00");
                    return checksum == strCheckSum;
                }
                else
                {
                    return false;
                }
            }
            catch (Exception)
            {
                return false;
            }
        }
    }

    public static class MATNO
    {
        public static bool Check(string code)
        {
            try
            {
                if (long.TryParse(code, out long res))
                    return true;
                else
                    return false;
            }
            catch (Exception e)
            {
                var debug = e.Message;
                return false;
            }
        }
    }


    public static class PCODE
    {
        public static bool Check(string code)
        {
            try
            {
                if (!string.IsNullOrEmpty(code) && code == code.Trim())
                    return true;
                else
                    return false;
            }
            catch (Exception e)
            {
                var debug = e.Message;
                return false;
            }
        }
    }
}
