﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SGAuditTrail;

namespace AzureCmdModels
{
    [Table("UserLoginServiceRole")]
    [Audit(DisplayName = "User Login Service Role")]
    public class UserLoginServiceRole
    {
        [Key]
        public int IDUserLoginServiceRole { get; set; }

        [Required]
        [StringLength(50)]
        [Audit(DisplayName = "UserLogin", InversePropertyName = "UserLogin", InversePropertyValue = "LgnName")]
        public string LgnName { get; set; }

        [Audit(DisplayName = "ServiceRole", InversePropertyName = "ServiceRole", InversePropertyValue = "RoleName")]
        public int IDServiceRole { get; set; }

        [InverseProperty("UserLoginServiceRoles")]
        public UserLogin UserLogin { get; set; }

        [InverseProperty("UserLoginServiceRoles")]
        public ServiceRole ServiceRole { get; set; }
    }
}
