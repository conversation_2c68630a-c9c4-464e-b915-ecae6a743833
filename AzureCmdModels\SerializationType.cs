﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureCmdModels
{
    [Table("SerializationType")]
    public class SerializationTypes
    {
        [Key]
        [Required]
        [StringLength(30)]
        public string IDSerializationType { get; set; }

        [StringLength(50)]
        public string SerializationType { get; set; }

        [StringLength(250)]
        public string Regex { get; set; }

        public int? OrderDisplayList { get; set; }

        public bool IsActive { get; set; }

        public bool IsCarton { get; set; }

        public bool IsCase { get; set; }

        public bool IsPallet { get; set; }

        public bool? IsSerialsExpected { get; set; }
    }
}
