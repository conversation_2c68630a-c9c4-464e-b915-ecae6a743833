using AS2Test_master.Models;
using MimeKit;

namespace AS2Test_master.Features.Services.MimePayload
{
    public interface IMimePayloadService
    {
        Task<MimeMessage> CreateMultipartMessage(MessageRequest messageRequest);
        Task<MimeMessage> CreateAtachmentMime(MessageRequest messageRequest);
        Task<MimeMessage> CreateMimeContent(MessageRequest message, string fileName, TransferEncodings transferEncoding);
        //  MimeEntity CreateMimeEntity(MimeEntity mimeEntity, string message, string fileName);
        Task<MimeMessage> CreateMimeMessage(MessageRequest messageRequest, string fileName, string transferEncoding);
        Task<MimeMessage> CreateXmlAttachment(MessageRequest messageRequest, string filename,
                  TransferEncodings transferEncodings);

    }
}