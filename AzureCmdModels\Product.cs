﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SGAuditTrail;
using System.Text.Json.Serialization;
using static AzureCmdModels.ProductNumber;

namespace AzureCmdModels
{
    [Table("Product")]
    [Audit(DisplayName = "Product")]
    public class Product
    {

        public Product()
        {
            ProductAdditionalCodes = new HashSet<ProductAdditionalCode>();
            ProductAggregationLevels = new HashSet<CustomerAggregationLevel>();
            ProductLabels = new HashSet<ProductLabel>();

        }

        public Product(CustomerAggregationLevel customerAggregationLevel)
        {
            ProductAdditionalCodes = new HashSet<ProductAdditionalCode>();
            ProductAggregationLevels = new HashSet<CustomerAggregationLevel>();

            IDProduct = 0 - customerAggregationLevel.IDCustomerAggregationLevel;
            IDServiceMasterRecord = customerAggregationLevel.Product.IDServiceMasterRecord;
            IDCustomer = customerAggregationLevel.IDCustomer.Value;
            Code = customerAggregationLevel.GTIN;
            CodeType = "GTIN";
            InternalCodeType = null;
            InternalCode = null;
            Name = $"{customerAggregationLevel.Name} of {customerAggregationLevel.Product.Name}";
            CommonName = customerAggregationLevel.Name;
            Form = customerAggregationLevel.Name;
            PackType = customerAggregationLevel.Type;
            PackSize = customerAggregationLevel.UnitCapacity.HasValue ? customerAggregationLevel.UnitCapacity.Value : 0;
            Strength = null;
            IsActive = customerAggregationLevel.IsActive;
            TimestampCreated = customerAggregationLevel.TimestampCreated;
            UserCreated = customerAggregationLevel.UserCreated;
            TimestampUpdated = customerAggregationLevel.TimestampUpdated;
            UserUpdated = customerAggregationLevel.UserUpdated;
            SerialNumberSourceType = customerAggregationLevel.Product.SerialNumberSourceType;
            SerializationType = customerAggregationLevel.Product.SerializationType;
            IDCustomerPartner = customerAggregationLevel.Product.IDCustomerPartner;
            Service = customerAggregationLevel.Product.Service;
        }

        [Key]
        public int IDProduct { get; set; }

        [DisplayName("Customer")]
        public int IDCustomer { get; set; }

        [DisplayName("Service Master Record")]
        [Audit(DisplayName = "Service Master Record", InversePropertyName = "Service", InversePropertyValue = "Name")]
        [Range(1, int.MaxValue, ErrorMessage = "Select service")]
        public int IDServiceMasterRecord { get; set; }

        [DisplayName("Code Type")]
        [Required]
        [StringLength(10)]
        [Audit(DisplayName = "Code Type")]
        public string CodeType { get; set; }

        [Required]
        [StringLength(14)]
        [Audit(DisplayName = "Code")]
        public string Code { get; set; }


        [DisplayName("Internal Code Type")]
        [StringLength(50)]
        [Audit(DisplayName = "Internal Code Type")]
        public string InternalCodeType { get; set; }

        [DisplayName("Internal Code")]
        [StringLength(50)]
        [Audit(DisplayName = "Internal Code")]
        public string InternalCode { get; set; }

        [Required]
        [StringLength(255)]
        [Audit(DisplayName = "Product Name")]
        public string Name { get; set; }

        [Required]
        [DisplayName("Common Name")]
        [StringLength(255)]
        [Audit(DisplayName = "Common name")]
        public string CommonName { get; set; }

        [DisplayName("Form")]
        [StringLength(100)]
        [Audit(DisplayName = "Form")]
        public string Form { get; set; }

        [StringLength(100)]
        [DisplayName("Strength")]
        [Audit(DisplayName = "Strength")]
        public string Strength { get; set; }

        [DisplayName("Pack Size")]
        [Range(1, int.MaxValue, ErrorMessage = "The field Pack Size is out of range.")]
        [Audit(DisplayName = "Pack Size")]
        public int PackSize { get; set; }

        [StringLength(50)]
        [DisplayName("Pack Type")]
        [Audit(DisplayName = "Pack Type")]
        public string PackType { get; set; }

        [Audit(DisplayName = "Serial Number Source Type")]
        [DisplayName("Serial Number Source Type")]
        [StringLength(50)]
        public string SerialNumberSourceType { get; set; }

        [Audit(DisplayName = "Serialization Type")]
        [DisplayName("Serialization Type")]
        [StringLength(128)]
        public string SerializationType { get; set; }

        [DisplayName("Partner")]
        [Audit(DisplayName = "Partner", InversePropertyName = "CustomerPartner", InversePropertyValue = "Name")]
        public int? IDCustomerPartner { get; set; }

        [Required]
        [DisplayName("Timestamp created")]
        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [DisplayName("User created")]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [DisplayName("Timestamp updated")]
        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [DisplayName("User updated")]
        [StringLength(128)]
        public string UserUpdated { get; set; }

        [InverseProperty("Products")]
        public Customer Customer { get; set; }

        [InverseProperty("Products")]
        public Service Service { get; set; }

        [InverseProperty("Products")]
        public ProductCodeType ProductCodeType { get; set; }


        [InverseProperty("Product")]
        public ICollection<ProductAdditionalCode> ProductAdditionalCodes { get; set; }

        [InverseProperty("Products")]
        public CustomerPartner CustomerPartner { get; set; }

        [InverseProperty("Product")]
        public ICollection<CustomerAggregationLevel> ProductAggregationLevels { get; set; }

        [InverseProperty("Product")]
        public ICollection<ProductLabel> ProductLabels { get; set; }

        [DisplayName("Active")]
        [Audit(DisplayName = "Active", ValueTrue = "Yes", ValueFalse = "No")]
        public bool IsActive { get; set; }

        [NotMapped]
        public bool IsCodeValid
        {
            get
            {
                if (string.IsNullOrEmpty(this.CodeType))
                    return false;

                if (string.IsNullOrEmpty(this.Code))
                    return false;

                try
                {
                    if (this.CodeType == "GTIN")
                        return GTIN.Check(Code);
                    else if (this.CodeType == "PPN")
                        return PPN.Check(Code);
                    else if (this.CodeType == "MATNO")
                        return MATNO.Check(Code);
                    else if (this.CodeType == "PCODE")
                        return PCODE.Check(Code);
                    else
                        return false;
                }
                catch (Exception e)
                {
                    var debug = e.Message;
                    return false;
                }
            }
        }

        public AuditTrailHeader GetAuditTrailHeader(string user, int page, int pages)
        {
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

            AuditTrailHeader header = new AuditTrailHeader()
            {
                Controler = "Products",
                ID = IDProduct.ToString(),
                UserName = user,
                Page = page,
                Pages = pages
            };

            header.AddItem("Product", Name);
            header.AddItem("User", user);
            header.AddItem("Timestamp", timestamp);

            return header;
        }
        public override bool Equals(object obj)
        {
            if (obj == null)
                return false;

            if (obj.GetType() == this.GetType())
            {
                if (((Product)obj).IDProduct == this.IDProduct)
                    return true;
                else
                    return false;
            }
            else
                return false;
        }

    }
}