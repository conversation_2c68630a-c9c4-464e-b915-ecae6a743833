﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEbrModels
{
    [Table("BatchTemplateLevelFile")]
    public class BatchTemplateLevelFile
    {
        [Key]
        public int IDBatchTemplateLevelFile { get; set; }

        public int? IDBatch { get; set; }

        public int? IDBatchSerialization { get; set; }

        public int? IDBatchAggregation { get; set; }

        public int IDTemplateLevelFile { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }


        [InverseProperty("BatchTemplateLevelFiles")]
        public Batch Batch { get; set; }

        [InverseProperty("BatchTemplateLevelFiles")]
        public BatchSerialization BatchSerialization { get; set; }

        [InverseProperty("BatchTemplateLevelFiles")]
        public BatchAggregation BatchAggregation { get; set; }

        [InverseProperty("BatchTemplateLevelFiles")]
        public TemplateLevelFile TemplateLevelFile { get; set; }
    }
}
