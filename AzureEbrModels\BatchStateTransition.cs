﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEbrModels
{
    [Table("BatchStateTransition")]
    public class BatchStateTransition
    {
        public BatchStateTransition()
        {

        }

        [Key]
        public int IDBatchStateTransition { get; set; }

        [Required]
        [Range(1, int.MaxValue, ErrorMessage = "Serial state initial selection required")]
        public int IDBatchStateInitial { get; set; }

        [Required]
        [Range(1, int.MaxValue, ErrorMessage = "Serial state target selection required")]
        public int IDBatchStateTarget { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        [InverseProperty("BatchStateTransitionsInitial")]
        public BatchState BatchStateInitial { get; set; }

        [InverseProperty("BatchStateTransitionsTarget")]
        public BatchState BatchStateTarget { get; set; }
    }
}
