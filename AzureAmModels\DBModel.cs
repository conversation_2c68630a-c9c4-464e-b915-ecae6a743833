﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection.Emit;
using System.Text;
using System.Threading.Tasks;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace AzureAmModels
{
    public class DBModel : DbContext
    {
        public virtual DbSet<AppLog> AppLogs { get; set; }
        public virtual DbSet<AppLogItem> AppLogItems { get; set; }
        public virtual DbSet<AuditTrail> AuditTrails { get; set; }
        public virtual DbSet<Batch> Batches { get; set; }
        public virtual DbSet<BatchEventType> BatchEventTypes { get; set; }
        public virtual DbSet<BatchEvent> BatchEvents { get; set; }
        public virtual DbSet<BatchEventCode> BatchEventCodes { get; set; }
        public virtual DbSet<BatchEventStatus> BatchEventStatuses { get; set; }
        public virtual DbSet<BatchEventAggregation> BatchEventAggregations { get; set; }
        public virtual DbSet<BatchEventAggregationItem> BatchEventAggregationItems { get; set; }
        public virtual DbSet<NotAggregatedSerial> NotAggregatedSerials { get; set; }
        public virtual DbSet<Setting> Settings { get; set; }
        public virtual DbSet<ValidationStep> ValidationSteps { get; set; }
        public virtual DbSet<VerificationError> VerificationErrors { get; set; }
        public virtual DbSet<Order> Orders { get; set; }
        public virtual DbSet<OrderSerial> OrderSerials { get; set; }
        public virtual DbSet<OrderCryptoCode> OrderCryptoCodes { get; set; }
        public virtual DbSet<OrderStatusError> OrderStatusErrors { get; set; }
        public virtual DbSet<OrderHistory> OrdersHistory { get; set; }
        public virtual DbSet<BufferHistory> BuffersHistory { get; set; }
        public virtual DbSet<PoolInfo> PoolInfo { get; set; }


        private readonly string _connectionString;

        public DBModel(string connectionString)
        {
            _connectionString = connectionString;
            Database.SetCommandTimeout(600);
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_connectionString);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Batch>(entity =>
            {
                entity.Property(b => b.ExpiryDate).HasColumnType("datetime");
                entity.Property(b => b.ManufacturingDate).HasColumnType("datetime");
            });


            modelBuilder.Entity<AuditTrail>(entity =>
            {
                entity.Property(at => at.TimestampCreated).HasColumnType("datetime");
            });


            modelBuilder.Entity<BatchEvent>(entity =>
            {
                entity.HasOne(e => e.Batch)
                    .WithMany(b => b.BatchEvents)
                    .HasForeignKey(e => e.IDBatch)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_BatchEvent_Batch");

                entity.HasOne(e => e.BatchEventType)
                   .WithMany(t => t.BatchEvents)
                   .HasForeignKey(e => e.IDBatchEventType)
                   .OnDelete(DeleteBehavior.ClientSetNull)
                   .HasConstraintName("FK_BatchEvent_BatchEventType");

                entity.Property(e => e.TimestampCreated).HasColumnType("datetime");
                entity.Property(e => e.TimestampUpdated).HasColumnType("datetime");
            });


            modelBuilder.Entity<BatchEventCode>(entity =>
            {
                entity.HasOne(c => c.BatchEvent)
                    .WithMany(e => e.BatchEventCodes)
                    .HasForeignKey(e => e.IDBatchEvent)
                    .HasPrincipalKey(e => e.IDBatchEvent)
                    .OnDelete(DeleteBehavior.ClientSetNull);

                entity.Property(e => e.TimestampCreated).HasColumnType("datetime");
            });

            modelBuilder.Entity<BatchEventAggregation>(entity =>
            {
                entity.HasOne(a => a.BatchEvent)
                    .WithMany(e => e.BatchEventAggregations)
                    .HasForeignKey(a => a.IDBatchEvent)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_BatchEventAggreagtion_BatchEvent");

                entity.Property(e => e.TimestampCreated).HasColumnType("datetime");
                entity.Property(e => e.TimestampUpdated).HasColumnType("datetime");
                entity.Property(e => e.TimestampProduced).HasColumnType("datetime");
            });

            modelBuilder.Entity<BatchEventAggregationItem>(entity =>
            {
                entity.HasOne(i => i.BatchEventAggregation)
                    .WithMany(a => a.BatchEventAggregationItems)
                    .HasForeignKey(i => i.IDBatchEventAggregation)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_BatchEventAggregationItem_BatchEventAggreagtion");

                entity.Property(e => e.TimestampCreated).HasColumnType("datetime");

            });

            modelBuilder.Entity<BatchEventStatus>(entity =>
            {
                entity.HasOne(s => s.BatchEvent)
                    .WithMany(e => e.BatchEventStatuses)
                    .HasForeignKey(b => b.IDBatchEvent)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_BatchEventStatus_BatchEvent");

                entity.Property(e => e.TimestampCreated).HasColumnType("datetime");
            });



            modelBuilder.Entity<NotAggregatedSerial>(entity =>
            {
                entity.HasOne(nas => nas.Batch)
                    .WithMany(b => b.NotAggregatedSerials)
                    .HasForeignKey(nas => nas.IDBatch)
                    .OnDelete(DeleteBehavior.Restrict)
                    .HasConstraintName("FK_NotAggregatedSerial_Batch");

                entity.Property(e => e.TimestampCreated).HasColumnType("datetime");
            });

            modelBuilder.Entity<OrderCryptoCode>(entity =>
            {
                entity.HasOne(c => c.Order)
                    .WithMany(o => o.OrderProductCryptoCodes)
                    .HasForeignKey(c => c.IDOrder)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_OrderProductCryptoCode_Order");

                entity.Property(e => e.TimestampCreated).HasColumnType("datetime");
            });

            modelBuilder.Entity<OrderSerial>(entity =>
            {
                entity.HasOne(s => s.Order)
                    .WithMany(o => o.Serials)
                    .HasForeignKey(s => s.IDOrder)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_OrderSerial_Order");

            });

            modelBuilder.Entity<BufferHistory>(entity =>
            {
                entity.HasOne(b => b.OrderHistory)
                    .WithMany(o => o.BuffersHistory)
                    .HasForeignKey(b => b.IDOrderHistory)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_BufferHistory_OrderHistory");

                entity.Property(e => e.TimestampCreated).HasColumnType("datetime");
            });

            modelBuilder.Entity<OrderHistory>(entity =>
            {
                entity.HasOne(o => o.Order)
                    .WithMany(h => h.OrdersHistory)
                    .HasForeignKey(b => b.IDOrder)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_OrderHistory_Order");

                entity.Property(e => e.TimestampCreated).HasColumnType("datetime");
            });

            modelBuilder.Entity<PoolInfo>(entity =>
            {
                entity.HasOne(c => c.BufferHistory)
                    .WithMany(o => o.PoolInfos)
                    .HasForeignKey(c => c.IDBufferHistory)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_PoolInfo_BufferHistory");

                entity.Property(e => e.TimestampCreated).HasColumnType("datetime");
            });
        }
    }
}
