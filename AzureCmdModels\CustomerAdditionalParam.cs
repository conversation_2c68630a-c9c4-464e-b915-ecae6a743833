﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureCmdModels
{
    [Table("CustomerAdditionalParam")]
    public class CustomerAdditionalParam
    {
        [Key]
        public int IDCustomerAdditionalParam { get; set; }

        public int IDAdditionalParam { get; set; }

        public int IDCustomer { get; set; }

        [StringLength(128)]
        [Required]
        public string ParamType { get; set; }

        [StringLength(int.MaxValue)]
        public string? Regex { get; set; }

        [StringLength(int.MaxValue)]
        public string? Sql { get; set; }

        [StringLength(int.MaxValue)]
        public string? DefaultValue { get; set; }

        public bool IsReadOnly { get; set; }

        public bool IsOptional { get; set; }

        public bool IsHidden { get; set; }

        [InverseProperty("CustomerAdditionalParams")]
        public AdditionalParam AdditionalParam { get; set; }

    }

}
