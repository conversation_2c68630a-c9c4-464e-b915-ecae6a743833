﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SGAuditTrail;

namespace AzureCmdModels
{
    [Table("InternalCodeType")]
    [Audit(DisplayName = "Internal Code Type")]
    public class InternalCodeType
    {
        [Required]
        [StringLength(255)]
        [Audit(DisplayName = "Name")]
        [Key]
        public string Name { get; set; }
    }
}
