﻿using AS2App.Models.ConfigModels;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using System.Diagnostics;
using System.Security.Cryptography;

namespace AS2App.Features.Services.MicHandler
{
    public class MicHandlerService : IMicHandlerService
    {
        private readonly ILogger<MicHandlerService> _logger;
        private readonly IOptionsMonitor<AS2Settings> _optionsMonitor;
        private readonly IMemoryCache _memoryCache;

        public MicHandlerService(ILogger<MicHandlerService> logger, IOptionsMonitor<AS2Settings> optionsMonitor, IMemoryCache memoryCache)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _optionsMonitor = optionsMonitor ?? throw new ArgumentNullException(nameof(optionsMonitor));
            _memoryCache = memoryCache ?? throw new ArgumentNullException(nameof(memoryCache));

        }

        public bool ValidateMic(string mic, string messageID)
        {
            try 
            {
                if (string.IsNullOrWhiteSpace(mic) || string.IsNullOrWhiteSpace(messageID))
                {
                    _logger.LogWarning("Attempted to validate MIC with invalid mic or messageId.");
                    return false;
                }

                if (!_memoryCache.TryGetValue(messageID, out string storedMic))
                {
                    _logger.LogWarning("No MIC found in cache for message ID {MessageId}", messageID);
                    return false;
                }
                bool isValid = string.Equals(storedMic, mic, StringComparison.OrdinalIgnoreCase);


                return isValid;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating MIC for message ID {MessageId}", messageID);
                return false;
            }
        }

        public void GenerateSaveMic(byte[] byteMessage, string messageId, string integrityCheckAlgorithm)
        {
            //var byteMessagee = await message.ReadAsByteArrayAsync();

            var hashAlgorithm = CreateHashAlgorithm(integrityCheckAlgorithm);
            var messageIntegrityCheck = GenerateMic(byteMessage, hashAlgorithm);

            _logger.LogWarning("Mic {messageIntegrityCheck}", messageIntegrityCheck);

            StroreMicInCache(messageId, messageIntegrityCheck);
        }



        private HashAlgorithm CreateHashAlgorithm(string integrityCheckAlgorithm)
        {
            return integrityCheckAlgorithm switch
            {
                "SHA1" => SHA1.Create(),
                "SHA256" => SHA256.Create(),
                "SHA384" => SHA384.Create(),
                "SHA512" => SHA512.Create(),
                "MD5" => MD5.Create(),
                _ => throw new ArgumentException("Unsupported hash algorithm")
            };
        }

        private static string GenerateMic(byte[] message, HashAlgorithm hashAlgorithm)
        {
            try
            {
                var hashBytes = hashAlgorithm.ComputeHash(message);
                return Convert.ToBase64String(hashBytes);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error generating MIC: {ex.Message}");
                throw;
            }
        }

        public void StroreMicInCache(string messageId, string mic)

        {
            if (string.IsNullOrWhiteSpace(messageId) || string.IsNullOrWhiteSpace(mic))
            {
                _logger.LogWarning("Attempted to store MIC with invalid messageId or mic.");
                return;
            }
            double timeout = _optionsMonitor.CurrentValue.MicExpireTime;

            var options = new MemoryCacheEntryOptions()
                .SetAbsoluteExpiration(TimeSpan.FromMinutes(timeout))
                .SetPriority(CacheItemPriority.Normal)
                .SetSize(1024);

            _memoryCache.Set($"<{messageId}>", mic, options);
        }
    }
}
