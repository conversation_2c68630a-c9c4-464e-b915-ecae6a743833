﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SGAuditTrail;

namespace AzureCmdModels
{
    [Table("ContractStatus")]
    [Audit(DisplayName = "Contract Status")]
    public partial class ContractStatus
    {

        public ContractStatus()
        {
            Contracts = new HashSet<Contract>();
        }
        [Key]
        public int IDContractStatus { get; set; }

        [DisplayName("Status")]
        public string Name { get; set; }

        [InverseProperty("ContractStatus")]
        public ICollection<Contract> Contracts { get; set; }

    }
}
