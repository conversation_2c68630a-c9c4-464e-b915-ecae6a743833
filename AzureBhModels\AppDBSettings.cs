﻿using System;

namespace AzureBhModels
{
    public static class AppDBSettings
    {
        public const string API_KEY = "API_KEY";
        public const string USERNAME = "USERNAME";
        public const string PASSWORD = "PASSWORD";
        public const string URL = "URL";

        public const string IS_AUTO_PUBLISH = "IS_AUTO_PUBLISH";
        public const string AUTO_PUBLISH_LEVEL = "AUTO_PUBLISH_LEVEL";

        public const string FOLDER_FILE_LOG = "FOLDER_FILE_LOG";
        public const string FOLDER_FILE_IMPORT = "FOLDER_FILE_IMPORT";
        public const string STORAGE_SYSTEM = "STORAGE_SYSTEM";
        public const string AZURE_STORAGE_CONNECTION_STRING = "AZURE_STORAGE_CONNECTION_STRING";

        public const string AUTHORITY_PERMIT_TYPE = "AUTHORITY_PERMIT_TYPE";

        public const string COMPANY_PREFIX_LIST = "COMPANY_PREFIX_LIST";

        public const string IS_LOGISTICS_VISIBLE = "IS_LOGISTICS_VISIBLE";

        public const string MAX_REPORT_QUANTITY = "MaxReportQuantity";
    }
}
