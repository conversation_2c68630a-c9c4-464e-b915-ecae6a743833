﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureBhModels
{
    [Table("BatchAggregationItem")]
    public class BatchAggregationItem
    {
        [Key]
        public long IDBatchAggregationItem { get; set; }

        [Required]
        public long IDBatchAggregation { get; set; }

        [StringLength(50)]
        public string SerialType { get; set; }

        [StringLength(14)]
        public string Code { get; set; }


        [Required]
        [StringLength(100)]
        public string SerialNumber { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        public string UserCreated { get; set; }

        [InverseProperty("BatchAggregationItems")]
        public BatchAggregation BatchAggregation { get; set; }
    }

    public class AggregationItemForBulkInsert
    {
        public long IDBatchAggregation { get; set; }

        public string SerialType { get; set; }

        public string Code { get; set; }

        public string Value { get; set; }

        public DateTime Timestamp { get; set; }

        public string LgnName { get; set; }
    }
}
