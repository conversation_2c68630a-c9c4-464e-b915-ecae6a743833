using MimeKit;
using MimeKit.Cryptography;


public enum As2MessageType
{
    // Regular Message Types
    Plain,
    Signed,
    Encrypted,
    EncryptedAndSigned,
    
    // MDN Types (Message Disposition Notifications)
    MdnPlain,                    // Regular MDN without security
    MdnSigned,                   // Signed MDN
    MdnAsync,                    // Asynchronous MDN
    MdnSync,                     // Synchronous MDN
    
    // Compressed variants
    Compressed,                  // Just compressed
    CompressedAndSigned,         // Compressed and then signed
    CompressedAndEncrypted,      // Compressed and then encrypted
    CompressedSignedAndEncrypted, // Compressed, signed, and then encrypted
    
    // Error/Invalid Types
    Invalid,                     // Message format is invalid
    Unknown                      // Message type cannot be determined
}

public class As2MessageTypeDetector
{
    //public As2MessageType DetermineMessageType(MimeMessage message, IDictionary<string, string> headers)
    public As2MessageType DetermineMessageType(MimeMessage message)
    {
        var headersDict = message.Headers.ToDictionary(h => h.Field, h => h.Value);
        // Check if it's an MDN first
        foreach(var part in message.BodyParts)
        {
            part.Headers.ToDictionary(h => h.Field, h => h.Value);
        }
        //var headersDict = message.BodyParts.Headers.ToDictionary(h => h.Field, h => h.Value);
        // Check if it's an MDN first
        if (IsMdn(message, headersDict))
        {
            return DetermineMdnType(message, headersDict);
        }

        // Check compression
        bool isCompressed = IsCompressed(message);
        
        // Check encryption and signature
        bool isEncrypted = IsEncrypted(message);
        bool isSigned = IsSigned(message);

        if (isCompressed)
        {
            if (isEncrypted && isSigned)
                return As2MessageType.CompressedSignedAndEncrypted;
            if (isEncrypted)
                return As2MessageType.CompressedAndEncrypted;
            if (isSigned)
                return As2MessageType.CompressedAndSigned;
            return As2MessageType.Compressed;
        }

        if (isEncrypted && isSigned)
            return As2MessageType.EncryptedAndSigned;
        if (isEncrypted)
            return As2MessageType.Encrypted;
        if (isSigned)
            return As2MessageType.Signed;
        if (IsValid(message))
            return As2MessageType.Plain;

        return As2MessageType.Invalid;
    }

    private bool IsMdn(MimeMessage message, IDictionary<string, string> headers)
    {
        // Check headers for MDN indicators
        return headers.ContainsKey("Disposition-Notification-To") ||
               headers.ContainsKey("Message-ID") && headers["Message-ID"].Contains("MDN");
    }

    private As2MessageType DetermineMdnType(MimeMessage message, IDictionary<string, string> headers)
    {
        bool isAsync = headers.ContainsKey("Receipt-Delivery-Option");
        bool isSigned = IsSigned(message);

        if (isAsync)
        {
            if (isSigned)
                return As2MessageType.MdnSigned;
            return As2MessageType.MdnAsync;
        }
        else
        {
            if (isSigned)
                return As2MessageType.MdnSigned;
            return As2MessageType.MdnSync;
        }
    }

    private bool IsCompressed(MimeMessage message)
    {
        // Check for compression (usually indicated by Content-Type: application/pkcs7-mime; smimetype=compressed-data)
        var pkcs7 = message.Body as ApplicationPkcs7Mime;
        return pkcs7?.SecureMimeType == SecureMimeType.CompressedData ||
               message.Headers.Any(h => h.Field == "Content-Type" && h.Value.Contains("application/x-pkcs7-mime; smimetype=compressed-data"));
    }

    private bool IsEncrypted(MimeMessage message)
    {
        var pkcs7 = message.Body as ApplicationPkcs7Mime;
        return message.Body is MultipartEncrypted ||
               (pkcs7?.SecureMimeType == SecureMimeType.EnvelopedData);
    }

    private bool IsSigned(MimeMessage message)
    {
        var pkcs7 = message.Body as ApplicationPkcs7Mime;
        return message.Body is MultipartSigned ||
               (pkcs7?.SecureMimeType == SecureMimeType.SignedData);
    }

    private bool IsValid(MimeMessage message)
    {
        // Basic validation of message format
        return message.Body != null;
    }
}