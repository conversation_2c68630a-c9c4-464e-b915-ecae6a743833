﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureAmModels
{
    public class AzureModel
    {
        public string Instance { get; set; }
        public string ClientId { get; set; }
        public string ClientSecret { get; set; }
        public string Domain { get; set; }
        public string TenantId { get; set; }
    }

    public enum ExtendPropertyEnum
    {
        STRING,
        INT,
        DECIMAL,
        DATETIME,
        BOOLEAN,
        DATE,
        BIGINT
    }

    public enum TableValuesEnum
    {
        BATCH,
        SERIALIZATION,
        AGGREGATION,
        DEAGGREGATION
    }
    public enum ReportStateEnum
    {
        DRAFT,
        REPORTED,
        FAILED,
        CANCELLED
    }

    public static class AuditTrailConstants
    {
        public enum ActionEnum
        {
            CREATE,
            UPDATE,
            DELETE,
            APPEND,
            REMOVE,
            ADD
        }

        public enum TableEnum
        {
            BATCH,
            REPORT,
            APP_LOG,
            PRODUCT,
            MAH,
            TARGET_MARKET,
            SETTING,
            COUNTRY,
            VALIDATION,
            ORDER
        }

        public enum ScopeEnum
        {
            Batch,
            Report,
            App_Log,
            Event,
            TargetMarket,
            Wholesaler,
            Setting,
            Country,
            Mah,
            Validation,
            Support,
            AlternateProduct,
            ProductExtend,
            ProductOrder,
            Order,
            OrderHistory,
            SerialNumbers,
            BufferHistory
        }
    }
    public class AuditTrailProperty
    {
        public string Property { get; set; }
        public string OldValue { get; set; }
        public string NewValue { get; set; }
    }

    public class AuditTrailItemExport
    {
        public AuditTrailItemExport()
        {

        }

        public AuditTrailItemExport(DateTime timestamp,
                                    int idAuditTrail,
                                    string action,
                                    string displayName,
                                    string oldValue,
                                    string newValue,
                                    string user)
        {
            Timestamp = timestamp;
            IDAuditTrail = idAuditTrail;
            Action = action;
            DisplayName = displayName;
            OldValue = oldValue;
            NewValue = newValue;
            User = user;
        }

        public int IDAuditTrail { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime Timestamp { get; set; }

        public string FieldName { get; set; }

        public string Action { get; set; }

        public string OldValue { get; set; }

        public string NewValue { get; set; }

        public string DisplayName { get; set; }

        public string User { get; set; }
    }


    public enum DropoutReasonEnum
    {
        DEFECT,
        EXPIRY,
        QA_SAMPLES,
        PRODUCT_RECALL,
        COMPLAINTS,
        PRODUCT_TESTING,
        DEMO_SAMPLES,
        OTHER
    }

    public enum OrderStatusEnum
    {

        UNKNOWN,
        NEW,
        PUBLISHING,
        PUBLISHED,
        CREATED,
        PENDING,
        DECLINED,
        REJECTED,
        EXPIRED,
        APPROVED,
        READY,
        CLOSED,
        CANCELED,
        ERROR
    }

    public enum BufferStatusEnum
    {
        UNKNOWN,
        NEW,
        PENDING,
        ACTIVE,
        EXHAUSTED,
        REJECTED,
        CLOSED
    }
}
