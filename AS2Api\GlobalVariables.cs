﻿using Microsoft.EntityFrameworkCore;

namespace AS2Api
{
    public class GlobalVariables
    {
        private readonly AzureCmdModels.DBModel _cmdDBModel;

        private readonly IHttpContextAccessor _httpContextAccessor;

        public AzureCmdModels.DBModel CmdDBModel
        {
            get
            {
                if (_cmdDBModel != null)
                    return _cmdDBModel;

                else
                    throw new Exception("Error! SATT PLATFORM [CloudMasterData] Database is not connected");
            }
        }

        public bool IsError { get; set; }
        public string ErrorMessage { get; set; }


        public AzureCmdModels.UserLogin UserLogin { get; set; }

        public GlobalVariables(IHttpContextAccessor httpContextAccessor, AzureCmdModels.DBModel cmdDBModel)
        {
            _httpContextAccessor = httpContextAccessor;
            _cmdDBModel = cmdDBModel;

            InitializeUser();
        }


        public AzureCmdModels.UserLogin GetUserLogin(string lgnName)
        {
            return _cmdDBModel.UserLogins.AsNoTracking()
                .FirstOrDefault(u => u.LgnName == lgnName && u.IDCustomer == 0);
        }

        private void InitializeUser()
        {
            try
            {
                if (_httpContextAccessor == null)
                    throw new Exception("HTTP Context Accessor missing.");

                var httpContext = _httpContextAccessor.HttpContext;
                if (httpContext.User == null || httpContext.User.Identity == null || string.IsNullOrEmpty(httpContext.User.Identity.Name))
                    throw new Exception("User Identity unknown");

                UserLogin = GetUserLogin(httpContext.User.Identity.Name);
                if (UserLogin == null)
                    throw new Exception("Unknown user");

            }
            catch (Exception e)
            {
                IsError = true;
                ErrorMessage = $"User initialization error {e.Message}";
            }
        }

        private int idCustomer;

        public int IDCustomer
        {
            get
            {
                return idCustomer;
            }
            set
            {
                idCustomer = value;
            }
        }
    }
}
