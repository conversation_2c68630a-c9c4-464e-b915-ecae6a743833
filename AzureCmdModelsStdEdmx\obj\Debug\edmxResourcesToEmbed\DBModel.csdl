﻿<?xml version="1.0" encoding="utf-8"?>
<Schema Namespace="CloudMasterData" Alias="Self" annotation:UseStrongSpatialTypes="false" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
  <EntityType Name="AdditionalCodeType">
    <Key>
      <PropertyRef Name="Name" />
    </Key>
    <Property Name="Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" Nullable="false" />
  </EntityType>
  <EntityType Name="AuditTrail">
    <Key>
      <PropertyRef Name="IDAuditTrail" />
    </Key>
    <Property Name="IDAuditTrail" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
    <Property Name="Action" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
    <Property Name="IDTable" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
    <Property Name="TableName" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
    <Property Name="Timestamp" Type="DateTime" Precision="3" />
    <Property Name="User" Type="String" MaxLength="128" FixedLength="false" Unicode="true" />
    <Property Name="DisplayName" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
    <Property Name="IDTableDetail" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
  </EntityType>
  <EntityType Name="AuditTrailItem">
    <Key>
      <PropertyRef Name="IDAuditTrailItem" />
    </Key>
    <Property Name="IDAuditTrailItem" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
    <Property Name="IDAuditTrail" Type="Int32" Nullable="false" />
    <Property Name="FieldName" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
    <Property Name="OldValue" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
    <Property Name="NewValue" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
    <Property Name="DisplayName" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
  </EntityType>
  <EntityType Name="BusinessSegment">
    <Key>
      <PropertyRef Name="IDBusinessSegment" />
    </Key>
    <Property Name="IDBusinessSegment" Type="Int32" Nullable="false" />
    <Property Name="Segment" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
  </EntityType>
  <EntityType Name="CommunicationChannel">
    <Key>
      <PropertyRef Name="Type" />
    </Key>
    <Property Name="Type" Type="String" MaxLength="50" FixedLength="false" Unicode="true" Nullable="false" />
  </EntityType>
  <EntityType Name="Contract">
    <Key>
      <PropertyRef Name="IDContract" />
    </Key>
    <Property Name="IDContract" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
    <Property Name="IDCustomer" Type="Int32" Nullable="false" />
    <Property Name="Name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="StartDate" Type="DateTime" Nullable="false" Precision="0" />
    <Property Name="EndDate" Type="DateTime" Nullable="false" Precision="0" />
    <Property Name="IDContractStatus" Type="Int32" Nullable="false" />
    <Property Name="IDContractType" Type="Int32" Nullable="false" />
    <Property Name="MaxUsers" Type="Int32" Nullable="false" />
    <Property Name="ExpirationWarningDays" Type="Int32" Nullable="false" />
    <Property Name="SerialNumberCount" Type="Int32" Nullable="false" />
    <Property Name="ContractNumber" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
    <Property Name="TimestampCreated" Type="DateTime" Nullable="false" Precision="3" />
    <Property Name="UserCreated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="TimestampUpdated" Type="DateTime" Precision="3" />
    <Property Name="UserUpdated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" />
  </EntityType>
  <EntityType Name="ContractStatu">
    <Key>
      <PropertyRef Name="IDContractStatus" />
    </Key>
    <Property Name="IDContractStatus" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
    <Property Name="Name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
  </EntityType>
  <EntityType Name="ContractType">
    <Key>
      <PropertyRef Name="IDContractType" />
    </Key>
    <Property Name="IDContractType" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
    <Property Name="Name" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
  </EntityType>
  <EntityType Name="Country">
    <Key>
      <PropertyRef Name="Code" />
    </Key>
    <Property Name="Code" Type="String" MaxLength="2" FixedLength="true" Unicode="false" Nullable="false" />
    <Property Name="Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="NameSecLng" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
    <Property Name="GS1RNCode" Type="Int32" />
  </EntityType>
  <EntityType Name="Customer">
    <Key>
      <PropertyRef Name="IDCustomer" />
    </Key>
    <Property Name="IDCustomer" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
    <Property Name="Name" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
    <Property Name="TimestampCreated" Type="DateTime" Nullable="false" Precision="3" />
    <Property Name="UserCreated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="TimestampUpdated" Type="DateTime" Precision="3" />
    <Property Name="UserUpdated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" />
    <Property Name="GS1CompanyPrefix" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
    <Property Name="SGLN" Type="String" MaxLength="20" FixedLength="false" Unicode="false" />
    <Property Name="GLN" Type="String" MaxLength="13" FixedLength="false" Unicode="false" />
    <Property Name="CustomerStreet" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
    <Property Name="CustomerStreet2" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
    <Property Name="CustomerCity" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
    <Property Name="CustomerPostCode" Type="String" MaxLength="30" FixedLength="false" Unicode="true" />
    <Property Name="CustomerCountryCode" Type="String" MaxLength="2" FixedLength="true" Unicode="false" />
    <Property Name="Code" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
    <Property Name="IDBusinessSegment" Type="Int32" Nullable="false" />
    <NavigationProperty Name="CustomerAggregationLevels" Relationship="Self.FK_CustomerAggregationLevel_Customer" FromRole="Customer" ToRole="CustomerAggregationLevel" />
    <NavigationProperty Name="ProductLabels" Relationship="Self.FK_ProductLabel_Customer" FromRole="Customer" ToRole="ProductLabel" />
  </EntityType>
  <EntityType Name="CustomerAggregationLevel">
    <Key>
      <PropertyRef Name="IDCustomerAggregationLevel" />
    </Key>
    <Property Name="IDCustomerAggregationLevel" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
    <Property Name="IDCustomer" Type="Int32" Nullable="false" />
    <Property Name="IDProduct" Type="Int32" />
    <Property Name="Name" Type="String" MaxLength="20" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="Type" Type="String" MaxLength="20" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="SerialType" Type="String" MaxLength="10" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="UnitCapacity" Type="Int32" />
    <Property Name="SubLevel" Type="String" MaxLength="20" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="SubLevelItemType" Type="String" MaxLength="20" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="TimestampCreated" Type="DateTime" Nullable="false" Precision="3" />
    <Property Name="UserCreated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="TimestampUpdated" Type="DateTime" Precision="3" />
    <Property Name="UserUpdated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" />
    <NavigationProperty Name="Customer" Relationship="Self.FK_CustomerAggregationLevel_Customer" FromRole="CustomerAggregationLevel" ToRole="Customer" />
    <NavigationProperty Name="Product" Relationship="Self.FK_CustomerAggregationLevel_Product" FromRole="CustomerAggregationLevel" ToRole="Product" />
  </EntityType>
  <EntityType Name="CustomerContract">
    <Key>
      <PropertyRef Name="IDCustomerContract" />
    </Key>
    <Property Name="IDCustomerContract" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
    <Property Name="IDCustomer" Type="Int32" Nullable="false" />
    <Property Name="Name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="StartDate" Type="DateTime" Nullable="false" Precision="0" />
    <Property Name="EndDate" Type="DateTime" Precision="0" />
    <Property Name="IsTrial" Type="Boolean" Nullable="false" />
    <Property Name="TrialEndDate" Type="DateTime" Precision="3" />
    <Property Name="IsContract" Type="Boolean" Nullable="false" />
    <Property Name="ContractTerm" Type="DateTime" Precision="0" />
    <Property Name="ContractData" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
    <Property Name="ContractNotifyEndDays" Type="Int32" Nullable="false" />
    <Property Name="IsPayed" Type="Boolean" Nullable="false" />
    <Property Name="PaymentTerm" Type="DateTime" Precision="0" />
    <Property Name="PaymentNotifyDays" Type="Int32" Nullable="false" />
    <Property Name="InvoiceData" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
    <Property Name="IsCanceled" Type="Boolean" Nullable="false" />
    <Property Name="CancelReason" Type="String" MaxLength="200" FixedLength="false" Unicode="true" />
    <Property Name="TimestampCreated" Type="DateTime" Nullable="false" Precision="3" />
    <Property Name="UserCreated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="TimestampUpdated" Type="DateTime" Precision="3" />
    <Property Name="UserUpdated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" />
  </EntityType>
  <EntityType Name="CustomerLabelDataSource">
    <Key>
      <PropertyRef Name="IDCustomerDataSource" />
    </Key>
    <Property Name="IDCustomerDataSource" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
    <Property Name="IDCustomer" Type="Int32" Nullable="false" />
    <Property Name="Name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="DataSource" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
    <Property Name="TimestampCreated" Type="DateTime" Nullable="false" Precision="3" />
    <Property Name="UserCreated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="TimestampUpdated" Type="DateTime" Precision="3" />
    <Property Name="UserUpdated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" />
    <Property Name="SrcSystemName" Type="String" MaxLength="200" FixedLength="false" Unicode="true" />
    <Property Name="SrcSystem" Type="String" MaxLength="2" FixedLength="false" Unicode="true" />
    <Property Name="Unit" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
    <Property Name="SerialType" Type="String" MaxLength="20" FixedLength="false" Unicode="true" />
    <Property Name="Item" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
    <Property Name="ItemSerialType" Type="String" MaxLength="20" FixedLength="false" Unicode="true" />
  </EntityType>
  <EntityType Name="CustomerPartner">
    <Key>
      <PropertyRef Name="IDCustomerPartner" />
    </Key>
    <Property Name="IDCustomerPartner" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
    <Property Name="IDCustomer" Type="Int32" Nullable="false" />
    <Property Name="Name" Type="String" MaxLength="255" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="SystemName" Type="String" MaxLength="255" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="SNXRequest" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
    <Property Name="SSCCRequest" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
    <Property Name="EBRImport" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
    <Property Name="BatchReport" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
    <Property Name="ShipmentReport" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
    <Property Name="IsActive" Type="Boolean" Nullable="false" />
  </EntityType>
  <EntityType Name="CustomerPartnerParameter">
    <Key>
      <PropertyRef Name="IDCustomerPartnerParameter" />
    </Key>
    <Property Name="IDCustomerPartnerParameter" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
    <Property Name="IDCustomerPartner" Type="Int32" Nullable="false" />
    <Property Name="Name" Type="String" MaxLength="255" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="Value" Type="String" MaxLength="255" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="TimestampCreated" Type="DateTime" Nullable="false" Precision="3" />
    <Property Name="UserCreated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
  </EntityType>
  <EntityType Name="CustomerService">
    <Key>
      <PropertyRef Name="IDCustomerService" />
    </Key>
    <Property Name="IDCustomerService" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
    <Property Name="IDCustomer" Type="Int32" Nullable="false" />
    <Property Name="IDService" Type="Int32" Nullable="false" />
    <Property Name="IsDatabaseCustomerSeparation" Type="Boolean" Nullable="false" />
    <Property Name="DatabaseConnectionString" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
  </EntityType>
  <EntityType Name="InternalCodeType">
    <Key>
      <PropertyRef Name="Name" />
    </Key>
    <Property Name="Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" Nullable="false" />
  </EntityType>
  <EntityType Name="LabelTemplate">
    <Key>
      <PropertyRef Name="IDLabelTemplate" />
    </Key>
    <Property Name="IDLabelTemplate" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
    <Property Name="IDCustomer" Type="Int32" Nullable="false" />
    <Property Name="Unit" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
    <Property Name="Label" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="LabelName" Type="String" MaxLength="255" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="TimestampCreated" Type="DateTime" Nullable="false" Precision="3" />
    <Property Name="UserCreated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
  </EntityType>
  <EntityType Name="MahType">
    <Key>
      <PropertyRef Name="IDMahType" />
    </Key>
    <Property Name="IDMahType" Type="Int32" Nullable="false" />
    <Property Name="Type" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
  </EntityType>
  <EntityType Name="Manufacturer">
    <Key>
      <PropertyRef Name="IDMah" />
    </Key>
    <Property Name="IDMah" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
    <Property Name="IDCustomer" Type="Int32" Nullable="false" />
    <Property Name="IDMahType" Type="Int32" />
    <Property Name="MahID" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
    <Property Name="MahName" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="MahStreet" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
    <Property Name="MahStreet2" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
    <Property Name="MahCity" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
    <Property Name="MahPostCode" Type="String" MaxLength="30" FixedLength="false" Unicode="true" />
    <Property Name="MahCountryCode" Type="String" MaxLength="2" FixedLength="true" Unicode="false" />
    <Property Name="TimeStampCreated" Type="DateTime" Nullable="false" Precision="3" />
    <Property Name="UserCreated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="TimeStampUpdated" Type="DateTime" Precision="3" />
    <Property Name="UserUpdated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" />
    <Property Name="GS1CompanyPrefix" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
    <Property Name="GLN" Type="String" MaxLength="13" FixedLength="false" Unicode="false" />
    <Property Name="SGLN" Type="String" MaxLength="20" FixedLength="false" Unicode="false" />
    <Property Name="IsActive" Type="Boolean" Nullable="false" />
  </EntityType>
  <EntityType Name="Product">
    <Key>
      <PropertyRef Name="IDProduct" />
    </Key>
    <Property Name="IDProduct" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
    <Property Name="IDServiceMasterRecord" Type="Int32" Nullable="false" />
    <Property Name="IDCustomer" Type="Int32" Nullable="false" />
    <Property Name="CodeType" Type="String" MaxLength="10" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="Code" Type="String" MaxLength="14" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="InternalCodeType" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
    <Property Name="InternalCode" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
    <Property Name="Name" Type="String" MaxLength="255" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="CommonName" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
    <Property Name="Form" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
    <Property Name="PackType" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
    <Property Name="PackSize" Type="Int32" />
    <Property Name="Strength" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
    <Property Name="IsActive" Type="Boolean" Nullable="false" />
    <Property Name="TimestampCreated" Type="DateTime" Nullable="false" Precision="3" />
    <Property Name="UserCreated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="TimestampUpdated" Type="DateTime" Precision="3" />
    <Property Name="UserUpdated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" />
    <Property Name="SerialNumberSourceType" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
    <Property Name="SerializationType" Type="String" MaxLength="128" FixedLength="false" Unicode="false" />
    <Property Name="IDCustomerPartner" Type="Int32" />
    <NavigationProperty Name="CustomerAggregationLevels" Relationship="Self.FK_CustomerAggregationLevel_Product" FromRole="Product" ToRole="CustomerAggregationLevel" />
    <NavigationProperty Name="ProductLabels" Relationship="Self.FK_ProductLabel_Product" FromRole="Product" ToRole="ProductLabel" />
  </EntityType>
  <EntityType Name="ProductAdditionalCode">
    <Key>
      <PropertyRef Name="IDProductAdditionalCode" />
    </Key>
    <Property Name="IDProductAdditionalCode" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
    <Property Name="IDProduct" Type="Int32" Nullable="false" />
    <Property Name="CodeType" Type="String" MaxLength="50" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="Code" Type="String" MaxLength="50" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="UserCreated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="TimestampCreated" Type="DateTime" Nullable="false" Precision="3" />
    <Property Name="UserUpdated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" />
    <Property Name="TimestampUpdated" Type="DateTime" Precision="3" />
  </EntityType>
  <EntityType Name="ProductCodeType">
    <Key>
      <PropertyRef Name="CodeType" />
    </Key>
    <Property Name="CodeType" Type="String" MaxLength="10" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="Length" Type="Int32" />
    <Property Name="Regex" Type="String" MaxLength="128" FixedLength="false" Unicode="true" />
  </EntityType>
  <EntityType Name="ProductLabel">
    <Key>
      <PropertyRef Name="IDProductLabel" />
    </Key>
    <Property Name="IDProductLabel" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
    <Property Name="IDCustomer" Type="Int32" Nullable="false" />
    <Property Name="IDProduct" Type="Int32" Nullable="false" />
    <Property Name="LabelName" Type="String" MaxLength="255" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="Label" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="Level" Type="String" MaxLength="20" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="IsDefault" Type="Boolean" Nullable="false" />
    <Property Name="IDTargetMarket" Type="Int32" />
    <Property Name="TimestampCreated" Type="DateTime" Nullable="false" Precision="3" />
    <Property Name="UserCreated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="PrintingSrcSystem" Type="String" MaxLength="2" FixedLength="false" Unicode="true" />
    <Property Name="PrintingSrcSystemName" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
    <NavigationProperty Name="Customer" Relationship="Self.FK_ProductLabel_Customer" FromRole="ProductLabel" ToRole="Customer" />
    <NavigationProperty Name="Product" Relationship="Self.FK_ProductLabel_Product" FromRole="ProductLabel" ToRole="Product" />
    <NavigationProperty Name="TargetMarket" Relationship="Self.FK_ProductLabel_TargetMarket" FromRole="ProductLabel" ToRole="TargetMarket" />
  </EntityType>
  <EntityType Name="SerializationType">
    <Key>
      <PropertyRef Name="IDSerializationType" />
    </Key>
    <Property Name="IDSerializationType" Type="String" MaxLength="30" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="SerializationType1" Type="String" MaxLength="50" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="Regex" Type="String" MaxLength="250" FixedLength="false" Unicode="true" />
    <Property Name="OrderDisplayList" Type="Int32" />
    <Property Name="IsActive" Type="Boolean" Nullable="false" />
    <Property Name="IsCarton" Type="Boolean" Nullable="false" />
    <Property Name="IsCase" Type="Boolean" Nullable="false" />
    <Property Name="IsPallet" Type="Boolean" Nullable="false" />
    <Property Name="IsSerialsExpected" Type="Boolean" />
  </EntityType>
  <EntityType Name="SerialNumberSourceType">
    <Key>
      <PropertyRef Name="IDSerialNumberSourceType" />
    </Key>
    <Property Name="IDSerialNumberSourceType" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
    <Property Name="IDService" Type="Int32" Nullable="false" />
    <Property Name="Code" Type="String" MaxLength="50" FixedLength="false" Unicode="false" Nullable="false" />
    <Property Name="Name" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
  </EntityType>
  <EntityType Name="Service">
    <Key>
      <PropertyRef Name="IDService" />
    </Key>
    <Property Name="IDService" Type="Int32" Nullable="false" />
    <Property Name="Name" Type="String" MaxLength="255" FixedLength="false" Unicode="false" Nullable="false" />
    <Property Name="SrcSystem" Type="String" MaxLength="2" FixedLength="false" Unicode="true" />
    <Property Name="SerializationType" Type="String" MaxLength="30" FixedLength="false" Unicode="true" />
    <Property Name="IsCMD" Type="Boolean" Nullable="false" />
    <Property Name="IsServiceOwnProduct" Type="Boolean" Nullable="false" />
    <Property Name="IsServiceOwnCustomer" Type="Boolean" Nullable="false" />
    <Property Name="ADURI" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
    <Property Name="ProductUpdateSql" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
    <Property Name="CustomerUpdateSql" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
    <Property Name="IsServiceOwnManufacturer" Type="Boolean" Nullable="false" />
    <Property Name="ManufacturerUpdateSql" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
    <Property Name="UserUpdateSql" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
    <Property Name="IsExternalCodeAccepted" Type="Boolean" Nullable="false" />
    <Property Name="ProductRegex" Type="String" MaxLength="500" FixedLength="false" Unicode="true" />
    <Property Name="IsUserDeleteAllowed" Type="Boolean" Nullable="false" />
    <Property Name="IsServiceOwnLabel" Type="Boolean" Nullable="false" />
  </EntityType>
  <EntityType Name="ServiceContract">
    <Key>
      <PropertyRef Name="IDServiceContract" />
    </Key>
    <Property Name="IDServiceContract" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
    <Property Name="IDContract" Type="Int32" Nullable="false" />
    <Property Name="IDService" Type="Int32" Nullable="false" />
  </EntityType>
  <EntityType Name="ServiceEnvironment">
    <Key>
      <PropertyRef Name="IDServiceEnvironment" />
    </Key>
    <Property Name="IDServiceEnvironment" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
    <Property Name="IDService" Type="Int32" Nullable="false" />
    <Property Name="Name" Type="String" MaxLength="255" FixedLength="false" Unicode="false" />
  </EntityType>
  <EntityType Name="ServiceRole">
    <Key>
      <PropertyRef Name="IDServiceRole" />
    </Key>
    <Property Name="IDServiceRole" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
    <Property Name="RoleName" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="GroupName" Type="String" MaxLength="128" FixedLength="false" Unicode="true" />
  </EntityType>
  <EntityType Name="ServiceUserLogin">
    <Key>
      <PropertyRef Name="IDServiceUserLogin" />
    </Key>
    <Property Name="IDServiceUserLogin" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
    <Property Name="IDService" Type="Int32" Nullable="false" />
    <Property Name="LgnName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="ExternalSenderCode" Type="String" MaxLength="250" FixedLength="false" Unicode="true" />
    <Property Name="ExternalRecieverCode" Type="String" MaxLength="250" FixedLength="false" Unicode="true" />
    <Property Name="IDManufacturer" Type="Int32" />
  </EntityType>
  <EntityType Name="TargetMarket">
    <Key>
      <PropertyRef Name="IDTargetMarket" />
    </Key>
    <Property Name="IDTargetMarket" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
    <Property Name="Name" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="ShortName" Type="String" MaxLength="4" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="GS1RNCode" Type="Int32" />
    <Property Name="TimestampCreated" Type="DateTime" Nullable="false" Precision="3" />
    <Property Name="UserCreated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="TimestampUpdated" Type="DateTime" Precision="3" />
    <Property Name="UserUpdated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" />
    <Property Name="SerializationType" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
    <Property Name="SrcSystem" Type="String" MaxLength="2" FixedLength="false" Unicode="true" />
    <NavigationProperty Name="ProductLabels" Relationship="Self.FK_ProductLabel_TargetMarket" FromRole="TargetMarket" ToRole="ProductLabel" />
  </EntityType>
  <EntityType Name="UserLogin">
    <Key>
      <PropertyRef Name="LgnName" />
    </Key>
    <Property Name="LgnName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="IDCustomer" Type="Int32" Nullable="false" />
    <Property Name="IDManufacturer" Type="Int32" />
    <Property Name="IsADUser" Type="Boolean" Nullable="false" />
    <Property Name="TimestampCreated" Type="DateTime" Nullable="false" Precision="3" />
    <Property Name="UserCreated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="TimestampUpdated" Type="DateTime" Precision="3" />
    <Property Name="UserUpdated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" />
  </EntityType>
  <EntityType Name="UserLoginServiceRole">
    <Key>
      <PropertyRef Name="IDUserLoginServiceRole" />
    </Key>
    <Property Name="IDUserLoginServiceRole" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
    <Property Name="LgnName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="IDServiceRole" Type="Int32" Nullable="false" />
  </EntityType>
  <EntityType Name="UserPrinterSetting">
    <Key>
      <PropertyRef Name="LgnName" />
    </Key>
    <Property Name="LgnName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="UseNetworkPrinter" Type="Boolean" Nullable="false" />
    <Property Name="NetworkPrinterIP" Type="String" MaxLength="20" FixedLength="false" Unicode="true" />
    <Property Name="NetworkPrinterPort" Type="Int32" />
    <Property Name="PrinterDriverName" Type="String" MaxLength="250" FixedLength="false" Unicode="true" />
    <Property Name="PrinterLanguageType" Type="String" MaxLength="8" FixedLength="false" Unicode="true" />
    <Property Name="TimestampCreated" Type="DateTime" Nullable="false" Precision="3" />
    <Property Name="TimestampUpdated" Type="DateTime" Precision="3" />
    <Property Name="UserCreated" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
    <Property Name="UserUpdated" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
  </EntityType>
  <Association Name="FK_CustomerAggregationLevel_Customer">
    <End Role="Customer" Type="Self.Customer" Multiplicity="1" />
    <End Role="CustomerAggregationLevel" Type="Self.CustomerAggregationLevel" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="Customer">
        <PropertyRef Name="IDCustomer" />
      </Principal>
      <Dependent Role="CustomerAggregationLevel">
        <PropertyRef Name="IDCustomer" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Association Name="FK_ProductLabel_Customer">
    <End Role="Customer" Type="Self.Customer" Multiplicity="1" />
    <End Role="ProductLabel" Type="Self.ProductLabel" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="Customer">
        <PropertyRef Name="IDCustomer" />
      </Principal>
      <Dependent Role="ProductLabel">
        <PropertyRef Name="IDCustomer" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Association Name="FK_CustomerAggregationLevel_Product">
    <End Role="Product" Type="Self.Product" Multiplicity="0..1" />
    <End Role="CustomerAggregationLevel" Type="Self.CustomerAggregationLevel" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="Product">
        <PropertyRef Name="IDProduct" />
      </Principal>
      <Dependent Role="CustomerAggregationLevel">
        <PropertyRef Name="IDProduct" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Association Name="FK_ProductLabel_Product">
    <End Role="Product" Type="Self.Product" Multiplicity="1" />
    <End Role="ProductLabel" Type="Self.ProductLabel" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="Product">
        <PropertyRef Name="IDProduct" />
      </Principal>
      <Dependent Role="ProductLabel">
        <PropertyRef Name="IDProduct" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Association Name="FK_ProductLabel_TargetMarket">
    <End Role="TargetMarket" Type="Self.TargetMarket" Multiplicity="0..1" />
    <End Role="ProductLabel" Type="Self.ProductLabel" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="TargetMarket">
        <PropertyRef Name="IDTargetMarket" />
      </Principal>
      <Dependent Role="ProductLabel">
        <PropertyRef Name="IDTargetMarket" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <EntityContainer Name="DBModel" annotation:LazyLoadingEnabled="true">
    <EntitySet Name="AdditionalCodeTypes" EntityType="Self.AdditionalCodeType" />
    <EntitySet Name="AuditTrails" EntityType="Self.AuditTrail" />
    <EntitySet Name="AuditTrailItems" EntityType="Self.AuditTrailItem" />
    <EntitySet Name="BusinessSegments" EntityType="Self.BusinessSegment" />
    <EntitySet Name="CommunicationChannels" EntityType="Self.CommunicationChannel" />
    <EntitySet Name="Contracts" EntityType="Self.Contract" />
    <EntitySet Name="ContractStatus" EntityType="Self.ContractStatu" />
    <EntitySet Name="ContractTypes" EntityType="Self.ContractType" />
    <EntitySet Name="Countries" EntityType="Self.Country" />
    <EntitySet Name="Customers" EntityType="Self.Customer" />
    <EntitySet Name="CustomerAggregationLevels" EntityType="Self.CustomerAggregationLevel" />
    <EntitySet Name="CustomerContracts" EntityType="Self.CustomerContract" />
    <EntitySet Name="CustomerLabelDataSources" EntityType="Self.CustomerLabelDataSource" />
    <EntitySet Name="CustomerPartners" EntityType="Self.CustomerPartner" />
    <EntitySet Name="CustomerPartnerParameters" EntityType="Self.CustomerPartnerParameter" />
    <EntitySet Name="CustomerServices" EntityType="Self.CustomerService" />
    <EntitySet Name="InternalCodeTypes" EntityType="Self.InternalCodeType" />
    <EntitySet Name="LabelTemplates" EntityType="Self.LabelTemplate" />
    <EntitySet Name="MahTypes" EntityType="Self.MahType" />
    <EntitySet Name="Manufacturers" EntityType="Self.Manufacturer" />
    <EntitySet Name="Products" EntityType="Self.Product" />
    <EntitySet Name="ProductAdditionalCodes" EntityType="Self.ProductAdditionalCode" />
    <EntitySet Name="ProductCodeTypes" EntityType="Self.ProductCodeType" />
    <EntitySet Name="ProductLabels" EntityType="Self.ProductLabel" />
    <EntitySet Name="SerializationTypes" EntityType="Self.SerializationType" />
    <EntitySet Name="SerialNumberSourceTypes" EntityType="Self.SerialNumberSourceType" />
    <EntitySet Name="Services" EntityType="Self.Service" />
    <EntitySet Name="ServiceContracts" EntityType="Self.ServiceContract" />
    <EntitySet Name="ServiceEnvironments" EntityType="Self.ServiceEnvironment" />
    <EntitySet Name="ServiceRoles" EntityType="Self.ServiceRole" />
    <EntitySet Name="ServiceUserLogins" EntityType="Self.ServiceUserLogin" />
    <EntitySet Name="TargetMarkets" EntityType="Self.TargetMarket" />
    <EntitySet Name="UserLogins" EntityType="Self.UserLogin" />
    <EntitySet Name="UserLoginServiceRoles" EntityType="Self.UserLoginServiceRole" />
    <EntitySet Name="UserPrinterSettings" EntityType="Self.UserPrinterSetting" />
    <AssociationSet Name="FK_CustomerAggregationLevel_Customer" Association="Self.FK_CustomerAggregationLevel_Customer">
      <End Role="Customer" EntitySet="Customers" />
      <End Role="CustomerAggregationLevel" EntitySet="CustomerAggregationLevels" />
    </AssociationSet>
    <AssociationSet Name="FK_ProductLabel_Customer" Association="Self.FK_ProductLabel_Customer">
      <End Role="Customer" EntitySet="Customers" />
      <End Role="ProductLabel" EntitySet="ProductLabels" />
    </AssociationSet>
    <AssociationSet Name="FK_CustomerAggregationLevel_Product" Association="Self.FK_CustomerAggregationLevel_Product">
      <End Role="Product" EntitySet="Products" />
      <End Role="CustomerAggregationLevel" EntitySet="CustomerAggregationLevels" />
    </AssociationSet>
    <AssociationSet Name="FK_ProductLabel_Product" Association="Self.FK_ProductLabel_Product">
      <End Role="Product" EntitySet="Products" />
      <End Role="ProductLabel" EntitySet="ProductLabels" />
    </AssociationSet>
    <AssociationSet Name="FK_ProductLabel_TargetMarket" Association="Self.FK_ProductLabel_TargetMarket">
      <End Role="TargetMarket" EntitySet="TargetMarkets" />
      <End Role="ProductLabel" EntitySet="ProductLabels" />
    </AssociationSet>
  </EntityContainer>
</Schema>