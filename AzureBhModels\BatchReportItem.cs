﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureBhModels
{
    [Table("BatchReportItem")]
    public class BatchReportItem
    {
        public BatchReportItem()
        {
        }

        [Key]
        public int IDBatchReportItem { get; set; }

        public int IDBatchReport { get; set; }

        [Required]
        [StringLength(50)]
        public string Level { get; set; }

        [StringLength(10)]
        public string CodeType { get; set; }

        [StringLength(100)]
        public string SerialNumber { get; set; }

        public int SerializedUnitsCount { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [InverseProperty("BatchReportItems")]
        public BatchReport BatchReport { get; set; }
    }
}
