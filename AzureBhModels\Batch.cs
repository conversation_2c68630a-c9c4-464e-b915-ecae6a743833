﻿using SgXmlEvent;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;

namespace AzureBhModels
{
    [Table("Batch")]
    public class Batch : INotifyPropertyChanged
    {
        public Batch()
        {
            BatchSerializations = new HashSet<BatchSerialization>();
            BatchAggregations = new HashSet<BatchAggregation>();
            NotAggregatedSerials = new HashSet<NotAggregatedSerial>();
            BatchSerialStates = new HashSet<BatchSerialState>();
            SerialLevels = new HashSet<SerialLevel>();
            SgFiles = new HashSet<SgFile>();
            VerificationErrors = new HashSet<VerificationError>();
            BatchReports = new HashSet<BatchReport>();
        }

        public Batch(SgXmlEvent.SgXmlEvent file, int idProduct, SgVerificationResult sgVerificationResult, string lgnName)
        {
            DateTime timestampCreated = DateTime.UtcNow;

            this.BatchID = file.LOT;
            this.ExpiryDate = file.EXP.Value;
            this.IDProduct = idProduct;
            this.TimestampCreated = DateTime.UtcNow;
            this.UserCreated = lgnName;
            this.State = (int)BatchStateEnum.NEW;
            this.ManufacturingDate = file.MNF;

            VerificationErrors = sgVerificationResult.Errors
                .Where(e => e.IsSkipAllowed)
                .Select(e => new VerificationError
                {
                    IsSkipAllowed = e.IsSkipAllowed,
                    SerialNumber = e.SerialNumber,
                    SerialType = e.SerialType,
                    ItemSerialNumber = e.ItemSerialNumber,
                    ItemSerialType = e.ItemSerialType,
                    Error = e.Error,
                    Level = e.Level,
                    TimestampCreated = timestampCreated,
                    UserCreated = lgnName
                })
                .ToList();

            if (file.AggregationLevels != null)
            {
                SerialLevels = file.AggregationLevels
                   .Select(l => new SerialLevel
                   {
                       Name = l.Name,
                       TimestampCreated = timestampCreated,
                       UserCreated = lgnName,
                       Type = l.Type,
                       UnitCapacity = l.UnitCapacity,
                       Count = l.Count,
                       SubLevel = l.ItemLevel,
                       IsAggregationLevel = l.Name != PackLevel.SERIALIZED_UNIT,
                       SerialType = l.CodeType,
                       SubLevelItemType = l.ItemCodeType
                   })
                   .ToList();
            }

            if (file.SerialStateCounters != null)
            {
                BatchSerialStates = file.SerialStateCounters
                    .Select(s => new BatchSerialState
                    {
                        CurrentCount = s.Count,
                        InitialCount = s.Count,
                        State = s.State,
                        TimestampCreated = timestampCreated,
                        UserCreated = lgnName
                    })
                    .ToList();
            }

        }

        public Batch(int idProduct, string batchID, DateTime expiryDate, string lgnName)
        { //TODO: Check property values
            this.IDProduct = idProduct;
            this.BatchID = batchID;
            this.ExpiryDate = expiryDate;
            this.UserCreated = lgnName;
            this.TimestampCreated = DateTime.Now;
            this.State = (int)BatchStateEnum.NEW;
        }


        [Key]
        public int IDBatch { get; set; }

        [Required]
        public int? IDProduct { get; set; }

        [Required]
        [StringLength(100)]
        [RegularExpression(@"^([\u0021;\u0022;\u0025;\u0026;\u0027;\u0028;\u0029;\u002A;\u002B;\u002C;\u002D;\u002E;\u002F;\u003A;\u003B;\u003C;\u003D;\u003E;\u003F;\u005F;a-zA-Z0-9]{1,20})$",
         ErrorMessage = "Batch ID is expected to be alphanumeric values of up to 20 characters, limited to those characters defined in the GS1 General  Specifications.")]
        public string BatchID { get; set; }

        [Required]
        [DisplayName("Expiry date")]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? ExpiryDate { get; set; }

        [DisplayName("Manufacturing date")]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? ManufacturingDate { get; set; }

        public BatchStateEnum State { get; set; } //TODO

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        [InverseProperty("Batch")]
        public ICollection<BatchSerialization> BatchSerializations { get; set; }

        [InverseProperty("Batch")]
        public ICollection<BatchAggregation> BatchAggregations { get; set; }

        [InverseProperty("Batch")]
        public ICollection<SerialLevel> SerialLevels { get; set; }

        [InverseProperty("Batch")]
        public ICollection<VerificationError> VerificationErrors { get; set; }

        [InverseProperty("Batch")]
        public ICollection<SgFile> SgFiles { get; set; }

        [InverseProperty("Batch")]
        public ICollection<BatchSerialState> BatchSerialStates { get; set; }

        [InverseProperty("Batch")]
        public ICollection<BatchReport> BatchReports { get; set; }

        [InverseProperty("Batch")]
        public ICollection<NotAggregatedSerial> NotAggregatedSerials { get; set; }

        [NotMapped]
        public bool LoaderVisible { get; private set; }



        public event PropertyChangedEventHandler PropertyChanged;
        private void NotifyPropertyChanged([CallerMemberName] string propertyName = "")
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public void ShowLoader()
        {
            LoaderVisible = true;
            NotifyPropertyChanged(nameof(LoaderVisible));
        }

        public void HideLoader()
        {
            LoaderVisible = false;
            NotifyPropertyChanged(nameof(LoaderVisible));
        }

        public SgXmlEvent.SgXmlEvent ToSgXmlEvent(List<string> states = null)
        {
            SgXmlEvent.SgXmlEvent sgXmlEvent = new SgXmlEvent.SgXmlEvent()
            {
                //TODO: CIP = this.IDProduct, //todo
                EXP = this.ExpiryDate,
                LOT = this.BatchID,
                MNF = this.ManufacturingDate,
                SERIALSECTION = this.BatchSerializations
                                                   .Select(s => new SgXmlEvent.SerialSection
                                                   {
                                                       SERIAL = s.SerialNumber,
                                                   })
                                                   .ToList()
                                                   .Where(s => states == null || states.Contains(s.State))
                                                   .ToList(),
                AGGREGATION = this.BatchAggregations
                                                 .Select(a => new SgXmlEvent.Serial
                                                 {
                                                     Code = a.SerialNumber,
                                                     Timestamp = a.TimestampCreated,
                                                     ItemCount = a.ItemCount.HasValue ? a.ItemCount.Value : 0,
                                                     ItemLevel = a.ItemLevel,
                                                     ItemType = a.ItemLevel,
                                                     Level = a.Level,
                                                     Type = a.CodeType,
                                                     Items = a.BatchAggregationItems
                                                             .Select(ai => new SgXmlEvent.Serial.Item
                                                             {
                                                                 Value = ai.SerialNumber
                                                             })
                                                             .ToList()
                                                 })
                                                 .ToList()
            };


            return sgXmlEvent;
        }
    }

    public enum BatchStateEnum
    {
        NEW,
        PARTIALLY_REPORTED,
        REPORTED
    }
}
