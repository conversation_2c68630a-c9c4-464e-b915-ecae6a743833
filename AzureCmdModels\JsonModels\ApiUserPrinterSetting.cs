﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace AzureCmdModels.JsonModels
{
    public class ApiUserPrinterSetting
    {
        public ApiUserPrinterSetting()
        {

        }

        public UserPrinterSetting ToUserPrinterSetting() 
        {
            UserPrinterSetting userPrinterSetting = new UserPrinterSetting
            {
                LgnName = this.LgnName,
                UseNetworkPrinter = this.UseNetworkPrinter,
                NetworkPrinterIP = this.NetworkPrinterIP,
                NetworkPrinterPort = this.NetworkPrinterPort,
                PrinterDriverName = this.PrinterDriverName,
                PrinterLanguageType = this.PrinterLanguageType
            };

            return userPrinterSetting;
        }

        [DataMember(Name = "lgnName")]
        public string LgnName { get; set; }

        [DataMember(Name = "useNetworkPrinter")]
        public bool UseNetworkPrinter { get; set; }

        [StringLength(20)]
        [RegularExpression("^(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$")]
        [DataMember(Name = "networkPrinterIP")]
        public string NetworkPrinterIP { get; set; }

        [DataMember(Name = "networkPrinterPort")]
        public int? NetworkPrinterPort { get; set; }
        
        [StringLength(250)]
        [DataMember(Name = "printerDriverName")]
        public string PrinterDriverName { get; set; }
        
        [StringLength(8)]
        [DataMember(Name = "printerLanguageType")]
        public string PrinterLanguageType { get; set; }
    }
}
