namespace AS2Test.Models
{
    /// <summary>
    /// Represents a response from a service call.
    /// </summary>
    /// <typeparam name="T">The type of the data returned by the service.</typeparam>
    public class ServiceResponse<T>
    {
        public bool IsSuccess { get; set; }
        public T? As2ServerResponse { get; set; }
        public string Message { get; set; }
        public T? Error { get; set; }
        public string ErrorMessage { get; set; }
        
    }
}