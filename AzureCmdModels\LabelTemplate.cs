﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureCmdModels
{
    [Table("LabelTemplate")]
    public class LabelTemplate
    {
        [Key]
        public int IDLabelTemplate { get; set; }

        [Required]
        public int IDCustomer { get; set; }

        [Required]
        public string Label { get; set; }

        [Required]
        [StringLength(255)]
        public string LabelName { get; set; }

        public string Unit { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampCreated { get; set; }

        [Required]
        public string UserCreated { get; set; }


        [InverseProperty("LabelTemplates")]
        public Customer Customer { get; set; }

    }
}
