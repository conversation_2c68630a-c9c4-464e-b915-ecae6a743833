﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureBhModels
{
    [Table("BatchAggregation")]
    public class BatchAggregation
    {
        public BatchAggregation()
        {
            BatchAggregationItems = new HashSet<BatchAggregationItem>();
        }

        [Key]
        public long IDBatchAggregation { get; set; }

        [Required]
        public int IDBatch { get; set; }

        public int? IDSgFile { get; set; }

        [Required]
        [StringLength(100)]
        public string Level { get; set; }

        [Required]
        [StringLength(20)]
        public string CodeType { get; set; }

        [Required]
        [StringLength(100)]
        public string SerialNumber { get; set; }

        [Required]
        [StringLength(100)]
        public string ItemLevel { get; set; }

        [StringLength(50)]
        public string ItemType { get; set; }

        [StringLength(14)]
        public string TypeCode { get; set; }

        [Required]
        public int? ItemCount { get; set; }

        public bool IsReported { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        public string UserCreated { get; set; }

        [InverseProperty("BatchAggregations")]
        public Batch Batch { get; set; }

        [InverseProperty("BatchAggregations")]
        public SgFile SgFile { get; set; }

        [InverseProperty("BatchAggregation")]
        public ICollection<BatchAggregationItem> BatchAggregationItems { get; set; }

    }
}
