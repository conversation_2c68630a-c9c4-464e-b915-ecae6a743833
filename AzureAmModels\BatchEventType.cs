﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureAmModels
{
    [Table("BatchEventType")]
    public class BatchEventType
    {

        public BatchEventType()
        {
            BatchEvents = new HashSet<BatchEvent>();
        }

        [Key]
        public Int16 IDBatchEventType { get; set; }

        [Required]
        public string Name { get; set; }

        public string Description { get; set; }

        [InverseProperty("BatchEventType")]
        public ICollection<BatchEvent> BatchEvents { get; set; }

    }
}
