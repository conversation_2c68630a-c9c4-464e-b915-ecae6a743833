﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureCmdModels
{
    public class ViewMessages
    {
        public string ErrorMsg { get; set; }

        public string InfoMsg { get; set; }
    }

    public class PaginaitonViewModel
    {
        public string MethodName { get; set; }
        public bool IsSubmit { get; set; }
        public int CurrentPage { get; set; }
        public int Pages { get; set; }
        public string SearchStr { get; set; }
    }

    public class ContractsViewModel
    {
        public ContractsViewModel()
        {
            ServiceViewModelList = new List<ServiceViewModelItem>();
        }
        public Contract Contract { get; set; }

        public List<ServiceViewModelItem> ServiceViewModelList { get; set; }
    }

    public class ServiceViewModel
    {
        public ServiceViewModel()
        {
            ServiceViewModelList = new List<ServiceViewModelItem>();
        }

        public ServiceViewModel(Customer customer)
        {
            ServiceViewModelList = new List<ServiceViewModelItem>();
            this.Customer = customer;
        }



        public Customer Customer { get; set; }

        public List<ServiceViewModelItem> ServiceViewModelList { get; set; }
    }

    public class ServiceViewModelItem
    {
        public ServiceViewModelItem()
        {

        }
        public ServiceViewModelItem(int idCustomer, int idService, string name)
        {
            this.IDService = idService;
            this.IDCustomer = idCustomer;
            this.Name = name;
        }

        public int IDService { get; set; }

        public bool IsSelected { get; set; }

        public string Name { get; set; }

        public int IDServiceContract { get; set; }

        public int IDCustomer { get; set; }

    }

    public class UserLoginServiceRoleViewModel
    {
        public UserLoginServiceRoleViewModel(List<ServiceRoleViewModel> serviceRoleViewModels, List<CustomerServiceViewModel> customerServiceViewModels)
        {
            if (serviceRoleViewModels != null)
                ServiceRoleViewModelList = serviceRoleViewModels;
            else
                ServiceRoleViewModelList = new List<ServiceRoleViewModel>();

            if (customerServiceViewModels != null)
                CustomerServices = customerServiceViewModels;
            else
                CustomerServices = new List<CustomerServiceViewModel>();
        }

        public UserLoginServiceRoleViewModel()
        {
            ServiceRoleViewModelList = new List<ServiceRoleViewModel>();
            CustomerServices = new List<CustomerServiceViewModel>();
        }

        public UserLogin UserLogin { get; set; }

        public List<ServiceRoleViewModel> ServiceRoleViewModelList { get; set; }

        public List<CustomerServiceViewModel> CustomerServices { get; set; }
    }

    public class ServiceRoleViewModel
    {
        public int IDServiceRole { get; set; }

        public string LgnName { get; set; }

        public string Name { get; set; }

        public int IDUserLoginServiceRole { get; set; }

        public bool IsSelected { get; set; }

    }


    public enum DataSourceProviderEnum
    {
        FILE,
        SATT_PPD,
        SATT_EBR

    }
    public static class DefaultAggregationLevels
    {
        public static List<string> List
        {
            get
            {
                return new List<string>
                {
                    "SERIALIZED UNIT",
                    "CASE",
                    "BUNDLE",
                    "PALLET",
                    "LEVEL-1",
                    "LEVEL-2",
                    "LEVEL-3",
                };
            }
        }
    }

    public class DropDownValue
    {
        public DropDownValue()
        {

        }
        public DropDownValue(string value, string text)
        {
            Value = value;
            Text = text;
        }
        public DropDownValue(int intValue, string text)
        {
            IntValue = intValue;
            Text = text;
        }
        public int IntValue { get; set; }
        public string Value { get; set; }
        public string Text { get; set; }
    }

    public class DbList
    {
        [Key]
        public string Key { get; set; }
        public string Value { get; set; }
    }

    public class DbString
    {
        [Key]
        public string Value { get; set; }
    }

}
