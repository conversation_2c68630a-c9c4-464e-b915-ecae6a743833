﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AzureBhModels;

namespace AzureBhModels
{
    [Table("BatchReport")]
    public class BatchReport
    {
        public BatchReport()
        {
            BatchReportItems = new HashSet<BatchReportItem>();
            BatchReportLogs = new HashSet<BatchReportLog>();
            BatchReportExtendProperties = new HashSet<BatchReportExtendProperty>();
            BatchReportEvents = new HashSet<BatchReportEvent>();

        }

        [Key]
        public int IDBatchReport { get; set; }

        [Required]
        public int IDBatch { get; set; }

        public int IDReceiver { get; set; }
        public int IDSender { get; set; }

        public int SerializedUnitCount { get; set; }
        public int MaxCountOfSerializedUnits { get; set; }

        public int ItemCount { get; set; }

        public ReportStateEnum State { get; set; }

        /// <summary>
        /// Alphanumberic. Starts with yymmdd + GUID
        /// </summary>
        [Required]
        [StringLength(40)]
        public string InstanceIdentifier { get; set; }

        [Required]
        public bool IsAutoPublish { get; set; } //todo

        public DateTime? PublishTimestamp { get; set; }

        [StringLength(1)]
        public string ResponseStatusType { get; set; } //todo - enum?

        public int? ResponseCode { get; set; }

        [StringLength(50)]
        public string ResponseDate { get; set; }

        [StringLength(50)]
        public string MessageID { get; set; }

        //todo: nvarchar max
        public string ResponseStatusReason { get; set; }

        [StringLength(50)]
        public string ResponseStatusCode { get; set; }

        //todo: nvarchar(max)
        public string QueryResponseStatus { get; set; }

        public DateTime? QueryStatusTimestamp { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }


        [InverseProperty("BatchReports")]
        public Receiver Receiver { get; set; }

        [InverseProperty("BatchReports")]
        public Sender Sender { get; set; }

        [InverseProperty("BatchReport")]
        public ICollection<BatchReportItem> BatchReportItems { get; set; }

        [InverseProperty("BatchReport")]
        public ICollection<BatchReportLog> BatchReportLogs { get; set; }

        [InverseProperty("BatchReports")]
        public Batch Batch { get; set; }

        [InverseProperty("BatchReport")]
        public ICollection<BatchReportExtendProperty> BatchReportExtendProperties { get; set; }

        [InverseProperty("BatchReport")]
        public ICollection<BatchReportEvent> BatchReportEvents { get; set; }


        private void SetBatchReportExtendPropertiesNoValues(IQueryable<ExtendProperty> extendProperties, string lgnName)
        {
            DateTime now = DateTime.Now;

            // create BatchExtendProperties by getting all ExtendProperties defined in Settings
            if (extendProperties != null && extendProperties.Count() > 0)
            {
                List<BatchReportExtendProperty> propertiesList = new List<BatchReportExtendProperty>();

                foreach (var extendProperty in extendProperties)
                {
                    string displayTextValue = null;

                    if (extendProperty.IsSelectValue && !string.IsNullOrEmpty(extendProperty.DefaultValue))
                    {
                        if (extendProperty.ExtendPropertySelects != null && extendProperty.ExtendPropertySelects.Count > 0)
                        {
                            var selectValue = extendProperty.ExtendPropertySelects.FirstOrDefault(s => s.Key == extendProperty.DefaultValue);

                            if (selectValue != null)
                                displayTextValue = selectValue.Value;
                        }
                    }

                    propertiesList.Add(new BatchReportExtendProperty()
                    {
                        BatchReportPropertyReference = extendProperty.ReportPropertyReference,
                        IDExtendProperty = extendProperty.IDExtendProperty,
                        IsRequired = extendProperty.IsRequired,
                        Name = extendProperty.Name,
                        Regex = extendProperty.Regex,
                        IsSelectValue = extendProperty.IsSelectValue,
                        Type = extendProperty.Type,
                        TimestampCreated = now,
                        UserCreated = lgnName,
                        StringValue = string.IsNullOrEmpty(extendProperty.DefaultValue) ? null : extendProperty.DefaultValue,
                        DisplayText = displayTextValue

                    });

                }

                this.BatchReportExtendProperties = propertiesList;

            }
        }
    }

    public enum SerialNumberTypeEnum
    {
        SSCC,
        SERIAL
    }



}
