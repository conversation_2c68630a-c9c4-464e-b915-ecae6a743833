﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEbrModels
{
    [Table("TemplateSrcSystem")]
    public class TemplateSrcSystem
    {
        [Key]
        public int IDTemplateSrcSystem { get; set; }

        [Required]
        public int? IDTemplate { get; set; }

        [Required]
        [StringLength(100)]
        public string SystemName { get; set; }

        [Required]
        [StringLength(2)]
        public string SrcSystem { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [InverseProperty("TemplateSrcSystems")]
        public Template Template { get; set; }
    }
}
