﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Diagnostics.Contracts;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SGAuditTrail;

namespace AzureCmdModels
{
    [Table("Customer")]
    [Audit(DisplayName = "Customer")]
    public class Customer
    {
        public Customer()
        {
            Manufacturers = new HashSet<Manufacturer>();
            UserLogins = new HashSet<UserLogin>();
            Products = new HashSet<Product>();
            CustomerServices = new HashSet<CustomerService>();
            Contracts = new HashSet<Contract>();
            CustomerContracts = new HashSet<CustomerContract>();
            CustomerPartners = new HashSet<CustomerPartner>();
            CustomerAggregationLevels = new HashSet<CustomerAggregationLevel>();
            LabelTemplates = new HashSet<LabelTemplate>();
            CustomerLabelDataSources = new HashSet<CustomerLabelDataSource>();
            FileWatcherConfigs = new HashSet<FileWatcherConfig>();
        }

        [Key]
        public int IDCustomer { get; set; }

        [Required]
        [StringLength(255)]
        [DisplayName("Name")]
        [Audit(DisplayName = "Name")]
        public string Name { get; set; }

        [StringLength(100)]
        [DisplayName("GS1 Company Prefix")]
        [Audit(DisplayName = "GS1 Company Prefix")]
        public string GS1CompanyPrefix { get; set; }


        [StringLength(20)]
        [DisplayName("SGLN")]
        [Audit(DisplayName = "SGLN")]
        public string SGLN { get; set; }

        [StringLength(13)]
        [DisplayName("GLN")]
        [Audit(DisplayName = "GLN")]
        public string GLN { get; set; }

        [Column("IDBusinessSegment")]
        [DisplayName("Business Segment")]
        [Audit(DisplayName = "Business Segment", InversePropertyName = "BusinessSegment", InversePropertyValue = "Segment")]
        [Range(1, int.MaxValue, ErrorMessage = "Select business segment")]
        public int? IDBusinessSegment { get; set; }

        [Column("Code")]
        [StringLength(50)]
        [DisplayName("Code")]
        public string Code { get; set; }

        [StringLength(255)]
        [DisplayName("Street")]
        [Audit(DisplayName = "Street")]
        public string CustomerStreet { get; set; }

        [StringLength(255)]
        [DisplayName("Street 2")]
        [Audit(DisplayName = "Street 2")]
        public string CustomerStreet2 { get; set; }

        [StringLength(100)]
        [DisplayName("City")]
        [Audit(DisplayName = "City")]
        public string CustomerCity { get; set; }

        [StringLength(50)]
        [DisplayName("Post code")]
        [Audit(DisplayName = "Post Code")]
        public string CustomerPostCode { get; set; }

        [Column(TypeName = "char(2)")]
        [DisplayName("Country")]
        [Audit(DisplayName = "Country Code")]
        public string CustomerCountryCode { get; set; }

        [DisplayName("Created")]
        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimeStampCreated { get; set; }

        [StringLength(128)]
        [DisplayName("User Created")]
        public string UserCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimeStampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        [InverseProperty("Customers")]
        [DisplayName("Business Segment")]
        public BusinessSegment BusinessSegment { get; set; }

        [InverseProperty("Customer")]
        public ICollection<UserLogin> UserLogins { get; set; }

        [InverseProperty("Customer")]
        public ICollection<Manufacturer> Manufacturers { get; set; }

        [InverseProperty("Customer")]
        public ICollection<Product> Products { get; set; }

        [InverseProperty("Customer")]
        public ICollection<CustomerService> CustomerServices { get; set; }

        [InverseProperty("Customer")]
        public ICollection<Contract> Contracts { get; set; }

        [InverseProperty("Customer")]
        public ICollection<CustomerContract> CustomerContracts { get; set; }


        [InverseProperty("Customer")]
        public ICollection<CustomerPartner> CustomerPartners { get; set; }

        [InverseProperty("Customer")]
        public ICollection<CustomerAggregationLevel> CustomerAggregationLevels { get; set; }

        [InverseProperty("Customer")]
        public ICollection<LabelTemplate> LabelTemplates { get; set; }

        [InverseProperty("Customer")]
        public ICollection<CustomerLabelDataSource> CustomerLabelDataSources { get; set; }

        [InverseProperty("Customer")]
        public ICollection<FileWatcherConfig> FileWatcherConfigs { get; set; }

        public AuditTrailHeader GetAuditTrailHeader(string user, int page, int pages)
        {
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

            AuditTrailHeader header = new AuditTrailHeader()
            {
                Controler = "Customers",
                ID = IDCustomer.ToString(),
                UserName = user,
                Page = page,
                Pages = pages
            };

            header.AddItem("Customer", Name);
            header.AddItem("User", user);
            header.AddItem("Timestamp", timestamp);

            return header;
        }

        public CustomerContractDetail GetContractDetail()
        {
            return new CustomerContractDetail(this);
        }

    }

    public class CustomerContractDetail
    {
        public string CustomerName { get; set; }
        public bool IsError { get; set; }
        public bool IsInfo { get; set; }
        public bool IsWarning { get; set; }

        public List<ServiceDetail> Error { get; set; }
        public List<ServiceDetail> Info { get; set; }
        public List<ServiceDetail> Warning { get; set; }

        public CustomerContractDetail(Customer customer)
        {
            Error = new List<ServiceDetail>();
            Info = new List<ServiceDetail>();
            Warning = new List<ServiceDetail>();

            CustomerName = customer.Name;

            if (customer.CustomerContracts != null)
            {
                var csds = customer.CustomerContracts
                    .Where(s => !s.IsCanceled && ((!s.EndDate.HasValue)
                                || (s.EndDate.HasValue && s.EndDate.Value >= DateTime.Now.Date)
                                || (s.IsTrial && s.TrialEndDate.HasValue && s.TrialEndDate.Value > DateTime.Now.Date)))
                    .Select(s => new ServiceDetail(s))
                    .ToList();

                if (csds != null && csds.Count > 0)
                {
                    foreach (var item in csds)
                    {
                        if (item.IsError)
                        {
                            IsError = true;
                            Error.Add(item);
                        }

                        if (item.IsInfo)
                        {
                            IsInfo = true;
                            Info.Add(item);
                        }

                        if (item.IsWarning)
                        {
                            IsWarning = true;
                            Warning.Add(item);
                        }
                    }
                }
            }
        }
    }

    public class ServiceDetail
    {
        public string Name { get; set; }
        public int IDCustomerContract { get; set; }
        public string Subscription { get; set; }

        public bool IsError { get; set; }
        public bool IsInfo { get; set; }
        public bool IsWarning { get; set; }

        public List<string> InfoMessage { get; set; }
        public List<string> ErrorMessage { get; set; }
        public List<string> WarningMessage { get; set; }

        public ServiceDetail(CustomerContract contract)
        {
            InfoMessage = new List<string>();
            ErrorMessage = new List<string>();
            WarningMessage = new List<string>();

            Name = contract.Name;
            IDCustomerContract = contract.IDCustomerContract;
            Subscription = contract.StartDate.ToString("yyyy-MM-dd") + " - " + (contract.EndDate.HasValue ? contract.EndDate.Value.ToString("yyyy-MM-dd") : "End not specified");

            if (contract.IsTrial && contract.TrialEndDate.HasValue)
            {
                if (contract.TrialEndDate.Value < DateTime.Now)
                {
                    IsError = true;
                    ErrorMessage.Add(string.Format("Trial period ({0}) ends. Please contact SoftGroup sales department. ", contract.TrialEndDate.Value.ToString("yyyy-MM-dd")));
                }
                else
                {
                    IsInfo = true;
                    InfoMessage.Add(string.Format("Trial version ends after {0} days", (int)(contract.TrialEndDate.Value - DateTime.Now).TotalDays));
                }
            }

            if (!contract.IsContract)
            {
                if (contract.ContractTerm.HasValue)
                {
                    int ctdays = (int)(contract.ContractTerm.Value - DateTime.Now).TotalDays;
                    if (ctdays < 0)
                    {
                        IsError = true;
                        ErrorMessage.Add(string.Format("Contract term ends {0} days ago. Please contact SoftGroup sales departent. ", Math.Abs(ctdays)));
                    }
                    else if (ctdays < contract.ContractNotifyEndDays)
                    {
                        IsWarning = true;
                        WarningMessage.Add(string.Format("Contract term ends after {0} days. Contract term - {1}. ", Math.Abs(ctdays), contract.ContractTerm.Value.ToString("yyyy-MM-dd")));
                    }
                    else
                    {
                        IsInfo = true;
                        InfoMessage.Add(string.Format("Contract not signed. Contract term - {0}. ", contract.ContractTerm.Value.ToString("yyyy-MM-dd")));
                    }
                }
            }
            else if (!contract.IsPayed)
            {
                if (contract.PaymentTerm.HasValue)
                {
                    int ptdays = (int)(contract.PaymentTerm.Value - DateTime.Now).TotalDays;
                    if (ptdays < 0)
                    {
                        IsError = true;
                        ErrorMessage.Add(string.Format("Payment term ends {0} days ago. Please contact SoftGroup sales departent. ", Math.Abs(ptdays)));
                    }
                    else if (ptdays < contract.PaymentNotifyDays)
                    {
                        IsWarning = true;
                        WarningMessage.Add(string.Format("Payment term ends after {0} days. Payment term - {1}. ", Math.Abs(ptdays), contract.PaymentTerm.Value.ToString("yyyy-MM-dd")));
                    }
                    else
                    {
                        IsInfo = true;
                        InfoMessage.Add(string.Format("Contract not payed. Payment term - {0}. ", contract.PaymentTerm.Value.ToString("yyyy-MM-dd")));
                    }
                }
                else
                {
                    IsInfo = true;
                    InfoMessage.Add("Subscription payment missing. ");
                }
            }
        }
    }
}
