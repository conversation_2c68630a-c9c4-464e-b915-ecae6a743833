using AzureSnxModels;
using Microsoft.Identity.Client;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Security;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Web;
using GSNXSCDataTransfer.CustomModels;
using static GSNXSCDataTransfer.CustomModels.SnxApi;

namespace GSNXSCDataTransfer
{
    public class GsnxApiClient
    {
        private HttpClient httpClient;
        private IPublicClientApplication app;

        private string[] Scopes
        {
            get
            {
                return new string[] { scope };
            }
        }

        private readonly string scope;
        private readonly string user;
        private readonly string password;

        public GsnxApiClient(AzureConnectionParams eventHistoryClientParams)
        {
            app = PublicClientApplicationBuilder.Create(eventHistoryClientParams.ClientId)
                .WithAuthority(eventHistoryClientParams.Authority)
                .Build();

            httpClient = new HttpClient()
            {
                BaseAddress = new Uri(eventHistoryClientParams.Url),
                Timeout = new TimeSpan(0, 10, 0),
            };

            scope = eventHistoryClientParams.Scope;
            user = eventHistoryClientParams.User;
            password = eventHistoryClientParams.Password;
        }


        public async Task<HttpResponseMessage> CreatePool(AzureGsnxApiModels.ApiSnxImport snxImport,
                                           string serializationTypeEnum,
                                           int? idCustomer,
                                           string username)
        {
            if (await GetAcessToken())
            {

                var json = JsonSerializer.Serialize(snxImport);
                var stringContent = new StringContent(json, UnicodeEncoding.UTF8, "application/json");
                HttpResponseMessage response;
                if (idCustomer.HasValue)
                    response = await httpClient.PostAsync($"api/v1/CreatePool/{serializationTypeEnum}/{idCustomer.Value}/{HttpUtility.UrlEncode(username.Replace(@"SATT\", ""))}", stringContent);
                else
                    response = await httpClient.PostAsync($"api/v1/CreatePool/{serializationTypeEnum}", stringContent);

                return response;


            }
            else
                throw new Exception("Get access token failed");

        }

        public async Task<List<ProductExtended>> GetProductList()
        {
            if (await GetAcessToken())
            {
                HttpResponseMessage response = await httpClient.GetAsync($"api/v1/GetProductList");

                if (response.IsSuccessStatusCode)
                {
                    //Response is correct
                    var responseString = await response.Content.ReadAsStringAsync();
                    if (!string.IsNullOrEmpty(responseString))
                    {
                        var products = JsonSerializer.Deserialize<List<ProductExtended>>(responseString);
                        return products;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    //Server return error
                    var responseString = await response.Content.ReadAsStringAsync();

                    if (!string.IsNullOrEmpty(responseString))
                    {
                        ErrorResultJson error = null;
                        try
                        {
                            error = JsonSerializer.Deserialize<ErrorResultJson>(responseString);
                        }
                        catch (Exception)
                        {

                        }

                        if (error != null)
                            throw new Exception(response.StatusCode.ToString() + " " + error.Message);

                    }

                    string exception = response.StatusCode.ToString() + " " + response.ReasonPhrase;
                    throw new Exception(exception);
                }
            }
            else
                throw new Exception("Get access token failed");

        }


        public async Task<string> SetProduct(ApiProduct product)
        {

            if (await GetAcessToken())
            {
                var json = JsonSerializer.Serialize(product);
                var stringContent = new StringContent(json, UnicodeEncoding.UTF8, "application/json");

                var response = await httpClient.PostAsync($"api/v1/SetProduct", stringContent);

                if (response.IsSuccessStatusCode)
                {
                    //Response is correct
                    var responseString = await response.Content.ReadAsStringAsync();
                    if (!string.IsNullOrEmpty(responseString))
                    {
                        return responseString;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    //Server return error
                    var responseString = await response.Content.ReadAsStringAsync();

                    if (!string.IsNullOrEmpty(responseString))
                        throw new Exception(responseString);

                    string exception = response.StatusCode.ToString() + " " + response.ReasonPhrase;
                    throw new Exception(exception);
                }
            }
            else
                throw new Exception("Get access token failed");
        }

        private async Task<bool> GetAcessToken()
        {
            var accounts = await app.GetAccountsAsync();

            AuthenticationResult result;
            if (accounts.Any())
            {
                result = await app.AcquireTokenSilent(Scopes, accounts.FirstOrDefault()).ExecuteAsync();
            }
            else
            {
                result = await app.AcquireTokenByUsernamePassword(Scopes, user, password).ExecuteAsync();
            }
            if (result == null || string.IsNullOrEmpty(result.AccessToken))
                throw new Exception("GetAccessToken failed");

            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", result.AccessToken);
            return true;
        }

        private static SecureString GetSecurePassword(string password)
        {
            var securePassword = new SecureString();
            foreach (char c in password)
                securePassword.AppendChar(c);

            return securePassword;
        }

    }
}
