﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEbrModels
{
    [Table("BatchState")]
    public partial class BatchState
    {
        public BatchState()
        {
            Batches = new HashSet<Batch>();
            MachinesInProgress = new HashSet<Machine>();
        }

        [Key]
        [DisplayName("ID")]
        public int IDBatchState { get; set; }

        [Required]
        [DisplayName("Index")]
        public int ListIndex { get; set; }

        [Required]
        [StringLength(100)]
        [DisplayName("Name")]
        public string Name { get; set; }

        [StringLength(200)]
        [DisplayName("Description")]
        public string ShortDescription { get; set; }

        [Required]
        [DisplayName("Initial State")]
        public bool IsInitialState { get; set; }

        [DisplayName("Line visible")]
        public bool IsLineVisible { get; set; }

        [StringLength(256)]
        [DisplayName("Read Group")]
        public string ReadGroup { get; set; }

        [StringLength(256)]
        [DisplayName("Edit Group")]
        public string EditGroup { get; set; }

        [StringLength(256)]
        [DisplayName("Transition Group")]
        public string TransitionGroup { get; set; }

        [Required]
        [DisplayName("Transition Reason Required")]
        public bool IsTransitionReasonRequired { get; set; }

        [Required]
        [DisplayName("Serial Numbers Request Allowed")]
        public bool IsSerialNumbersRequestAllowed { get; set; }

        [StringLength(256)]
        [DisplayName("Picture")]
        public string Picture { get; set; }

        [Required]
        [DisplayName("Timestamp created")]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        [DisplayName("User Created")]
        public string UserCreated { get; set; }

        [DisplayName("Timestamp Updated")]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        [DisplayName("User Updated")]
        public string UserUpdated { get; set; }


        [StringLength(200)]
        public string InVerification { get; set; }

        [StringLength(200)]
        public string OutVerification { get; set; }

        [InverseProperty("BatchState")]
        public ICollection<Batch> Batches { get; set; }

        [InverseProperty("BatchStateInProgress")]
        public ICollection<Machine> MachinesInProgress { get; set; }

        [InverseProperty("BatchStateTarget")]
        public ICollection<BatchStateTransition> BatchStateTransitionsTarget { get; set; }

        [InverseProperty("BatchStateInitial")]
        public ICollection<BatchStateTransition> BatchStateTransitionsInitial { get; set; }
    }
}
