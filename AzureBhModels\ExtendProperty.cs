﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureBhModels
{
    [Table("ExtendProperty")]
    public class ExtendProperty
    {

        [Key]
        public int IDExtendProperty { get; set; }

        [Required]
        [StringLength(128)]
        public string Name { get; set; }

        public bool IsRequired { get; set; }

        public bool IsSelectValue { get; set; }

        [Required]
        public ExtendPropertyEnum Type { get; set; }


        [StringLength(1000)]
        public string Regex { get; set; }


        [Required]
        [StringLength(128)]
        public string ReportPropertyReference { get; set; }

        [StringLength(50)]
        public string DefaultValue { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        [InverseProperty("ExtendProperty")]
        public ICollection<ExtendPropertySelect> ExtendPropertySelects { get; set; }

    }
}
public enum ExtendPropertyEnum
{
    STRING,
    INT,
    DECIMAL,
    DATETIME,
    BOOLEAN,
    DATE,
    BIGINT
}

