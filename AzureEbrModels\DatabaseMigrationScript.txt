﻿

Anton Добавена настройка за принтирането на етикетите на агрегационите нива заедно с динамичните полета към тях
select * from AppSetting

--insert into AppSetting ([Name], [Type], [Value], TimestampCreated, UserCreated)
--values('AggregationLevelSource', 0, 'CMD', GETDATE(), '<EMAIL>')

--------------START EBR Templates 18-03-2024 Anton--------------------------
CREATE TABLE [dbo].[Template](
	[IDTemplate] [int] IDENTITY(1,1) NOT NULL,
	[Name] [nvarchar](100) NOT NULL,
	[Description] [nvarchar](1000) NULL,
	[IsActive] [bit] NOT NULL,
	[TimestampCreated] [datetime] NOT NULL,
	[UserCreated] [nvarchar](128) NOT NULL,
	[TimestampUpdated] [datetime] NULL,
	[UserUpdated] [nvarchar](128) NULL,
 CONSTRAINT [PK_Template] PRIMARY KEY CLUSTERED 
(
	[IDTemplate] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[Template] ADD  DEFAULT ((0)) FOR [IsActive]
GO

ALTER TABLE [dbo].[Template] ADD  DEFAULT (getdate()) FOR [TimestampCreated]
GO

CREATE TABLE [dbo].[TemplateExtendProperty](
	[IDTemplateExtendProperty] [int] IDENTITY(1,1) NOT NULL,
	[IDTemplate] [int] NULL,
	[IDTemplateLevel] [int] NULL,
	[IDExtendProperty] [int] NOT NULL,
	[TimestampCreated] [datetime] NOT NULL,
	[UserCreated] [nvarchar](128) NOT NULL,
 CONSTRAINT [PK_TemplateExtendProperty] PRIMARY KEY CLUSTERED 
(
	[IDTemplateExtendProperty] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[TemplateExtendProperty] ADD  DEFAULT (getdate()) FOR [TimestampCreated]
GO

ALTER TABLE [dbo].[TemplateExtendProperty]  WITH CHECK ADD  CONSTRAINT [FK_TemplateExtendProperty_Template] FOREIGN KEY([IDTemplate])
REFERENCES [dbo].[Template] ([IDTemplate])
GO

ALTER TABLE [dbo].[TemplateExtendProperty] CHECK CONSTRAINT [FK_TemplateExtendProperty_Template]
GO

ALTER TABLE [dbo].[TemplateExtendProperty]  WITH CHECK ADD  CONSTRAINT [FK_TemplateExtendProperty_TemplateLevel] FOREIGN KEY([IDTemplateLevel])
REFERENCES [dbo].[TemplateLevel] ([IDTemplateLevel])
GO

ALTER TABLE [dbo].[TemplateExtendProperty] CHECK CONSTRAINT [FK_TemplateExtendProperty_TemplateLevel]
GO

CREATE TABLE [dbo].[TemplateLevel](
	[IDTemplateLevel] [int] IDENTITY(1,1) NOT NULL,
	[IDTemplate] [int] NOT NULL,
	[Name] [nvarchar](20) NOT NULL,
	[Type] [nvarchar](20) NOT NULL,
	[SerialType] [nvarchar](10) NOT NULL,
	[SubLevel] [nvarchar](20) NULL,
	[SubLevelType] [nvarchar](10) NULL,
	[UnitCapacity] [int] NULL,
	[IsSerialization] [bit] NOT NULL,
	[TimestampCreated] [datetime] NOT NULL,
	[UserCreated] [nvarchar](128) NOT NULL,
	[TimestampUpdated] [datetime] NULL,
	[UserUpdated] [nvarchar](128) NULL,
 CONSTRAINT [PK_TemplateLevel] PRIMARY KEY CLUSTERED 
(
	[IDTemplateLevel] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[TemplateLevel] ADD  DEFAULT ((0)) FOR [HasSerialization]
GO

ALTER TABLE [dbo].[TemplateLevel] ADD  DEFAULT (getdate()) FOR [TimestampCreated]
GO

ALTER TABLE [dbo].[TemplateLevel]  WITH CHECK ADD  CONSTRAINT [FK_TemplateLevel_Template] FOREIGN KEY([IDTemplate])
REFERENCES [dbo].[Template] ([IDTemplate])
GO

ALTER TABLE [dbo].[TemplateLevel] CHECK CONSTRAINT [FK_TemplateLevel_Template]
GO

CREATE TABLE [dbo].[TemplateLevelFile](
	[IDTemplateLevelFile] [int] IDENTITY(1,1) NOT NULL,
	[IDTemplate] [int] NULL,
	[IDTemplateLevel] [int] NOT NULL,
	[FileName] nvarchar(255) not null,
	[Type] [nvarchar](20) NOT NULL,
	[FileType] [nvarchar](10) NOT NULL,
	[Data] [nvarchar](max) NOT NULL,
	[TimestampCreated] [datetime] NOT NULL,
	[UserCreated] [nvarchar](128) NOT NULL,
	[TimestampUpdated] [datetime] NULL,
	[UserUpdated] [nvarchar](128) NULL,
 CONSTRAINT [PK_TemplateLevelFile] PRIMARY KEY CLUSTERED 
(
	[IDTemplateLevelFile] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [dbo].[TemplateLevelFile] ADD  DEFAULT (getdate()) FOR [TimestampCreated]
GO

ALTER TABLE [dbo].[TemplateLevelFile]  WITH CHECK ADD  CONSTRAINT [FK_TemplateLevelFile_TemplateLevel] FOREIGN KEY([IDTemplateLevel])
REFERENCES [dbo].[TemplateLevel] ([IDTemplateLevel])
GO

ALTER TABLE [dbo].[TemplateLevelFile] CHECK CONSTRAINT [FK_TemplateLevelFile_TemplateLevel]
GO

ALTER TABLE [dbo].[TemplateLevelFile]  WITH CHECK ADD  CONSTRAINT [FK_TemplateLevelFile_Template] FOREIGN KEY([IDTemplate])
REFERENCES [dbo].[Template] ([IDTemplate])
GO

ALTER TABLE [dbo].[TemplateLevelFile] CHECK CONSTRAINT [FK_TemplateLevelFile_Template]
GO

CREATE TABLE [dbo].[TemplateLine](
	[IDTemplateLine] [int] IDENTITY(1,1) NOT NULL,
	[IDTemplate] [int] NOT NULL,
	[IDLine] [int] NOT NULL,
	[TimestampCreated] [datetime] NOT NULL,
	[UserCreated] [nvarchar](128) NOT NULL,
 CONSTRAINT [PK_TemplateLine] PRIMARY KEY CLUSTERED 
(
	[IDTemplateLine] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[TemplateLine] ADD  DEFAULT (getdate()) FOR [TimestampCreated]
GO

ALTER TABLE [dbo].[TemplateLine]  WITH CHECK ADD  CONSTRAINT [FK_TemplateLine_Template] FOREIGN KEY([IDTemplate])
REFERENCES [dbo].[Template] ([IDTemplate])
GO

ALTER TABLE [dbo].[TemplateLine] CHECK CONSTRAINT [FK_TemplateLine_Template]
GO

CREATE TABLE [dbo].[TemplateSrcSystem](
	[IDTemplateSrcSystem] [int] IDENTITY(1,1) NOT NULL,
	[IDTemplate] [int] NOT NULL,
	[SystemName] [nvarchar](100) NOT NULL,
	[SrcSystem] [nvarchar](2) NOT NULL,
	[TimestampCreated] [datetime] NOT NULL,
	[UserCreated] [nvarchar](128) NOT NULL,
 CONSTRAINT [PK_TemplateSrcSystem] PRIMARY KEY CLUSTERED 
(
	[IDTemplateSrcSystem] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[TemplateSrcSystem] ADD  DEFAULT (getdate()) FOR [TimestampCreated]
GO

ALTER TABLE [dbo].[TemplateSrcSystem]  WITH CHECK ADD  CONSTRAINT [FK_TemplateSrcSystem_Template] FOREIGN KEY([IDTemplate])
REFERENCES [dbo].[Template] ([IDTemplate])
GO

ALTER TABLE [dbo].[TemplateSrcSystem] CHECK CONSTRAINT [FK_TemplateSrcSystem_Template]
GO

CREATE TABLE [dbo].[TemplateTargetMarket](
	[IDTemplateTargetMarket] [int] IDENTITY(1,1) NOT NULL,
	[IDTemplate] [int] NOT NULL,
	[Country] [nvarchar](100) NOT NULL,
	[CountryCode] [nvarchar](2) NOT NULL,
	[TimestampCreated] [datetime] NOT NULL,
	[UserCreated] [nvarchar](128) NOT NULL,
 CONSTRAINT [PK_TemplateTargetMarket] PRIMARY KEY CLUSTERED 
(
	[IDTemplateTargetMarket] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[TemplateTargetMarket] ADD  DEFAULT (getdate()) FOR [TimestampCreated]
GO

ALTER TABLE [dbo].[TemplateTargetMarket]  WITH CHECK ADD  CONSTRAINT [FK_TemplateTargetMarket_Template] FOREIGN KEY([IDTemplate])
REFERENCES [dbo].[Template] ([IDTemplate])
GO

ALTER TABLE [dbo].[TemplateTargetMarket] CHECK CONSTRAINT [FK_TemplateTargetMarket_Template]
GO

alter table ExtendProperty add IsTemplate bit not null default(0)
go

insert into appsetting ([Name], [Type], [Value], timestampcreated, usercreated)
values ('EbrTemplateRequired', 4, 'True', getdate(), '<EMAIL>')
go

alter table BatchSerialization add IDTemplateLevel int null
go

alter table BatchAggregation add IDTemplateLevel int null
go

alter table Batch add IDTemplate int null
go

alter table templatelevel add IDAggregationLevel int null
go

alter table TemplateLevelFile add IsActive bit not null default(0)
go

alter table TemplateLevel add IsActive bit not null default(0)
go

alter table ExtendProperty add [Level] nvarchar(50) null
go

alter table ExtendProperty add [SubLevel] nvarchar(50) null
go

alter table Batch add ProductionQuantity int null
go

CREATE TABLE [dbo].[BatchTemplateLevelFile](
	[IDBatchTemplateLevelFile] [int] IDENTITY(1,1) NOT NULL,
	[IDBatch] [int] NULL,
	[IDBatchSerialization] [int] NULL,
	[IDBatchAggregation] [int] NULL,
	[IDTemplateLevelFile] [int] NULL,
	[TimestampCreated] [datetime] NOT NULL,
	[UserCreated] [nvarchar](128) NOT NULL,
 CONSTRAINT [PK_BatchTemplateLevelFile] PRIMARY KEY CLUSTERED 
(
	[IDBatchTemplateLevelFile] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[BatchTemplateLevelFile] ADD  DEFAULT (getdate()) FOR [TimestampCreated]
GO

ALTER TABLE [dbo].[BatchTemplateLevelFile]  WITH CHECK ADD  CONSTRAINT [FK_BatchTemplateLevelFile_Batch] FOREIGN KEY([IDBatch])
REFERENCES [dbo].[Batch] ([IDBatch])
GO

ALTER TABLE [dbo].[BatchTemplateLevelFile] CHECK CONSTRAINT [FK_BatchTemplateLevelFile_Batch]
GO

ALTER TABLE [dbo].[BatchTemplateLevelFile]  WITH CHECK ADD  CONSTRAINT [FK_BatchTemplateLevelFile_BatchAggregation] FOREIGN KEY([IDBatchAggregation])
REFERENCES [dbo].[BatchAggregation] ([IDBatchAggregation])
GO

ALTER TABLE [dbo].[BatchTemplateLevelFile] CHECK CONSTRAINT [FK_BatchTemplateLevelFile_BatchAggregation]
GO

ALTER TABLE [dbo].[BatchTemplateLevelFile]  WITH CHECK ADD  CONSTRAINT [FK_BatchTemplateLevelFile_BatchSerialization] FOREIGN KEY([IDBatchSerialization])
REFERENCES [dbo].[BatchSerialization] ([IDBatchSerialization])
GO

ALTER TABLE [dbo].[BatchTemplateLevelFile] CHECK CONSTRAINT [FK_BatchTemplateLevelFile_BatchSerialization]
GO

ALTER TABLE [dbo].[BatchTemplateLevelFile]  WITH CHECK ADD  CONSTRAINT [FK_BatchTemplateLevelFile_TemplateLevelFile] FOREIGN KEY([IDTemplateLevelFile])
REFERENCES [dbo].[TemplateLevelFile] ([IDTemplateLevelFile])
GO

ALTER TABLE [dbo].[BatchTemplateLevelFile] CHECK CONSTRAINT [FK_BatchTemplateLevelFile_TemplateLevelFile]
GO

alter table Templatelevelfile add FileSize int null
go

insert into AppSetting ([Name],[Type], [Value], TimestampCreated, UserCreated)
values ('MnfDateRequired', 4, 'True', dateadd(dd,-1, GETDATE()), '<EMAIL>')
go

alter table Batch alter column ManufacturingDate datetime null
go

insert into AppSetting ([Name],[Type], [Value], TimestampCreated, UserCreated)
values ('Batch.ProductionQty.Required', 4, 'True', dateadd(dd,-1, GETDATE()), '<EMAIL>')
go

--------------END EBR Templates 18-03-2024 Anton--------------------------

--------------START EBR Templates extend properties template value Anton ----------------

alter table [TemplateExtendProperty] add 
  IsTemplateValue bit default 0 not null,
  StringValue nvarchar(100) null,
  DisplayText nvarchar(100) null,
  TimestampUpdated datetime null,
  UserUpdated nvarchar(128) null


 -------------END EBR Templates extend properties template value Anton ----------------


 ---- Stoyan updated PROD 2024-06-23 -------------



 ----- START new SP for EBR API ------------
 CREATE procedure [dbo].[spBatchAggregationSerialSetIDLineRequestSSCC](  
 @IDBatchAggregation int,   
 @IDLineRequest int,   
 @Count int)  
as  
begin  
set nocount on;  
 update top (@Count) BatchAggregationSerial  
 set IDLineRequest = @IDLineRequest , [State] = 1  
 where IDBatchAggregation = @IDBatchAggregation   
  and [State] = 0  
  and [IsSGTIN] = 0
  and IDLineRequest is null;  
   
 declare @res int = @@ROWCOUNT;  
  
 select @res as Result;  
 RETURN @res;  
  
 set nocount off;  
end

go

CREATE procedure [dbo].[spBatchAggregationSerialSetIDLineRequestSGTIN](  
 @IDBatchAggregation int,   
 @IDLineRequest int,   
 @Count int)  
as  
begin  
set nocount on;  
 update top (@Count) BatchAggregationSerial  
 set IDLineRequest = @IDLineRequest , [State] = 1  
 where IDBatchAggregation = @IDBatchAggregation   
  and [State] = 0  
  and [IsSGTIN] = 1
  and IDLineRequest is null;  
   
 declare @res int = @@ROWCOUNT;  
  
 select @res as Result;  
 RETURN @res;  
  
 set nocount off;  
end

go