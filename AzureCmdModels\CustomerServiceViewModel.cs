﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureCmdModels
{
    public class CustomerServiceViewModel : CustomerService
    {
        public bool IsSelected { get; set; }
        public bool ServiceSelected { get; set; }

        public string ExternalSenderCode { get; set; }
        public string ExternalRecieverCode { get; set; }

        public CustomerServiceViewModel()
        {

        }

        public CustomerServiceViewModel(CustomerService customerService, bool isSelected = false)
        {
            this.IsSelected = isSelected;
            this.ServiceSelected = isSelected;

            foreach (var property in customerService.GetType().GetProperties())
            {
                var prop = this.GetType().GetProperty(property.Name);
                if (prop != null)
                    prop.SetValue(this, property.GetValue(customerService));
            }
        }
    }
}
