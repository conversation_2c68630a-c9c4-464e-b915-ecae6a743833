﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureBhModels
{
    [Table("LogisticsReportLog")]
    public class LogisticsReportLog
    {
        [Key]
        public int IDLogisticsReportLog { get; set; }

        public int IDLogisticsReport { get; set; }

        public string Type { get; set; }

        public string Message { get; set; }


        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [InverseProperty("LogisticsReportLogs")]
        public LogisticsReport LogisticsReport { get; set; }
    }
}
