﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureCmdModels
{
    public class ServiceUserLoginSelectViewModel : ServiceUserLogin
    {
        public bool IsSelected { get; set; }
        public bool UserSelected { get; set; }

        public ServiceUserLoginSelectViewModel()
        {

        }

        public ServiceUserLoginSelectViewModel(ServiceUserLogin serviceUserLogin, bool isSelected = false)
        {
            this.IsSelected = isSelected;
            this.UserSelected = isSelected;

            foreach (var property in serviceUserLogin.GetType().GetProperties())
            {
                var prop = this.GetType().GetProperty(property.Name);
                if (prop != null)
                    prop.SetValue(this, property.GetValue(serviceUserLogin));
            }
        }
    }
}
