﻿<?xml version="1.0" encoding="utf-8"?>
<Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
  <EntityContainerMapping StorageEntityContainer="CloudMasterDataStoreContainer" CdmEntityContainer="DBModel">
    <EntitySetMapping Name="AdditionalCodeTypes">
      <EntityTypeMapping TypeName="CloudMasterData.AdditionalCodeType">
        <MappingFragment StoreEntitySet="AdditionalCodeType">
          <ScalarProperty Name="Name" ColumnName="Name" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="AuditTrails">
      <EntityTypeMapping TypeName="CloudMasterData.AuditTrail">
        <MappingFragment StoreEntitySet="AuditTrail">
          <ScalarProperty Name="IDAuditTrail" ColumnName="IDAuditTrail" />
          <ScalarProperty Name="Action" ColumnName="Action" />
          <ScalarProperty Name="IDTable" ColumnName="IDTable" />
          <ScalarProperty Name="TableName" ColumnName="TableName" />
          <ScalarProperty Name="Timestamp" ColumnName="Timestamp" />
          <ScalarProperty Name="User" ColumnName="User" />
          <ScalarProperty Name="DisplayName" ColumnName="DisplayName" />
          <ScalarProperty Name="IDTableDetail" ColumnName="IDTableDetail" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="AuditTrailItems">
      <EntityTypeMapping TypeName="CloudMasterData.AuditTrailItem">
        <MappingFragment StoreEntitySet="AuditTrailItem">
          <ScalarProperty Name="IDAuditTrailItem" ColumnName="IDAuditTrailItem" />
          <ScalarProperty Name="IDAuditTrail" ColumnName="IDAuditTrail" />
          <ScalarProperty Name="FieldName" ColumnName="FieldName" />
          <ScalarProperty Name="OldValue" ColumnName="OldValue" />
          <ScalarProperty Name="NewValue" ColumnName="NewValue" />
          <ScalarProperty Name="DisplayName" ColumnName="DisplayName" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="BusinessSegments">
      <EntityTypeMapping TypeName="CloudMasterData.BusinessSegment">
        <MappingFragment StoreEntitySet="BusinessSegment">
          <ScalarProperty Name="IDBusinessSegment" ColumnName="IDBusinessSegment" />
          <ScalarProperty Name="Segment" ColumnName="Segment" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="CommunicationChannels">
      <EntityTypeMapping TypeName="CloudMasterData.CommunicationChannel">
        <MappingFragment StoreEntitySet="CommunicationChannel">
          <ScalarProperty Name="Type" ColumnName="Type" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="Contracts">
      <EntityTypeMapping TypeName="CloudMasterData.Contract">
        <MappingFragment StoreEntitySet="Contract">
          <ScalarProperty Name="IDContract" ColumnName="IDContract" />
          <ScalarProperty Name="IDCustomer" ColumnName="IDCustomer" />
          <ScalarProperty Name="Name" ColumnName="Name" />
          <ScalarProperty Name="StartDate" ColumnName="StartDate" />
          <ScalarProperty Name="EndDate" ColumnName="EndDate" />
          <ScalarProperty Name="IDContractStatus" ColumnName="IDContractStatus" />
          <ScalarProperty Name="IDContractType" ColumnName="IDContractType" />
          <ScalarProperty Name="MaxUsers" ColumnName="MaxUsers" />
          <ScalarProperty Name="ExpirationWarningDays" ColumnName="ExpirationWarningDays" />
          <ScalarProperty Name="SerialNumberCount" ColumnName="SerialNumberCount" />
          <ScalarProperty Name="ContractNumber" ColumnName="ContractNumber" />
          <ScalarProperty Name="TimestampCreated" ColumnName="TimestampCreated" />
          <ScalarProperty Name="UserCreated" ColumnName="UserCreated" />
          <ScalarProperty Name="TimestampUpdated" ColumnName="TimestampUpdated" />
          <ScalarProperty Name="UserUpdated" ColumnName="UserUpdated" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="ContractStatus">
      <EntityTypeMapping TypeName="CloudMasterData.ContractStatu">
        <MappingFragment StoreEntitySet="ContractStatus">
          <ScalarProperty Name="IDContractStatus" ColumnName="IDContractStatus" />
          <ScalarProperty Name="Name" ColumnName="Name" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="ContractTypes">
      <EntityTypeMapping TypeName="CloudMasterData.ContractType">
        <MappingFragment StoreEntitySet="ContractType">
          <ScalarProperty Name="IDContractType" ColumnName="IDContractType" />
          <ScalarProperty Name="Name" ColumnName="Name" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="Countries">
      <EntityTypeMapping TypeName="CloudMasterData.Country">
        <MappingFragment StoreEntitySet="Country">
          <ScalarProperty Name="Code" ColumnName="Code" />
          <ScalarProperty Name="Name" ColumnName="Name" />
          <ScalarProperty Name="NameSecLng" ColumnName="NameSecLng" />
          <ScalarProperty Name="GS1RNCode" ColumnName="GS1RNCode" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="Customers">
      <EntityTypeMapping TypeName="CloudMasterData.Customer">
        <MappingFragment StoreEntitySet="Customer">
          <ScalarProperty Name="IDCustomer" ColumnName="IDCustomer" />
          <ScalarProperty Name="Name" ColumnName="Name" />
          <ScalarProperty Name="TimestampCreated" ColumnName="TimestampCreated" />
          <ScalarProperty Name="UserCreated" ColumnName="UserCreated" />
          <ScalarProperty Name="TimestampUpdated" ColumnName="TimestampUpdated" />
          <ScalarProperty Name="UserUpdated" ColumnName="UserUpdated" />
          <ScalarProperty Name="GS1CompanyPrefix" ColumnName="GS1CompanyPrefix" />
          <ScalarProperty Name="SGLN" ColumnName="SGLN" />
          <ScalarProperty Name="GLN" ColumnName="GLN" />
          <ScalarProperty Name="CustomerStreet" ColumnName="CustomerStreet" />
          <ScalarProperty Name="CustomerStreet2" ColumnName="CustomerStreet2" />
          <ScalarProperty Name="CustomerCity" ColumnName="CustomerCity" />
          <ScalarProperty Name="CustomerPostCode" ColumnName="CustomerPostCode" />
          <ScalarProperty Name="CustomerCountryCode" ColumnName="CustomerCountryCode" />
          <ScalarProperty Name="Code" ColumnName="Code" />
          <ScalarProperty Name="IDBusinessSegment" ColumnName="IDBusinessSegment" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="CustomerAggregationLevels">
      <EntityTypeMapping TypeName="CloudMasterData.CustomerAggregationLevel">
        <MappingFragment StoreEntitySet="CustomerAggregationLevel">
          <ScalarProperty Name="IDCustomerAggregationLevel" ColumnName="IDCustomerAggregationLevel" />
          <ScalarProperty Name="IDCustomer" ColumnName="IDCustomer" />
          <ScalarProperty Name="IDProduct" ColumnName="IDProduct" />
          <ScalarProperty Name="Name" ColumnName="Name" />
          <ScalarProperty Name="Type" ColumnName="Type" />
          <ScalarProperty Name="SerialType" ColumnName="SerialType" />
          <ScalarProperty Name="UnitCapacity" ColumnName="UnitCapacity" />
          <ScalarProperty Name="SubLevel" ColumnName="SubLevel" />
          <ScalarProperty Name="SubLevelItemType" ColumnName="SubLevelItemType" />
          <ScalarProperty Name="TimestampCreated" ColumnName="TimestampCreated" />
          <ScalarProperty Name="UserCreated" ColumnName="UserCreated" />
          <ScalarProperty Name="TimestampUpdated" ColumnName="TimestampUpdated" />
          <ScalarProperty Name="UserUpdated" ColumnName="UserUpdated" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="CustomerContracts">
      <EntityTypeMapping TypeName="CloudMasterData.CustomerContract">
        <MappingFragment StoreEntitySet="CustomerContract">
          <ScalarProperty Name="IDCustomerContract" ColumnName="IDCustomerContract" />
          <ScalarProperty Name="IDCustomer" ColumnName="IDCustomer" />
          <ScalarProperty Name="Name" ColumnName="Name" />
          <ScalarProperty Name="StartDate" ColumnName="StartDate" />
          <ScalarProperty Name="EndDate" ColumnName="EndDate" />
          <ScalarProperty Name="IsTrial" ColumnName="IsTrial" />
          <ScalarProperty Name="TrialEndDate" ColumnName="TrialEndDate" />
          <ScalarProperty Name="IsContract" ColumnName="IsContract" />
          <ScalarProperty Name="ContractTerm" ColumnName="ContractTerm" />
          <ScalarProperty Name="ContractData" ColumnName="ContractData" />
          <ScalarProperty Name="ContractNotifyEndDays" ColumnName="ContractNotifyEndDays" />
          <ScalarProperty Name="IsPayed" ColumnName="IsPayed" />
          <ScalarProperty Name="PaymentTerm" ColumnName="PaymentTerm" />
          <ScalarProperty Name="PaymentNotifyDays" ColumnName="PaymentNotifyDays" />
          <ScalarProperty Name="InvoiceData" ColumnName="InvoiceData" />
          <ScalarProperty Name="IsCanceled" ColumnName="IsCanceled" />
          <ScalarProperty Name="CancelReason" ColumnName="CancelReason" />
          <ScalarProperty Name="TimestampCreated" ColumnName="TimestampCreated" />
          <ScalarProperty Name="UserCreated" ColumnName="UserCreated" />
          <ScalarProperty Name="TimestampUpdated" ColumnName="TimestampUpdated" />
          <ScalarProperty Name="UserUpdated" ColumnName="UserUpdated" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="CustomerLabelDataSources">
      <EntityTypeMapping TypeName="CloudMasterData.CustomerLabelDataSource">
        <MappingFragment StoreEntitySet="CustomerLabelDataSource">
          <ScalarProperty Name="IDCustomerDataSource" ColumnName="IDCustomerDataSource" />
          <ScalarProperty Name="IDCustomer" ColumnName="IDCustomer" />
          <ScalarProperty Name="Name" ColumnName="Name" />
          <ScalarProperty Name="DataSource" ColumnName="DataSource" />
          <ScalarProperty Name="TimestampCreated" ColumnName="TimestampCreated" />
          <ScalarProperty Name="UserCreated" ColumnName="UserCreated" />
          <ScalarProperty Name="TimestampUpdated" ColumnName="TimestampUpdated" />
          <ScalarProperty Name="UserUpdated" ColumnName="UserUpdated" />
          <ScalarProperty Name="SrcSystemName" ColumnName="SrcSystemName" />
          <ScalarProperty Name="SrcSystem" ColumnName="SrcSystem" />
          <ScalarProperty Name="Unit" ColumnName="Unit" />
          <ScalarProperty Name="SerialType" ColumnName="SerialType" />
          <ScalarProperty Name="Item" ColumnName="Item" />
          <ScalarProperty Name="ItemSerialType" ColumnName="ItemSerialType" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="CustomerPartners">
      <EntityTypeMapping TypeName="CloudMasterData.CustomerPartner">
        <MappingFragment StoreEntitySet="CustomerPartner">
          <ScalarProperty Name="IDCustomerPartner" ColumnName="IDCustomerPartner" />
          <ScalarProperty Name="IDCustomer" ColumnName="IDCustomer" />
          <ScalarProperty Name="Name" ColumnName="Name" />
          <ScalarProperty Name="SystemName" ColumnName="SystemName" />
          <ScalarProperty Name="SNXRequest" ColumnName="SNXRequest" />
          <ScalarProperty Name="SSCCRequest" ColumnName="SSCCRequest" />
          <ScalarProperty Name="EBRImport" ColumnName="EBRImport" />
          <ScalarProperty Name="BatchReport" ColumnName="BatchReport" />
          <ScalarProperty Name="ShipmentReport" ColumnName="ShipmentReport" />
          <ScalarProperty Name="IsActive" ColumnName="IsActive" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="CustomerPartnerParameters">
      <EntityTypeMapping TypeName="CloudMasterData.CustomerPartnerParameter">
        <MappingFragment StoreEntitySet="CustomerPartnerParameter">
          <ScalarProperty Name="IDCustomerPartnerParameter" ColumnName="IDCustomerPartnerParameter" />
          <ScalarProperty Name="IDCustomerPartner" ColumnName="IDCustomerPartner" />
          <ScalarProperty Name="Name" ColumnName="Name" />
          <ScalarProperty Name="Value" ColumnName="Value" />
          <ScalarProperty Name="TimestampCreated" ColumnName="TimestampCreated" />
          <ScalarProperty Name="UserCreated" ColumnName="UserCreated" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="CustomerServices">
      <EntityTypeMapping TypeName="CloudMasterData.CustomerService">
        <MappingFragment StoreEntitySet="CustomerService">
          <ScalarProperty Name="IDCustomerService" ColumnName="IDCustomerService" />
          <ScalarProperty Name="IDCustomer" ColumnName="IDCustomer" />
          <ScalarProperty Name="IDService" ColumnName="IDService" />
          <ScalarProperty Name="IsDatabaseCustomerSeparation" ColumnName="IsDatabaseCustomerSeparation" />
          <ScalarProperty Name="DatabaseConnectionString" ColumnName="DatabaseConnectionString" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="InternalCodeTypes">
      <EntityTypeMapping TypeName="CloudMasterData.InternalCodeType">
        <MappingFragment StoreEntitySet="InternalCodeType">
          <ScalarProperty Name="Name" ColumnName="Name" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="LabelTemplates">
      <EntityTypeMapping TypeName="CloudMasterData.LabelTemplate">
        <MappingFragment StoreEntitySet="LabelTemplate">
          <ScalarProperty Name="IDLabelTemplate" ColumnName="IDLabelTemplate" />
          <ScalarProperty Name="IDCustomer" ColumnName="IDCustomer" />
          <ScalarProperty Name="Unit" ColumnName="Unit" />
          <ScalarProperty Name="Label" ColumnName="Label" />
          <ScalarProperty Name="LabelName" ColumnName="LabelName" />
          <ScalarProperty Name="TimestampCreated" ColumnName="TimestampCreated" />
          <ScalarProperty Name="UserCreated" ColumnName="UserCreated" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="MahTypes">
      <EntityTypeMapping TypeName="CloudMasterData.MahType">
        <MappingFragment StoreEntitySet="MahType">
          <ScalarProperty Name="IDMahType" ColumnName="IDMahType" />
          <ScalarProperty Name="Type" ColumnName="Type" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="Manufacturers">
      <EntityTypeMapping TypeName="CloudMasterData.Manufacturer">
        <MappingFragment StoreEntitySet="Manufacturer">
          <ScalarProperty Name="IDMah" ColumnName="IDMah" />
          <ScalarProperty Name="IDCustomer" ColumnName="IDCustomer" />
          <ScalarProperty Name="IDMahType" ColumnName="IDMahType" />
          <ScalarProperty Name="MahID" ColumnName="MahID" />
          <ScalarProperty Name="MahName" ColumnName="MahName" />
          <ScalarProperty Name="MahStreet" ColumnName="MahStreet" />
          <ScalarProperty Name="MahStreet2" ColumnName="MahStreet2" />
          <ScalarProperty Name="MahCity" ColumnName="MahCity" />
          <ScalarProperty Name="MahPostCode" ColumnName="MahPostCode" />
          <ScalarProperty Name="MahCountryCode" ColumnName="MahCountryCode" />
          <ScalarProperty Name="TimeStampCreated" ColumnName="TimeStampCreated" />
          <ScalarProperty Name="UserCreated" ColumnName="UserCreated" />
          <ScalarProperty Name="TimeStampUpdated" ColumnName="TimeStampUpdated" />
          <ScalarProperty Name="UserUpdated" ColumnName="UserUpdated" />
          <ScalarProperty Name="GS1CompanyPrefix" ColumnName="GS1CompanyPrefix" />
          <ScalarProperty Name="GLN" ColumnName="GLN" />
          <ScalarProperty Name="SGLN" ColumnName="SGLN" />
          <ScalarProperty Name="IsActive" ColumnName="IsActive" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="Products">
      <EntityTypeMapping TypeName="CloudMasterData.Product">
        <MappingFragment StoreEntitySet="Product">
          <ScalarProperty Name="IDProduct" ColumnName="IDProduct" />
          <ScalarProperty Name="IDServiceMasterRecord" ColumnName="IDServiceMasterRecord" />
          <ScalarProperty Name="IDCustomer" ColumnName="IDCustomer" />
          <ScalarProperty Name="CodeType" ColumnName="CodeType" />
          <ScalarProperty Name="Code" ColumnName="Code" />
          <ScalarProperty Name="InternalCodeType" ColumnName="InternalCodeType" />
          <ScalarProperty Name="InternalCode" ColumnName="InternalCode" />
          <ScalarProperty Name="Name" ColumnName="Name" />
          <ScalarProperty Name="CommonName" ColumnName="CommonName" />
          <ScalarProperty Name="Form" ColumnName="Form" />
          <ScalarProperty Name="PackType" ColumnName="PackType" />
          <ScalarProperty Name="PackSize" ColumnName="PackSize" />
          <ScalarProperty Name="Strength" ColumnName="Strength" />
          <ScalarProperty Name="IsActive" ColumnName="IsActive" />
          <ScalarProperty Name="TimestampCreated" ColumnName="TimestampCreated" />
          <ScalarProperty Name="UserCreated" ColumnName="UserCreated" />
          <ScalarProperty Name="TimestampUpdated" ColumnName="TimestampUpdated" />
          <ScalarProperty Name="UserUpdated" ColumnName="UserUpdated" />
          <ScalarProperty Name="SerialNumberSourceType" ColumnName="SerialNumberSourceType" />
          <ScalarProperty Name="SerializationType" ColumnName="SerializationType" />
          <ScalarProperty Name="IDCustomerPartner" ColumnName="IDCustomerPartner" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="ProductAdditionalCodes">
      <EntityTypeMapping TypeName="CloudMasterData.ProductAdditionalCode">
        <MappingFragment StoreEntitySet="ProductAdditionalCode">
          <ScalarProperty Name="IDProductAdditionalCode" ColumnName="IDProductAdditionalCode" />
          <ScalarProperty Name="IDProduct" ColumnName="IDProduct" />
          <ScalarProperty Name="CodeType" ColumnName="CodeType" />
          <ScalarProperty Name="Code" ColumnName="Code" />
          <ScalarProperty Name="UserCreated" ColumnName="UserCreated" />
          <ScalarProperty Name="TimestampCreated" ColumnName="TimestampCreated" />
          <ScalarProperty Name="UserUpdated" ColumnName="UserUpdated" />
          <ScalarProperty Name="TimestampUpdated" ColumnName="TimestampUpdated" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="ProductCodeTypes">
      <EntityTypeMapping TypeName="CloudMasterData.ProductCodeType">
        <MappingFragment StoreEntitySet="ProductCodeType">
          <ScalarProperty Name="CodeType" ColumnName="CodeType" />
          <ScalarProperty Name="Length" ColumnName="Length" />
          <ScalarProperty Name="Regex" ColumnName="Regex" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="ProductLabels">
      <EntityTypeMapping TypeName="CloudMasterData.ProductLabel">
        <MappingFragment StoreEntitySet="ProductLabel">
          <ScalarProperty Name="IDProductLabel" ColumnName="IDProductLabel" />
          <ScalarProperty Name="IDCustomer" ColumnName="IDCustomer" />
          <ScalarProperty Name="IDProduct" ColumnName="IDProduct" />
          <ScalarProperty Name="LabelName" ColumnName="LabelName" />
          <ScalarProperty Name="Label" ColumnName="Label" />
          <ScalarProperty Name="Level" ColumnName="Level" />
          <ScalarProperty Name="IsDefault" ColumnName="IsDefault" />
          <ScalarProperty Name="IDTargetMarket" ColumnName="IDTargetMarket" />
          <ScalarProperty Name="TimestampCreated" ColumnName="TimestampCreated" />
          <ScalarProperty Name="UserCreated" ColumnName="UserCreated" />
          <ScalarProperty Name="PrintingSrcSystem" ColumnName="PrintingSrcSystem" />
          <ScalarProperty Name="PrintingSrcSystemName" ColumnName="PrintingSrcSystemName" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="SerializationTypes">
      <EntityTypeMapping TypeName="CloudMasterData.SerializationType">
        <MappingFragment StoreEntitySet="SerializationType">
          <ScalarProperty Name="IDSerializationType" ColumnName="IDSerializationType" />
          <ScalarProperty Name="SerializationType1" ColumnName="SerializationType" />
          <ScalarProperty Name="Regex" ColumnName="Regex" />
          <ScalarProperty Name="OrderDisplayList" ColumnName="OrderDisplayList" />
          <ScalarProperty Name="IsActive" ColumnName="IsActive" />
          <ScalarProperty Name="IsCarton" ColumnName="IsCarton" />
          <ScalarProperty Name="IsCase" ColumnName="IsCase" />
          <ScalarProperty Name="IsPallet" ColumnName="IsPallet" />
          <ScalarProperty Name="IsSerialsExpected" ColumnName="IsSerialsExpected" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="SerialNumberSourceTypes">
      <EntityTypeMapping TypeName="CloudMasterData.SerialNumberSourceType">
        <MappingFragment StoreEntitySet="SerialNumberSourceType">
          <ScalarProperty Name="IDSerialNumberSourceType" ColumnName="IDSerialNumberSourceType" />
          <ScalarProperty Name="IDService" ColumnName="IDService" />
          <ScalarProperty Name="Code" ColumnName="Code" />
          <ScalarProperty Name="Name" ColumnName="Name" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="Services">
      <EntityTypeMapping TypeName="CloudMasterData.Service">
        <MappingFragment StoreEntitySet="Service">
          <ScalarProperty Name="IDService" ColumnName="IDService" />
          <ScalarProperty Name="Name" ColumnName="Name" />
          <ScalarProperty Name="SrcSystem" ColumnName="SrcSystem" />
          <ScalarProperty Name="SerializationType" ColumnName="SerializationType" />
          <ScalarProperty Name="IsCMD" ColumnName="IsCMD" />
          <ScalarProperty Name="IsServiceOwnProduct" ColumnName="IsServiceOwnProduct" />
          <ScalarProperty Name="IsServiceOwnCustomer" ColumnName="IsServiceOwnCustomer" />
          <ScalarProperty Name="ADURI" ColumnName="ADURI" />
          <ScalarProperty Name="ProductUpdateSql" ColumnName="ProductUpdateSql" />
          <ScalarProperty Name="CustomerUpdateSql" ColumnName="CustomerUpdateSql" />
          <ScalarProperty Name="IsServiceOwnManufacturer" ColumnName="IsServiceOwnManufacturer" />
          <ScalarProperty Name="ManufacturerUpdateSql" ColumnName="ManufacturerUpdateSql" />
          <ScalarProperty Name="UserUpdateSql" ColumnName="UserUpdateSql" />
          <ScalarProperty Name="IsExternalCodeAccepted" ColumnName="IsExternalCodeAccepted" />
          <ScalarProperty Name="ProductRegex" ColumnName="ProductRegex" />
          <ScalarProperty Name="IsUserDeleteAllowed" ColumnName="IsUserDeleteAllowed" />
          <ScalarProperty Name="IsServiceOwnLabel" ColumnName="IsServiceOwnLabel" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="ServiceContracts">
      <EntityTypeMapping TypeName="CloudMasterData.ServiceContract">
        <MappingFragment StoreEntitySet="ServiceContract">
          <ScalarProperty Name="IDServiceContract" ColumnName="IDServiceContract" />
          <ScalarProperty Name="IDContract" ColumnName="IDContract" />
          <ScalarProperty Name="IDService" ColumnName="IDService" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="ServiceEnvironments">
      <EntityTypeMapping TypeName="CloudMasterData.ServiceEnvironment">
        <MappingFragment StoreEntitySet="ServiceEnvironment">
          <ScalarProperty Name="IDServiceEnvironment" ColumnName="IDServiceEnvironment" />
          <ScalarProperty Name="IDService" ColumnName="IDService" />
          <ScalarProperty Name="Name" ColumnName="Name" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="ServiceRoles">
      <EntityTypeMapping TypeName="CloudMasterData.ServiceRole">
        <MappingFragment StoreEntitySet="ServiceRole">
          <ScalarProperty Name="IDServiceRole" ColumnName="IDServiceRole" />
          <ScalarProperty Name="RoleName" ColumnName="RoleName" />
          <ScalarProperty Name="GroupName" ColumnName="GroupName" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="ServiceUserLogins">
      <EntityTypeMapping TypeName="CloudMasterData.ServiceUserLogin">
        <MappingFragment StoreEntitySet="ServiceUserLogin">
          <ScalarProperty Name="IDServiceUserLogin" ColumnName="IDServiceUserLogin" />
          <ScalarProperty Name="IDService" ColumnName="IDService" />
          <ScalarProperty Name="LgnName" ColumnName="LgnName" />
          <ScalarProperty Name="ExternalSenderCode" ColumnName="ExternalSenderCode" />
          <ScalarProperty Name="ExternalRecieverCode" ColumnName="ExternalRecieverCode" />
          <ScalarProperty Name="IDManufacturer" ColumnName="IDManufacturer" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="TargetMarkets">
      <EntityTypeMapping TypeName="CloudMasterData.TargetMarket">
        <MappingFragment StoreEntitySet="TargetMarket">
          <ScalarProperty Name="IDTargetMarket" ColumnName="IDTargetMarket" />
          <ScalarProperty Name="Name" ColumnName="Name" />
          <ScalarProperty Name="ShortName" ColumnName="ShortName" />
          <ScalarProperty Name="GS1RNCode" ColumnName="GS1RNCode" />
          <ScalarProperty Name="TimestampCreated" ColumnName="TimestampCreated" />
          <ScalarProperty Name="UserCreated" ColumnName="UserCreated" />
          <ScalarProperty Name="TimestampUpdated" ColumnName="TimestampUpdated" />
          <ScalarProperty Name="UserUpdated" ColumnName="UserUpdated" />
          <ScalarProperty Name="SerializationType" ColumnName="SerializationType" />
          <ScalarProperty Name="SrcSystem" ColumnName="SrcSystem" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="UserLogins">
      <EntityTypeMapping TypeName="CloudMasterData.UserLogin">
        <MappingFragment StoreEntitySet="UserLogin">
          <ScalarProperty Name="LgnName" ColumnName="LgnName" />
          <ScalarProperty Name="IDCustomer" ColumnName="IDCustomer" />
          <ScalarProperty Name="IDManufacturer" ColumnName="IDManufacturer" />
          <ScalarProperty Name="IsADUser" ColumnName="IsADUser" />
          <ScalarProperty Name="TimestampCreated" ColumnName="TimestampCreated" />
          <ScalarProperty Name="UserCreated" ColumnName="UserCreated" />
          <ScalarProperty Name="TimestampUpdated" ColumnName="TimestampUpdated" />
          <ScalarProperty Name="UserUpdated" ColumnName="UserUpdated" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="UserLoginServiceRoles">
      <EntityTypeMapping TypeName="CloudMasterData.UserLoginServiceRole">
        <MappingFragment StoreEntitySet="UserLoginServiceRole">
          <ScalarProperty Name="IDUserLoginServiceRole" ColumnName="IDUserLoginServiceRole" />
          <ScalarProperty Name="LgnName" ColumnName="LgnName" />
          <ScalarProperty Name="IDServiceRole" ColumnName="IDServiceRole" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="UserPrinterSettings">
      <EntityTypeMapping TypeName="CloudMasterData.UserPrinterSetting">
        <MappingFragment StoreEntitySet="UserPrinterSetting">
          <ScalarProperty Name="LgnName" ColumnName="LgnName" />
          <ScalarProperty Name="UseNetworkPrinter" ColumnName="UseNetworkPrinter" />
          <ScalarProperty Name="NetworkPrinterIP" ColumnName="NetworkPrinterIP" />
          <ScalarProperty Name="NetworkPrinterPort" ColumnName="NetworkPrinterPort" />
          <ScalarProperty Name="PrinterDriverName" ColumnName="PrinterDriverName" />
          <ScalarProperty Name="PrinterLanguageType" ColumnName="PrinterLanguageType" />
          <ScalarProperty Name="TimestampCreated" ColumnName="TimestampCreated" />
          <ScalarProperty Name="TimestampUpdated" ColumnName="TimestampUpdated" />
          <ScalarProperty Name="UserCreated" ColumnName="UserCreated" />
          <ScalarProperty Name="UserUpdated" ColumnName="UserUpdated" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
  </EntityContainerMapping>
</Mapping>