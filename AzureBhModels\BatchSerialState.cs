﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureBhModels
{
    [Table("BatchSerialState")]
    public class BatchSerialState
    {
        [Key]
        public int IDBatchSerialState { get; set; }

        [Required]
        public int IDBatch { get; set; }

        [Required]
        [StringLength(50)]
        public string State { get; set; }

        [Required]
        public int InitialCount { get; set; }

        public int? CurrentCount { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [InverseProperty("BatchSerialStates")]
        public Batch Batch { get; set; }

    }
}
