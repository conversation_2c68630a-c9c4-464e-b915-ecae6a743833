# OpenSSL self-signed cetificate Genration 
### 1. Create config file: cert.conf
~~~
[ req ]
default_bits       = 2048
prompt             = no
default_md         = sha256
distinguished_name = dn
req_extensions     = req_ext

[ dn ]
C = BG
ST = Sofia
L = Sofia
O = SoftGroup
OU = SoftGroup
CN = satt.net

[ req_ext ]
subjectAltName = @alt_names

[ alt_names ]
DNS.1 = satt.net
DNS.2 = www.satt.net
~~~
### 2. Generate a 2048-bit RSA Private Key with 3DES Encryption

~~~bash
openssl genrsa -des3 -out private.key 2048
~~~

### 3. Create a Certificate Signing Request (CSR)
~~~ bash
openssl req -new -key private.key -out certificate.csr -config cert.conf
~~~
### 4. Create a Self-Signed X.509 Certificate
```bash
openssl x509 -req -days 365 -in certificate.csr -signkey private.key -out certificate.crt -extfile cert.conf -extensions req_ext
```
### 5. Extract the Public Key from the Private Key
```
openssl x509 -in certificate.crt -pubkey -noout > public.key

```
### 6. Verify ccertificate
```
openssl verify -CAfile certificate.crt certificate.crt
```
7. Certificate Info
```
openssl x509 -in certificate.crt -text -noout
```


pass: SG2025@#$