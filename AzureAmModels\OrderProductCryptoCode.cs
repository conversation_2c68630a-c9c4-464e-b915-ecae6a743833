﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureAmModels
{
    [Table("OrderCryptoCode")]
    public class OrderCryptoCode
    {
        [Key]
        public int IDOrderCryptoCode { get; set; }

        public int IDOrder { get; set; }

        [StringLength(50)]
        public string BlockID { get; set; }

        [StringLength(256)]
        public string Code { get; set; }

        [Required]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [InverseProperty("OrderProductCryptoCodes")]
        public Order Order { get; set; }


        public override bool Equals(object obj)
        {
            if (obj == null)
                return false;

            if (obj.GetType() == this.GetType())
            {
                if (((OrderCryptoCode)obj).IDOrderCryptoCode == this.IDOrderCryptoCode)
                    return true;
                else
                    return false;
            }
            else
                return false;
        }
    }
}
