﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureAmModels
{
    [Table("OrderHistory")]
    public class OrderHistory
    {
        public OrderHistory()
        {
            BuffersHistory = new HashSet<BufferHistory>();
        }

        public OrderHistory(int idOrder, string lgnName)
        {
            BuffersHistory = new HashSet<BufferHistory>();

            IDOrder = idOrder;
            OrderStatus = OrderStatusEnum.CLOSED;
            DeclineReason = "";
            TotalQuantity = "0";
            TimestampCreated = DateTime.Now;
            UserCreated = lgnName;
        }

        public OrderHistory(int idOrder, string orderStatusParam, string declineReason, string totalQuantityParam, string lgnName)
        {
            IDOrder = idOrder;
            BuffersHistory = new List<BufferHistory>();
            OrderStatus = ParseToOrderStatusEnum(orderStatusParam);
            DeclineReason = DeclineReasonFormat(declineReason);
            TotalQuantity = totalQuantityParam;
            TimestampCreated = DateTime.Now;
            UserCreated = lgnName;
        }

        [Key]
        public int IDOrderHistory { get; set; }

        [Required]
        public int IDOrder { get; set; }

        [Required]
        [DisplayName("Order status")]
        [StringLength(50)]
        public OrderStatusEnum OrderStatus { get; set; }

        [DisplayName("Decline reason")]
        [StringLength(100)]
        public string DeclineReason { get; set; }

        [DisplayName("Quantity")]
        [StringLength(100)]
        public string TotalQuantity { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        [DisplayName("Created")]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        [DisplayName("User Created")]
        public string UserCreated { get; set; }

        [InverseProperty("OrderHistory")]
        public ICollection<BufferHistory> BuffersHistory { get; set; }

        [InverseProperty("OrdersHistory")]
        public Order Order { get; set; }



        public override bool Equals(object obj)
        {
            if (obj == null)
                return false;

            if (obj.GetType() == this.GetType())
            {
                if (((OrderHistory)obj).IDOrderHistory == this.IDOrderHistory)
                    return true;
                else
                    return false;
            }
            else
                return false;
        }
        private OrderStatusEnum ParseToOrderStatusEnum(string orderStatus)
        {
            if (Enum.TryParse(orderStatus, out OrderStatusEnum orderEnumStatus))
                return orderEnumStatus;
            else
                return OrderStatusEnum.UNKNOWN;
        }

        private string DeclineReasonFormat(string declineReason)
        {
            string _declineReason = string.Empty;
            if (!string.IsNullOrEmpty(declineReason) && declineReason.Length > 100)
                _declineReason = declineReason.Substring(0, 100);
            else
                _declineReason = declineReason;

            return _declineReason;
        }
    }
}
