using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using AS2Test.Models;
using MimeKit;

namespace AS2Test.Features.Services.MdnHandler
{
    public interface IMdnDecryptionVerificationService
    {
        /// <summary>
        /// Decrypts and verifies the provided MDN (Message Disposition Notification) response message.
        /// </summary>
        /// <param name="mdnResponseMessage">The HTTP response message containing the MDN to be decrypted and verified.</param>
        /// <param name="cancellationToken">A cancellation token that can be used to cancel the operation.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the MDN verification result.</returns>
        Task<MdnVerificationResult> DecryptAndVerify(HttpResponseMessage mdnResponseMessage, string receiverCertificate, string senderCertificate);
    }
}