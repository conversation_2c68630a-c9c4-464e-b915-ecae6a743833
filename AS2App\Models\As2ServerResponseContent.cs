using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AS2Test.Models
{
    /// <summary>
    /// Represents the response content from an AS2 server, including AS2-specific and HTTP response properties.
    /// </summary>
        public class As2ServerResponseContent
    {
        // AS2-specific properties
        public string MessageId { get; set; }
        public string OriginalMessageId { get; set; }
        public string Disposition { get; set; }  // e.g., "automatic-action/MDN-sent-automatically; processed"
        public bool ValidMic { get; set; }          
        public string Status { get; set; }       // e.g., "Success", "Failure"
        public DateTime Timestamp { get; set; }
        public string ErrorMessage { get; set; } // optional, null if no error

        // HTTP response properties
        public int HttpStatusCode { get; set; }
        public string ReasonPhrase { get; set; }
        public Dictionary<string, string> Headers { get; set; }
        public string RawBody { get; set; }      // optional: entire HTTP response body (e.g., full MDN)
    }
}