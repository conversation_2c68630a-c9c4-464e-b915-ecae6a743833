using GSNXSCDataTransfer.CustomModels;
using Microsoft.Data.SqlClient;
using System.Data;
using System.Linq;
using System.Xml.Serialization;

namespace GSNXSCDataTransfer
{
    public class Main
    {
        private const string SRC_SYSTEM_GNX = "GN";
        private static string _logDirectory = @"C:\TEMP\GSNXDataTransformation";

        private static GsnxApiClient gsnxApiClient { get; set; }

        private static int _appTimeout = 0; //application timeout in minutes
        private static DateTime _appStartTime;
        private static SqlConnection _connection;
        private static GSNXDataTransformation _gSNXDataTransformation;

        public static GsnxApiClient _gsnxApiClient
        {
            get
            {
                if (gsnxApiClient != null)
                    return gsnxApiClient;

                else
                    throw new Exception("Error! SATT GSNX API is not initialized");
            }
        }



        public async Task TransferSCSerials(string[] args)
        {
#if DEBUG
            if (args.Count() == 0)
                args = new string[] { @"GSNXSCDataTransformationXml.xml" };

#endif
            try
            {
                _appStartTime = DateTime.Now;

                CustomLogEvent($"Start SC transfer");

                if (args == null || args.Count() == 0)
                    throw new Exception("Invalid command line parameters");


                _gSNXDataTransformation = new(args[0]);


                if (_gSNXDataTransformation == null)
                    throw new Exception($"Invalid file {args[0]}");
                
                gsnxApiClient = InitializeApiClient();

                _logDirectory = _gSNXDataTransformation.LogDirectory;

                if (_gSNXDataTransformation.AppTimeout.HasValue && _gSNXDataTransformation.AppTimeout > 0)
                    _appTimeout = _gSNXDataTransformation.AppTimeout.Value;
                else
                    _appTimeout = 9999;

                CheckConnectionStringAndProcedures();

                List<CustomerProduct> sCCustomerProducts = await GetCustomerProducts();

                if (sCCustomerProducts == null || sCCustomerProducts.Count() == 0)
                    throw new Exception($"No products found for customer");

                if (await CreateMissingProducts( sCCustomerProducts))
                    Thread.Sleep(5000); //ensure missing products are created

                List<ProductExtended> gSNXCustomerProducts = await _gsnxApiClient.GetProductList();

                await FetchAndUploadSN(sCCustomerProducts, gSNXCustomerProducts);

                await FetchAndUploadSSCC(sCCustomerProducts, gSNXCustomerProducts);
                    

                CustomLogEvent($"Finish SC transfer");

            }
            catch (Exception e)
            {
                CustomLogEvent(e.Message + " " + e.InnerException?.Message, "Main", true);
            }
        }

        private static async Task<bool> CreateMissingProducts(List<CustomerProduct> sCCustomerProducts)
        {

            bool newProductsExist = false;
            List<ProductExtended> gSNXCustomerProducts = await _gsnxApiClient.GetProductList();

            foreach (CustomerProduct customerProduct in sCCustomerProducts) // create missing products
            {
                try
                {
                    if (DateTime.Now >= _appStartTime.AddMinutes(_appTimeout))
                    {
                        CustomLogEvent("Application timeout setting reached.");
                        Environment.Exit(0);
                    }

                    if (_gSNXDataTransformation.ProductsMaping.Any(p => p.GTIN.Contains(customerProduct.CIP)))
                        CustomLogEvent($"Skiping Product create for  [{customerProduct.ProductName}] with {customerProduct.CIP}", "CreateMissingProducts");


                    List<SourceSystem> sourceSystems = await GetSrcSystemInfo(customerProduct);

                    List<(string GTIN, string SrcSystem)> addedProducts = new();

                    foreach (var sourceSystem in sourceSystems)
                    {
                        if (!gSNXCustomerProducts.Any(p => Utils.GTIN.ToGTIN14(p.Code) == Utils.GTIN.ToGTIN14(customerProduct.CIP) && p.SrcSystemCode == sourceSystem.srcSystem)
                            && (!addedProducts.Any(p => Utils.GTIN.ToGTIN14(p.GTIN) == Utils.GTIN.ToGTIN14(customerProduct.CIP) && p.SrcSystem == sourceSystem.srcSystem)))
                        {
                            newProductsExist = true;

                            CustomLogEvent($"Create product [{customerProduct.ProductName}] {customerProduct.CIP}", "CreateMissingProducts");


                            var apiProduct = ProductToApi(customerProduct, sourceSystem.srcSystemID);

                            var res = await _gsnxApiClient.SetProduct(apiProduct);

                            if (string.IsNullOrEmpty(res))
                            {
                                addedProducts.Add((customerProduct.CIP, sourceSystem.srcSystem));
                                CustomLogEvent($"SetProduct for GTIN: {customerProduct.CIP} empty response", "CreateMissingProducts");
                            }
                            else
                                CustomLogEvent($"Process product result [{res}]", "CreateMissingProducts");
                        }
                        else
                            CustomLogEvent($"Similar product exists [{customerProduct.CIP}]", "CreateMissingProducts");
                    }


                }
                catch (Exception e)
                {
                    CustomLogEvent($"Error creating product {customerProduct.CIP} {e.Message} {e.InnerException?.Message}", "CreateMissingProductskAndCreateProducts", true);
                    throw;
                }
            }
            return newProductsExist;

        }

        private static async Task<List<SourceSystem>> GetSrcSystemInfo(CustomerProduct customerProduct)
        {
            SourceSystem sourceSystem = new SourceSystem()
            {
                srcSystemID = _gSNXDataTransformation.DefaultSrcSystemServiceID,
                srcSystem = _gSNXDataTransformation.DefaultSrcSystem
            };
            var sourceSystems = new List<SourceSystem>()
            { sourceSystem};


            var gtinProductMapping = _gSNXDataTransformation.ProductMaping.Where(pm => pm.GTIN == customerProduct.CIP);
            var gtinProductsMapping = _gSNXDataTransformation.ProductsMaping.Where(g => g.GTIN.Contains(customerProduct.CIP));


            if (gtinProductMapping != null && gtinProductMapping.Count() > 1)
            {
                var customerSerials = await GetCustomerSerials(customerProduct.CIP);

                var customerSerialsWithAICode = customerSerials.Where(s => !string.IsNullOrEmpty(s.AI91));


                if (customerSerialsWithAICode != null && customerSerialsWithAICode.Count() > 0)
                {
                    sourceSystems.Clear();

                    var groupedByAI91 = customerSerials
                    .GroupBy(cs => cs.AI91)
                    .Select(group => new
                    {
                        AI91 = group.Key,
                        CustomerSerials = group.ToList()
                    })
                    .ToList();

                    if (_gSNXDataTransformation.CryptoCodeMapping == null || _gSNXDataTransformation.CryptoCodeMapping.Issuer == null)
                        throw new Exception($"Missing CryptoCodemapping and found CIP {customerProduct.CIP} with AI Codes");

                    foreach (var aiCode in groupedByAI91)
                    {
                        var issuer = _gSNXDataTransformation.CryptoCodeMapping.Issuer.FirstOrDefault(i => aiCode.AI91.Contains(aiCode.AI91));

                        if (issuer == null)
                            throw new Exception($"Missing CryptoCodemapping for CIP {customerProduct.CIP} with AI code {aiCode.AI91}");


                        sourceSystems.Add(new SourceSystem()
                        {
                            srcSystemID = issuer.SrcSystemServiceID,
                            srcSystem = issuer.SrcSystem
                        });
                    }
                }

            }
            else
            {

                var productMapping = _gSNXDataTransformation.ProductMaping.FirstOrDefault(pm => pm.GTIN == customerProduct.CIP);

                if (productMapping != null)
                {
                    sourceSystems[0].srcSystemID = productMapping.SrcSystemServiceID;
                    sourceSystems[0].srcSystem = productMapping.SrcSystem;
                }
                else if (_gSNXDataTransformation.ProductsMaping != null && _gSNXDataTransformation.ProductsMaping.Any(g => g.GTIN.Contains(customerProduct.CIP)))
                {
                    sourceSystems[0].srcSystemID = _gSNXDataTransformation.ProductsMaping.FirstOrDefault(p => p.GTIN.Contains(customerProduct.CIP)).SrcSystemServiceID;
                    sourceSystems[0].srcSystem = _gSNXDataTransformation.ProductsMaping.FirstOrDefault(p => p.GTIN.Contains(customerProduct.CIP)).SrcSystem;

                }
            }

            return sourceSystems;


        }

        private static CustomModels.ApiProduct ProductToApi(CustomerProduct customerProduct,  int srcSystem)
        {

            CustomModels.ApiProduct result = new()
            {
                IDCustomer = _gSNXDataTransformation.IDCustomer,
                Code = Utils.GTIN.ToGTIN14(customerProduct.CIP),
                CodeType = "GTIN",
                CommonName = customerProduct.ProductName,
                Form = "n/a",
                IDProduct = 0,
                IDServiceMasterRecord = srcSystem,
                IsActive = true,
                Name = customerProduct.ProductName,
                PackSize = 1,
                PackType = "n/a",
                SerialNumberSourceType = "SELF_MADE",
                Strength = "n/a",
                User = _gSNXDataTransformation.GsnxUsername,
                SerializationType = "RANDOM"
            };



            return result;
        }

        private static async Task<List<CustomerProduct>> GetCustomerProducts()
        {
            List<CustomerProduct> reportResult = new List<CustomerProduct>();

            using (SqlCommand cmd = new SqlCommand(_gSNXDataTransformation.ProductsStoredProcedure, _connection))
            {

                using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                {

                    while (await reader.ReadAsync())
                    {
                        reportResult.Add(new CustomerProduct(reader));
                    }
                }

            }

            var result = reportResult?.Where(r => Utils.GTIN.Check(r.CIP)).ToList();

            return result;
        }

        private static async Task<List<CustomerSerial>> GetCustomerSerials(string cIP)
        {
            List<CustomerSerial> reportResult = new List<CustomerSerial>();

            using (SqlCommand cmd = new SqlCommand($"exec {_gSNXDataTransformation.SNXStoredProcedure} @CIP", _connection))
            {
                SqlParameter cipParam = new SqlParameter("CIP", System.Data.SqlDbType.NVarChar, 14);
                cipParam.SqlValue = cIP;
                cmd.Parameters.Add(cipParam);

                using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                {

                    while (await reader.ReadAsync())
                    {

                        reportResult.Add(new CustomerSerial(reader));
                    }
                }
            }

            return reportResult;
        }

        private static async Task<List<string>> GetCustomerSerialsAsString(string cIP)
        {
            List<string> reportResult = new();

            using (SqlCommand cmd = new SqlCommand($"exec {_gSNXDataTransformation.SNXStoredProcedure} @CIP", _connection))
            {
                SqlParameter cipParam = new SqlParameter("CIP", System.Data.SqlDbType.NVarChar, 14);
                cipParam.SqlValue = cIP;
                cmd.Parameters.Add(cipParam);

                using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                {

                    while (await reader.ReadAsync())
                    {
                        reportResult.Add((string)reader[0]);


                    }
                }
            }


            return reportResult;


        }

        private static async Task<List<CustomerSSCC>> GetCustomerSSCCs()
        {
            try
            {
                List<CustomerSSCC> reportResult = new List<CustomerSSCC>();

                using (SqlCommand cmd = new SqlCommand($"exec {_gSNXDataTransformation.SSCCStoredProcedure} ", _connection))
                {

                    using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                    {

                        while (await reader.ReadAsync())
                        {
                            var test = reader[0];
                            reportResult.Add(new CustomerSSCC(reader));
                        }
                    }
                }

                return reportResult;
            }

            catch (Exception e)
            {
                CustomLogEvent($"Exception collecting customer products {e.Message} {e.InnerException?.Message}");
                return null;
            }
        }

        private static void CheckConnectionStringAndProcedures()
        {
            string connString = _gSNXDataTransformation.ConnectionString;

            _connection = new SqlConnection(connString);
            _connection.Open();


            string[] procedures = { _gSNXDataTransformation.ProductsStoredProcedure, _gSNXDataTransformation.SNXStoredProcedure };

            bool spExists = false;

            foreach (string procedure in procedures)
            {
                if (string.IsNullOrEmpty(procedure))
                    continue;

                using (SqlCommand command = new SqlCommand($"select * from sysobjects where type='P' and name='{procedure}'", _connection))
                {
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            spExists = true;
                            break;
                        }
                        if (!spExists)
                            throw new Exception($"Stored procedure [{procedure}] does not exist");
                    }
                }
            }


        }

        private static async Task FetchAndUploadSN(List<CustomerProduct> sCCustomerProducts, List<ProductExtended> gSNXCustomerProducts)
        {
            try
            {

                foreach (CustomerProduct product in sCCustomerProducts)
                {

                    if (DateTime.Now >= _appStartTime.AddMinutes(_appTimeout))
                    {
                        CustomLogEvent("Application timeout setting reached.");
                        Environment.Exit(0);

                    }

                    List<CustomerSerial> sCCustomerSerials = await GetCustomerSerials(product.CIP);

                    List<SourceSystem> sourceSystems = await GetSrcSystemInfo(product);

                    await UploadSNWithoutCrypto(gSNXCustomerProducts, product, sCCustomerSerials, sourceSystems);

                    if (sourceSystems.Count > 1)
                    {
                        await UploadSNWithCrypto(gSNXCustomerProducts, product, sCCustomerSerials, sourceSystems);

                    }

                }
            }
            catch (Exception e)
            {
                CustomLogEvent(e.Message + " " + e.InnerException?.Message, "FetchAndUploadSN", true);
                throw new Exception($"SNPool Error: {e.Message} {e.InnerException?.Message} when uploading records");
            }
        }

        private static async Task UploadSNWithCrypto(List<ProductExtended> gSNXCustomerProducts, CustomerProduct product, List<CustomerSerial> sCCustomerSerials, List<SourceSystem> sourceSystems)
        {
            foreach (var sourceSystem in sourceSystems)
            {
                var customerProducts = gSNXCustomerProducts
                    .Where(cp => Utils.GTIN.ToGTIN14(cp.Code) == Utils.GTIN.ToGTIN14(product.CIP)
                             && cp.SrcSystemCode == sourceSystem.srcSystem)
                    .ToList();

                ProductExtended productExtended;

                if (customerProducts != null && customerProducts.Count() > 1)
                {
                    var productMaping = _gSNXDataTransformation.ProductMaping.FirstOrDefault(pm => pm.SrcSystem == sourceSystem.srcSystem && Utils.GTIN.ToGTIN14(pm.GTIN) == Utils.GTIN.ToGTIN14(product.CIP));
                    int idProduct;
                    if (!int.TryParse(productMaping.IDProduct, out idProduct))
                        throw new Exception($"invalid idproduct for mapping GTIN {productMaping.GTIN}");

                    if (productMaping != null)
                        productExtended = customerProducts.FirstOrDefault(p => p.IDProduct == idProduct);
                    else
                        productExtended = null;

                }
                else
                    productExtended = customerProducts.FirstOrDefault();


                if (productExtended == null)
                {
                    throw new Exception($"Missing customer product for GTIN {product.CIP} and system {sourceSystem} ");
                }
                else
                {

                    List<string> srcSystemAICodes = GetSrcSystemCryptoCodeMappingAICodes(productExtended.SrcSystem, product.CIP);

                    foreach (var AICode in srcSystemAICodes)
                    {
                        var serials = sCCustomerSerials.Where(s => s.AI91 == AICode).ToList();

                        CustomLogEvent($"Product [{productExtended.IDProduct}] [{productExtended.CodeType} {productExtended.Code}] processing {serials.Count} serials ", "FetchAndUploadSN");

                        if (serials.Count == 0)
                            return;

                        if (serials.Count < Utils.SerialImportLimit)
                            await CreateSNPool(productExtended, serials, 0, product.CIP);
                        else
                        {
                            int i = 0;
                            foreach (var customerSerials in serials.Chunk(Utils.SerialImportLimit))
                            {
                                if (DateTime.Now >= _appStartTime.AddMinutes(_appTimeout))
                                {
                                    CustomLogEvent("Application timeout setting reached.");
                                    Environment.Exit(0);
                                }
                                i++;
                                CustomLogEvent($"Product iteration {i} [{productExtended.IDProduct}] [{productExtended.CodeType} {productExtended.Code}] processing {customerSerials.Count()} serials ", "FetchAndUploadSN");

                                await CreateSNPool(productExtended, customerSerials.ToList(), i, product.CIP);
                            }
                        }
                    }
                }
            }
        }

        private static List<string> GetSrcSystemCryptoCodeMappingAICodes(string srcSystem, string cIP)
        {
            if (_gSNXDataTransformation.CryptoCodeMapping == null || _gSNXDataTransformation.CryptoCodeMapping.Issuer == null
                || _gSNXDataTransformation.CryptoCodeMapping.Issuer.FirstOrDefault(i => i.SrcSystem == srcSystem) == null)
                throw new Exception($"Found multiple source systems for GTIN [{cIP}] but no CryptoCodeMapping for srcSystem [{srcSystem}]");

            return _gSNXDataTransformation.CryptoCodeMapping.Issuer
                .FirstOrDefault(i => i.SrcSystem == srcSystem).AI91;
        }

        private static async Task UploadSNWithoutCrypto(List<ProductExtended> gSNXCustomerProducts, CustomerProduct product, List<CustomerSerial> sCCustomerSerials, List<SourceSystem> sourceSystems)
        {
            var sourceSystem = sourceSystems[0];
            var customerProducts = gSNXCustomerProducts
                .Where(cp => Utils.GTIN.ToGTIN14(cp.Code) == Utils.GTIN.ToGTIN14(product.CIP)
                         && cp.SrcSystemCode == sourceSystem.srcSystem)
                .ToList();

            var serialsWithoutCrypto = sCCustomerSerials.Where(s => string.IsNullOrEmpty(s.AI91)).ToList();

            ProductExtended productExtended;

            if (customerProducts != null && customerProducts.Count() > 1)
            {
                var productMaping = _gSNXDataTransformation.ProductMaping.FirstOrDefault(pm => pm.SrcSystem == sourceSystem.srcSystem && Utils.GTIN.ToGTIN14(pm.GTIN) == Utils.GTIN.ToGTIN14(product.CIP));
                int idProduct;
                if (!int.TryParse(productMaping.IDProduct, out idProduct))
                    throw new Exception($"invalid idproduct for mapping GTIN {productMaping.GTIN}");

                if (productMaping != null)
                    productExtended = customerProducts.FirstOrDefault(p => p.IDProduct == idProduct);
                else
                    productExtended = null;

            }
            else
                productExtended = customerProducts.FirstOrDefault();


            if (productExtended == null)
            {
                throw new Exception($"Missing customer product for GTIN {product.CIP} and system {sourceSystem} ");
            }
            else
            {
                CustomLogEvent($"Product [{productExtended.IDProduct}] [{productExtended.CodeType} {productExtended.Code}] processing {serialsWithoutCrypto.Count} serials ", "FetchAndUploadSN");

                if (serialsWithoutCrypto.Count == 0)
                    return;

                if (serialsWithoutCrypto.Count < Utils.SerialImportLimit)
                    await CreateSNPool(productExtended, serialsWithoutCrypto, 0, product.CIP);
                else
                {
                    int i = 0;
                    foreach (var customerSerials in serialsWithoutCrypto.Chunk(Utils.SerialImportLimit))
                    {
                        if (DateTime.Now >= _appStartTime.AddMinutes(_appTimeout))
                        {
                            CustomLogEvent("Application timeout setting reached.");
                            Environment.Exit(0);
                        }
                        i++;
                        CustomLogEvent($"Product iteration {i} [{productExtended.IDProduct}] [{productExtended.CodeType} {productExtended.Code}] processing {customerSerials.Count()} serials ", "FetchAndUploadSN");

                        await CreateSNPool(productExtended, customerSerials.ToList(), i, product.CIP);
                    }
                }
            }
        }

        private static async Task FetchAndUploadSSCC(List<CustomerProduct> sCCustomerProducts, List<ProductExtended> gSNXCustomerProducts)
        {
            try
            {
                List<CustomerSSCC> sCCustomerSSCCs = await GetCustomerSSCCs();
                Dictionary<SSCCKeyValuePair, List<string>> groupedCustomerSSCCs = sCCustomerSSCCs
                   .GroupBy(o => new SSCCKeyValuePair
                   {
                       CompanyPrefix = o.CompanyPrefix,
                       ExtentionDigit = o.ExtentionDigit
                   })
                   .ToDictionary(
                       g => g.Key, // The key is the CompanyPrefix and ExtensionDigit
                       g => g.Select(s => s.SSCCNumber).ToList()
                   );


                foreach (KeyValuePair<SSCCKeyValuePair, List<string>> customerSSCCs in groupedCustomerSSCCs)
                {
                    if (DateTime.Now >= _appStartTime.AddMinutes(_appTimeout))
                    {
                        CustomLogEvent("Application timeout setting reached.");
                        Environment.Exit(0);

                    }

                    {
                        CustomLogEvent($"CompanyPrefix [{customerSSCCs.Key.CompanyPrefix}] ExtDigit [{customerSSCCs.Key.ExtentionDigit} processing {customerSSCCs.Value.Count} SSCCs ", "FetchAndUploadSSCC");

                        if (customerSSCCs.Value.Count == 0)
                            continue;

                        await CreateSSCCPool(customerSSCCs);

                    }


                }
            }
            catch (Exception e)
            {
                CustomLogEvent(e.Message + " " + e.InnerException?.Message, "FetchAndUploadSN", true);
                throw new Exception($"SSCCPool Error: {e.Message} when inserting records");
            }
        }



        private static async Task CreateSNPool(ProductExtended product, List<CustomerSerial> sCCustomerSerials, int iteration, string CIP)
        {
            string datetime = DateTime.UtcNow.ToString("yyMMddHHmm");
            string poolCode = iteration > 0 ? $"SC{iteration}_{CIP}_{datetime}" : $"SC_{CIP}_{datetime}";

            AzureGsnxApiModels.ApiSnxImport snxImport = new AzureGsnxApiModels.ApiSnxImport()
            {
                ReferenceID = poolCode,
                Pool = poolCode,
                GTIN = product.Code,

                SnSerials = sCCustomerSerials.Select(s => new AzureGsnxApiModels.ApiSerialNumber
                {
                    AI91 = s.AI91,
                    AI92 = s.AI92,
                    Serial = s.Serial
                })
                    .ToList(),
                TargetSystem = product.SrcSystem
            };

            var result = await _gsnxApiClient.CreatePool(snxImport,
                 AzureSnxModels.SerializationTypeEnum.EXTERNAL_USED.ToString(),
                 _gSNXDataTransformation.IDCustomer,
                 _gSNXDataTransformation.GsnxUsername);

            if (result != null)
            {
                var responseString = await result.Content.ReadAsStringAsync();

                if (string.IsNullOrEmpty(responseString))
                    responseString = result.ReasonPhrase;


                if (result.IsSuccessStatusCode)
                {
                    CustomLogEvent(responseString, "CreateSNPool");


                    await SetSerialsUploadedState(CIP, sCCustomerSerials.Count, 1);

                    CustomLogEvent($"{sCCustomerSerials.Count} serials marked as uploaded for CIP {CIP}.", "CreateSNPool");

                }
                else if (result.StatusCode == System.Net.HttpStatusCode.BadRequest)
                {
                    CustomLogEvent("GSNX API ERROR: " +responseString, "CreateSNPool", true);

                    await SetSerialsUploadedState(CIP, sCCustomerSerials.Count, 2);

                    CustomLogEvent($"{sCCustomerSerials.Count} serials marked for error uploading for CIP {CIP}.", "CreateSNPool", true);


                }
                else

                    CustomLogEvent(responseString, "CreateSNPool", true);

            }
            else
                CustomLogEvent("Empty response string from GSNX API CreatePool", "CreateSNPool");


        }

        private static async Task SetSerialsUploadedState(string cIP, int cnt, int state)
        {
            using (SqlCommand cmd = new SqlCommand($"exec {_gSNXDataTransformation.MarkUploadedSerials} @CIP, @cnt, @state ", _connection))
            {
                SqlParameter cipParam = new SqlParameter("CIP", System.Data.SqlDbType.NVarChar, 14);
                cipParam.SqlValue = cIP;
                cmd.Parameters.Add(cipParam);

                SqlParameter cntParam = new SqlParameter("cnt", System.Data.SqlDbType.Int);
                cntParam.SqlValue = cnt;
                cmd.Parameters.Add(cntParam);

                SqlParameter stateParam = new SqlParameter("state", System.Data.SqlDbType.Int);
                stateParam.SqlValue = state;
                cmd.Parameters.Add(stateParam);
                
                await cmd.ExecuteNonQueryAsync();

            }
            
        }

        private static async Task SetSSCCsUploadedState(string companyPrefix, int extDigit, int state)
        {
            using (SqlCommand cmd = new SqlCommand($"exec {_gSNXDataTransformation.MarkUploadedSerials} @companyPrefix, @extDigit, @state ", _connection))
            {
                SqlParameter compPrefixParam = new SqlParameter("companyPrefix", System.Data.SqlDbType.NVarChar);
                compPrefixParam.SqlValue = companyPrefix;
                cmd.Parameters.Add(compPrefixParam);
                SqlParameter extDigitParam = new SqlParameter("extDigit", System.Data.SqlDbType.Int);

                extDigitParam.SqlValue = extDigit;
                cmd.Parameters.Add(extDigitParam);

                SqlParameter stateParam = new SqlParameter("state", System.Data.SqlDbType.Int);
                stateParam.SqlValue = state;
                cmd.Parameters.Add(stateParam);

                await cmd.ExecuteNonQueryAsync();

            }
        }

        private static async Task CreateSSCCPool(KeyValuePair<SSCCKeyValuePair, List<string>> customerSSCCs)
        {


            if (string.IsNullOrEmpty(customerSSCCs.Key.CompanyPrefix))
                throw new Exception($"no company prefix found for customer  sscc: [{customerSSCCs.Value.FirstOrDefault()}]");



            short extensionDigit;
            if (!short.TryParse(customerSSCCs.Key.ExtentionDigit, out extensionDigit))
            {
                throw new Exception($"invalid extension digit  {customerSSCCs.Key.ExtentionDigit} for product {customerSSCCs.Value.FirstOrDefault()}");
            }
            string datetime = DateTime.UtcNow.ToString("yyMMDDHHmm");
            string poolCode = $"SC_{customerSSCCs.Key.CompanyPrefix}_{customerSSCCs.Key.ExtentionDigit}_{datetime}";


            AzureGsnxApiModels.ApiSnxImport snxImport = new AzureGsnxApiModels.ApiSnxImport()
            {
                ReferenceID = poolCode,
                Pool = poolCode,
                CompanyPrefix = customerSSCCs.Key.CompanyPrefix,
                ExtensionDigit = extensionDigit,
                SsccSerials = customerSSCCs.Value // all SSCCs for this companyprefix + ext digit combo
            };

            var result = await _gsnxApiClient.CreatePool(snxImport,
                 AzureSnxModels.SerializationTypeEnum.EXTERNAL_USED.ToString(),
                 _gSNXDataTransformation.IDCustomer,
                 _gSNXDataTransformation.GsnxUsername);

            if (result != null)
            {
                var responseString = await result.Content.ReadAsStringAsync();

                if (string.IsNullOrEmpty(responseString))
                    responseString = result.ReasonPhrase;


                if (result.IsSuccessStatusCode)
                {
                    CustomLogEvent(responseString, "CreatePool");

                    await SetSSCCsUploadedState(customerSSCCs.Key.CompanyPrefix, extensionDigit, 1);
                    CustomLogEvent($"{customerSSCCs.Value.Count} SSCCs marked as uploaded for CompanyPrefix " +
                        $"[{customerSSCCs.Key.CompanyPrefix}] and ExtensionDigit [{extensionDigit}]", "CreatePool");

                }
                else if (result.StatusCode == System.Net.HttpStatusCode.BadRequest)
                {
                    await SetSSCCsUploadedState(customerSSCCs.Key.CompanyPrefix, extensionDigit, 2);
                    CustomLogEvent($"{customerSSCCs.Value.Count} SSCCs marked for error with CompanyPrefix " +
                        $"[{customerSSCCs.Key.CompanyPrefix}] and ExtensionDigit [{extensionDigit}]", "CreatePool");
                }
                else
                    CustomLogEvent(responseString, "CreatePool", true);

            }

        }

        private static GsnxApiClient InitializeApiClient()
        {
            AzureConnectionParams azureConnectionParams = new AzureConnectionParams()
            {
                Url = _gSNXDataTransformation.GsnxUrl,
                Scope = _gSNXDataTransformation.GsnxScope,
                ClientId = _gSNXDataTransformation.GsnxApplicationId,
                Authority = _gSNXDataTransformation.GsnxAuthority,
                User = _gSNXDataTransformation.GsnxUsername,
                Password = _gSNXDataTransformation.GsnxPassword
            };

            var gsnxApiClient = new GsnxApiClient(azureConnectionParams);
            if (gsnxApiClient == null)
                throw new Exception("Can not initialize connection with GSNX API.");

            return gsnxApiClient;

        }

        private class SsccPoolGroup
        {
            public short ExtensionDigit { get; set; }

            public string CompanyPrefix { get; set; }
        }

        private static void CustomLogEvent(string message, string method = "", bool isError = false)
        {
            try
            {
                string dt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                if (isError)
                    Console.WriteLine($"{dt} ERROR - {message}");
                else
                    Console.WriteLine($"{dt} INFO  - {message}");

                if (!isError)
                    LogFile.SaveInfo(_logDirectory, method, message);
                else
                    LogFile.SaveError(_logDirectory, method, message);


                Console.WriteLine(message);

            }
            catch (Exception e)
            {
                Console.WriteLine($"{e.Message} {e.InnerException?.Message}");
                Console.WriteLine($"{e.InnerException?.Message}");
                var debug = e.Message;
            }
        }

    }
}
