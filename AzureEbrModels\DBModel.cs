﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEbrModels
{
    public class DBModel : DbContext
    {
        public virtual DbSet<Line> Lines { get; set; }
        public virtual DbSet<SerialStateCounter> SerialStateCounters { get; set; }

        public virtual DbSet<LineRequest> LineRequests { get; set; } //TODO: Table relations in OnModelCreating
        public virtual DbSet<BatchState> BatchStates { get; set; }
        public virtual DbSet<AggregationLevel> AggregationLevels { get; set; }
        public virtual DbSet<AuditTrail> AuditTrails { get; set; }
        public virtual DbSet<Batch> Batches { get; set; }
        public virtual DbSet<BatchAggregation> BatchAggregations { get; set; }
        public virtual DbSet<BatchAggregationExtend> BatchAggregationExtends { get; set; }
        public virtual DbSet<BatchAggregationSerial> BatchAggregationSerials { get; set; }
        public virtual DbSet<BatchError> BatchErrors { get; set; }
        public virtual DbSet<BatchExtend> BatchExtends { get; set; }
        public virtual DbSet<BatchProduct> BatchProducts { get; set; }
        public virtual DbSet<BatchProductAdditionalCode> BatchProductAdditionalCodes { get; set; }
        public virtual DbSet<BatchSerialization> BatchSerializations { get; set; }
        public virtual DbSet<BatchSerializationExtend> BatchSerializationExtends { get; set; }
        public virtual DbSet<BatchSerializationSerial> BatchSerializationSerials { get; set; }
        public virtual DbSet<ExpiryDateFormat> ExpiryDateFormat { get; set; }
        public virtual DbSet<ExtendProperty> ExtendProperties { get; set; }
        public virtual DbSet<ExtendPropertySelect> ExtendPropertySelects { get; set; }
        public virtual DbSet<TargetMarket> TargetMarkets { get; set; }
        public virtual DbSet<BatchSerialsRequest> BatchSerialsRequests { get; set; }
        public virtual DbSet<AppSetting> AppSettings { get; set; }

        public virtual DbSet<BatchStateTransition> BatchStateTransitions { get; set; }
        public virtual DbSet<Machine> Machines { get; set; }
        public virtual DbSet<MachineSerial> MachineSerials { get; set; }
        public virtual DbSet<ValidationStep> ValidationSteps { get; set; }
        public virtual DbSet<BatchAggregationSerialItem> BatchAggregationSerialItems { get; set; }
        public virtual DbSet<PartnerSystem> PartnerSystems { get; set; }

        public virtual DbSet<VMOrder> VMOrders { get; set; }
        public virtual DbSet<VMOrderItem> VMOrderItems { get; set; }
        public virtual DbSet<Report> Reports { get; set; }
        public virtual DbSet<Template> Templates { get; set; }
        public virtual DbSet<TemplateExtendProperty> TemplateExtendProperties { get; set; }
        public virtual DbSet<TemplateLevel> TemplateLevels { get; set; }
        public virtual DbSet<TemplateLevelFile> TemplateLevelFiles { get; set; }
        public virtual DbSet<TemplateLine> TemplateLines { get; set; }
        public virtual DbSet<TemplateSrcSystem> TemplateSrcSystems { get; set; }
        public virtual DbSet<TemplateTargetMarket> TemplateTargetMarkets { get; set; }
        public virtual DbSet<BatchTemplateLevelFile> BatchTemplateLevelFiles { get; set; }
        public virtual DbSet<BatchUnassignedSerial> BatchUnassignedSerials { get; set; }


        private readonly string _connectionString;

        public DBModel(string connectionString)
        {
            _connectionString = connectionString;
            Database.SetCommandTimeout(600);
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_connectionString);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<TemplateExtendProperty>(entity =>
            {
                entity.HasOne(tep => tep.Template)
                    .WithMany(t => t.TemplateExtendProperties)
                    .HasForeignKey(b => b.IDTemplate)
                    .OnDelete(DeleteBehavior.Restrict)
                    .HasConstraintName("FK_TemplateExtendProperty_Template");

                entity.HasOne(tep => tep.TemplateLevel)
                   .WithMany(t => t.TemplateExtendProperties)
                   .HasForeignKey(b => b.IDTemplateLevel)
                   .OnDelete(DeleteBehavior.Restrict)
                   .HasConstraintName("FK_TemplateExtendProperty_TemplateLevel");

                entity.HasOne(tep => tep.ExtendProperty)
                  .WithMany(t => t.TemplateExtendProperties)
                  .HasForeignKey(b => b.IDExtendProperty)
                  .OnDelete(DeleteBehavior.Restrict)
                  .HasConstraintName("FK_TemplateExtendProperty_ExtendProperty");
            });

            modelBuilder.Entity<TemplateLevel>(entity =>
            {
                entity.HasOne(tl => tl.Template)
                    .WithMany(t => t.TemplateLevels)
                    .HasForeignKey(b => b.IDTemplate)
                    .OnDelete(DeleteBehavior.Restrict)
                    .HasConstraintName("FK_TemplateLevel_Template");

            });

            modelBuilder.Entity<TemplateLevelFile>(entity =>
            {
                entity.HasOne(tl => tl.TemplateLevel)
                    .WithMany(t => t.TemplateLevelFiles)
                    .HasForeignKey(b => b.IDTemplateLevel)
                    .OnDelete(DeleteBehavior.Restrict)
                    .HasConstraintName("FK_TemplateLevelFile_TemplateLevel");

                entity.HasOne(tl => tl.Template)
                   .WithMany(t => t.TemplateLevelFiles)
                   .HasForeignKey(b => b.IDTemplate)
                   .OnDelete(DeleteBehavior.Restrict)
                   .HasConstraintName("FK_TemplateLevelFile_Template");

            });

            modelBuilder.Entity<TemplateLine>(entity =>
            {
                entity.HasOne(tl => tl.Template)
                    .WithMany(t => t.TemplateLines)
                    .HasForeignKey(b => b.IDTemplate)
                    .OnDelete(DeleteBehavior.Restrict)
                    .HasConstraintName("FK_TemplateLine_Template");

                entity.HasOne(tl => tl.Line)
                    .WithMany(l => l.LineTemplates)
                    .HasForeignKey(b => b.IDLine)
                    .OnDelete(DeleteBehavior.Restrict)
                    .HasConstraintName("FK_TemplateLine_Line");

            });

            modelBuilder.Entity<TemplateSrcSystem>(entity =>
            {
                entity.HasOne(tl => tl.Template)
                    .WithMany(t => t.TemplateSrcSystems)
                    .HasForeignKey(b => b.IDTemplate)
                    .OnDelete(DeleteBehavior.Restrict)
                    .HasConstraintName("FK_TemplateSrcSystem_Template");

            });

            modelBuilder.Entity<TemplateTargetMarket>(entity =>
            {
                entity.HasOne(tl => tl.Template)
                    .WithMany(t => t.TemplateTargetMarkets)
                    .HasForeignKey(b => b.IDTemplate)
                    .OnDelete(DeleteBehavior.Restrict)
                    .HasConstraintName("FK_TemplateTargetMarket_Template");

            });

            modelBuilder.Entity<VMOrderItem>(entity =>
            {
                entity.HasOne(i => i.Order)
                    .WithMany(o => o.Items)
                    .HasForeignKey(i => i.IDVMOrder)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_VMOrderItem_VMOrder");
            });

            modelBuilder.Entity<VMOrder>(entity =>
            {
                entity.Property(e => e.TimestampCreated).HasColumnType("datetime");
                entity.Property(e => e.TimestampUpdated).HasColumnType("datetime");

                entity.HasOne(o => o.Batch)
                    .WithMany(b => b.VMOrders)
                    .HasForeignKey(o => o.IDBatch)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("FK_VMOrder_Batch");
            });

            modelBuilder.Entity<AuditTrail>(entity =>
            {
                entity.Property(e => e.TimestampCreated).HasColumnType("datetime");
            });

            modelBuilder.Entity<Report>(entity =>
            {
                entity.Property(e => e.TimestampCreated).HasColumnType("datetime");

                entity.HasOne(b => b.Batch)
                    .WithMany(r => r.Reports)
                    .HasForeignKey(b => b.IDBatch)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("FK_Report_Batch");
            });


            modelBuilder.Entity<Batch>(entity =>
            {
                entity.Property(e => e.TimestampCreated).HasColumnType("datetime");
                entity.Property(e => e.TimestampUpdated).HasColumnType("datetime");
                entity.Property(e => e.ExpiryDate).HasColumnType("datetime");
                entity.Property(e => e.ManufacturingDate).HasColumnType("datetime");


                entity.HasOne(b => b.Line)
                    .WithMany(l => l.Batches)
                    .HasForeignKey(b => b.IDLine)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_Batch_Line");

                entity.HasOne(b => b.TargetMarket)
                   .WithMany(l => l.Batches)
                   .HasForeignKey(b => b.IDTargetMarket)
                   .OnDelete(DeleteBehavior.Cascade)
                   .HasConstraintName("FK_Batch_TargetMarket");

                entity.HasOne(b => b.BatchState)
                   .WithMany(bs => bs.Batches)
                   .HasForeignKey(b => b.IDState)
                   .OnDelete(DeleteBehavior.Cascade)
                   .HasConstraintName("FK_Batch_State");

                entity.HasOne(e => e.BatchSerialization)
                    .WithOne(e => e.Batch)
                    .HasForeignKey<BatchSerialization>(e => e.IDBatch)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_BatchSerialization_Batch");

                entity.HasOne(e => e.BatchProduct)
                    .WithOne(e => e.Batch)
                    .HasForeignKey<BatchProduct>(e => e.IDBatch)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_BatchProduct_Batch");


            });

            modelBuilder.Entity<BatchAggregation>(entity =>
            {
                entity.HasOne(ba => ba.Batch)
                    .WithMany(b => b.BatchAggregations)
                    .HasForeignKey(ba => ba.IDBatch)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_BatchAggregation_Batch");
            });

            modelBuilder.Entity<BatchTemplateLevelFile>(entity =>
            {
                entity.HasOne(ba => ba.Batch)
                    .WithMany(b => b.BatchTemplateLevelFiles)
                    .HasForeignKey(ba => ba.IDBatch)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_BatchTemplateLevelFile_Batch");

                entity.HasOne(ba => ba.BatchSerialization)
                   .WithMany(b => b.BatchTemplateLevelFiles)
                   .HasForeignKey(ba => ba.IDBatchSerialization)
                   .OnDelete(DeleteBehavior.Cascade)
                   .HasConstraintName("FK_BatchTemplateLevelFile_BatchSerialization");

                entity.HasOne(ba => ba.BatchAggregation)
                   .WithMany(b => b.BatchTemplateLevelFiles)
                   .HasForeignKey(ba => ba.IDBatchAggregation)
                   .OnDelete(DeleteBehavior.Cascade)
                   .HasConstraintName("FK_BatchTemplateLevelFile_BatchAggregation");

                entity.HasOne(ba => ba.TemplateLevelFile)
                  .WithMany(b => b.BatchTemplateLevelFiles)
                  .HasForeignKey(ba => ba.IDTemplateLevelFile)
                  .OnDelete(DeleteBehavior.Cascade)
                  .HasConstraintName("FK_BatchTemplateLevelFile_TemplateLevelFile");
            });

            modelBuilder.Entity<BatchAggregationExtend>(entity =>
            {
                entity.HasOne(e => e.BatchAggregation)
                    .WithMany(ba => ba.BatchAggregationExtends)
                    .HasForeignKey(e => e.IDBatchAggregation)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_BatchAggregationExtend_BatchAggregation");
            });

            modelBuilder.Entity<BatchAggregationSerial>(entity =>
            {
                entity.HasOne(s => s.BatchAggregation)
                    .WithMany(ba => ba.BatchAggregationSerials)
                    .HasForeignKey(s => s.IDBatchAggregation)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_BatchAggregationSerial_BatchAggregation");
            });

            modelBuilder.Entity<BatchError>(entity =>
            {
                entity.HasOne(be => be.Batch)
                    .WithMany(b => b.BatchErrors)
                    .HasForeignKey(be => be.IDBatch)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_BatchError_Batch");
            });

            modelBuilder.Entity<BatchExtend>(entity =>
            {
                entity.HasOne(be => be.Batch)
                    .WithMany(b => b.BatchExtends)
                    .HasForeignKey(be => be.IDBatch)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_BatchExtend_Batch");
            });

            modelBuilder.Entity<BatchProductAdditionalCode>(entity =>
            {
                entity.HasOne(bpac => bpac.BatchProduct)
                    .WithMany(bp => bp.BatchProductAdditionalCodes)
                    .HasForeignKey(bp => bp.IDBatchProduct)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_BatchProductAdditionalCode_BatchProduct");
            });

            //modelBuilder.Entity<BatchSerialization>(entity =>
            //{
            //    entity.HasOne(bs => bs.Batch)
            //        .WithOne(b => b.BatchSerialization)
            //        .HasForeignKey(bs => bs.IDBatch)
            //        .OnDelete(DeleteBehavior.Cascade)
            //        .HasConstraintName("FK_BatchSerialization_Batch");
            //});

            //modelBuilder.Entity<BatchSerialization>(entity =>
            //{
            //    entity.HasOne(e => e.Batch)
            //        .WithOne(e => e.BatchSerialization)
            //        .HasForeignKey<Batch>(e => e.IDBatch)
            //        .OnDelete(DeleteBehavior.Cascade)
            //        .HasConstraintName("FK_BatchSerialization_Batch");
            //});




            modelBuilder.Entity<BatchSerializationExtend>(entity =>
            {
                entity.HasOne(e => e.BatchSerialization)
                    .WithMany(b => b.BatchSerializationExtends)
                    .HasForeignKey(e => e.IDBatchSerialization)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_BatchSerializationExtend_BatchSerialization");
            });

            modelBuilder.Entity<BatchSerialsRequest>(entity =>
            {
                entity.HasOne(r => r.BatchSerialization)
                    .WithMany(bs => bs.BatchSerialsRequests)
                    .HasForeignKey(r => r.IDBatchSerialization)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_BatchSerialsRequest_BatchSerialization");

                entity.HasOne(r => r.BatchAggregation)
                  .WithMany(ba => ba.BatchSerialsRequests)
                  .HasForeignKey(r => r.IDBatchAggregation)
                  .OnDelete(DeleteBehavior.Cascade)
                  .HasConstraintName("FK_BatchSerialsRequest_BatchAggregation");
            });

            modelBuilder.Entity<LineRequest>(entity =>
            {
                entity.HasOne(lr => lr.BatchSerialization)
                    .WithMany(bs => bs.LineRequests)
                    .HasForeignKey(lr => lr.IDBatchSerialization)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_LineRequest_BatchSerialization");

                entity.HasOne(lr => lr.BatchAggregation)
                    .WithMany(ba => ba.LineRequests)
                    .HasForeignKey(lr => lr.IDBatchAggregation)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_LineRequest_BatchAggregation");


            });

            modelBuilder.Entity<BatchSerializationSerial>(entity =>
            {
                entity.HasOne(s => s.BatchSerialization)
                    .WithMany(b => b.BatchSerializationSerials)
                    .HasForeignKey(s => s.IDBatchSerialization)
                    .OnDelete(DeleteBehavior.Restrict)
                    .HasConstraintName("FK_BatchSerializationSerial_BatchSerialization");
            });

            modelBuilder.Entity<BatchAggregationSerialItem>(entity =>
            {
                entity.HasOne(i => i.BatchAggregationSerial)
                    .WithMany(s => s.BatchAggregationSerialItems)
                    .HasForeignKey(s => s.IDBatchAggregationSerial)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_BatchAggregationSerialItem_BatchAggregationSerial");
            });


            modelBuilder.Entity<ExtendPropertySelect>(entity =>
            {
                entity.HasOne(eps => eps.ExtendProperty)
                    .WithMany(ep => ep.ExtendPropertySelects)
                    .HasForeignKey(eps => eps.IDExtendProperty)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_ExtendPropertySelect_ExtendProperty");
            });

            modelBuilder.Entity<BatchStateTransition>(entity =>
            {
                entity.HasOne(b => b.BatchStateTarget)
                .WithMany(b => b.BatchStateTransitionsTarget)
                .HasForeignKey(b => b.IDBatchStateTarget)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_BatchStateTransition_BatchStateTarget");
            });

            modelBuilder.Entity<BatchStateTransition>(entity =>
            {
                entity.HasOne(b => b.BatchStateInitial)
                .WithMany(b => b.BatchStateTransitionsInitial)
                .HasForeignKey(b => b.IDBatchStateInitial)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_BatchStateTransition_BatchStateInitial");
            });

            modelBuilder.Entity<Machine>(entity =>
            {
                entity.HasOne(ms => ms.Line)
                    .WithMany(ms => ms.Machines)
                    .HasForeignKey(ms => ms.IDLine)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_Machine_Line");

                entity.HasOne(ms => ms.Batch)
                    .WithMany(ms => ms.Machines)
                    .HasForeignKey(ms => ms.IDBatch)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_Machine_Batch");

                entity.HasOne(ms => ms.BatchStateInProgress)
                  .WithMany(ms => ms.MachinesInProgress)
                  .HasForeignKey(ms => ms.IDStateInProgress)
                  .OnDelete(DeleteBehavior.Cascade)
                  .HasConstraintName("FK_Machine_StateInProgress");

                entity.HasOne(ms => ms.AggregationLevel)
                  .WithMany(ms => ms.Machines)
                  .HasForeignKey(ms => ms.IDAggregationLevel)
                  .OnDelete(DeleteBehavior.Cascade)
                  .HasConstraintName("FK_Machine_AggregationLevel");

            });


            modelBuilder.Entity<MachineSerial>(entity =>
            {
                entity.HasOne(ms => ms.Machine)
                   .WithMany(ms => ms.MachineSerials)
                   .HasForeignKey(ms => ms.IDMachine)
                   .OnDelete(DeleteBehavior.Cascade)
                   .HasConstraintName("FK_MachineSerial_Machine");
            });



            //todo modelBuilder.ApplyConfiguration<SerialStateAndCountResult>();

            modelBuilder.Entity<SerialStateCounter>().HasNoKey();
        }
    }
}
