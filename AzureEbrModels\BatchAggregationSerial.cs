﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEbrModels
{
    [Table("BatchAggregationSerial")]
    public class BatchAggregationSerial
    {
        public BatchAggregationSerial()
        {
            BatchAggregationSerialItems = new HashSet<BatchAggregationSerialItem>();
        }

        [Key]
        public long IDBatchAggregationSerial { get; set; }

        [Required]
        public int IDBatchAggregation { get; set; }

        [Required]
        [StringLength(100)]
        public string RequestID { get; set; }

        [Required]
        [StringLength(100)]
        public string PoolCode { get; set; }

        [Required]
        [StringLength(30)]
        public string SerialNumber { get; set; }

        [Required]
        public bool IsSGTIN { get; set; }

        [Required]
        public SerialStateEnum State { get; set; }

        public int? IDLineRequest { get; set; }

        [Required]
        public int ItemCount { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        [InverseProperty("BatchAggregationSerials")]
        public BatchAggregation BatchAggregation { get; set; }

        [InverseProperty("BatchAggregationSerial")]
        public ICollection<BatchAggregationSerialItem> BatchAggregationSerialItems { get; set; }
    }
}
