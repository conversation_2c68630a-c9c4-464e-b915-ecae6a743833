﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEbrModels
{
    [Table("ExtendProperty")]
    public class ExtendProperty
    {
        public ExtendProperty()
        {
            ExtendPropertySelects = new HashSet<ExtendPropertySelect>();
            TemplateExtendProperties = new HashSet<TemplateExtendProperty>();
        }

        [Key]
        public int IDExtendProperty { get; set; }

        [Required]
        public TableValuesEnum Table { get; set; }

        public int? IDAggregationLevel { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        public bool IsRequired { get; set; }

        public bool IsSelectValue { get; set; }

        [Required]
        public ExtendPropertyEnum Type { get; set; }

        [StringLength(1000)]
        public string Regex { get; set; }

        [StringLength(10)]
        public string GS1AICode { get; set; }

        public bool IsTemplate { get; set; }

        [StringLength(50)]
        public string Level { get; set; }

        [StringLength(50)]
        public string SubLevel { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        [InverseProperty("ExtendProperty")]
        public ICollection<ExtendPropertySelect> ExtendPropertySelects { get; set; }

        [InverseProperty("ExtendProperty")]
        public ICollection<TemplateExtendProperty> TemplateExtendProperties { get; set; }
    }


    public class TableValuesEnumForCombo
    {
        public TableValuesEnum ValueField { get; set; }
        public string TextField { get; set; }
    }

    public class ExtendPropertyEnumForCombo
    {
        public ExtendPropertyEnum ValueField { get; set; }
        public string TextField { get; set; }
    }
}
