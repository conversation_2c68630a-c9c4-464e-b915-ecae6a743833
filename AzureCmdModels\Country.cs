﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureCmdModels
{
    [Table("Country")]
    public class Country
    {
        [Key]
        [Column(TypeName = "char(2)")]
        [DisplayName("Country code")]
        public string Code { get; set; }

        [Required]
        [StringLength(50)]
        [DisplayName("Country name")]
        public string Name { get; set; }

        [StringLength(50)]
        [DisplayName("Country name")]
        public string NameSecLng { get; set; }

        [Column("GS1RNCode")]
        [DisplayName("GS1 RNCode")]
        public int? Gs1rncode { get; set; }
    }
}
