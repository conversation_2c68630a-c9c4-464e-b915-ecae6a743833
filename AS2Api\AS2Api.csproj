﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup Label="Globals">
    <SccProjectName>SAK</SccProjectName>
    <SccProvider>SAK</SccProvider>
    <SccAuxPath>SAK</SccAuxPath>
    <SccLocalPath>SAK</SccLocalPath>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>disable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>aspnet-AS2Api-0185c602-4d5a-4604-9dd5-275f65e98956</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.7" NoWarn="NU1605" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="8.0.7" NoWarn="NU1605" />
    <PackageReference Include="Microsoft.Identity.Web" Version="2.15.2" />
    <PackageReference Include="Microsoft.Identity.Web.DownstreamApi" Version="2.15.2" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AzureCmdModels\AzureCmdModels.csproj" />
    <ProjectReference Include="..\AzureEventHistoryApiModels\AzureEventHistoryApiModels.csproj" />
    <ProjectReference Include="..\AzureEventHistoryClient\AzureEventHistoryClient.csproj" />
  </ItemGroup>

</Project>
