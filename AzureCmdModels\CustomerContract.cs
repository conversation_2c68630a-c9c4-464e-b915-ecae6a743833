﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SGAuditTrail;

namespace AzureCmdModels
{
    [Table("CustomerContract")]
    [Audit(DisplayName = "Customer Contract")]
    public class CustomerContract
    {
        [Key]
        [DisplayName("ID")]
        public int IDCustomerContract { get; set; }

        public int IDCustomer { get; set; }

        [StringLength(100)]
        [DisplayName("Service name")]
        [Audit(DisplayName = "Service name")]
        public string Name { get; set; }

        [DisplayName("Start date")]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        [Audit(DisplayName = "Start date")]
        public DateTime StartDate { get; set; }

        [DisplayName("End date")]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        [Audit(DisplayName = "End date")]
        public DateTime? EndDate { get; set; }

        [DisplayName("Trial version")]
        [Audit(DisplayName = "Trial version")]
        public bool IsTrial { get; set; }

        [DisplayName("Trial end date")]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        [Audit(DisplayName = "Trial end date")]
        public DateTime? TrialEndDate { get; set; }

        [DisplayName("Signed contract")]
        [Audit(DisplayName = "Signed contract")]
        public bool IsContract { get; set; }

        [DisplayName("Contract Term")]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        [Audit(DisplayName = "Contract Term")]
        public DateTime? ContractTerm { get; set; }

        [StringLength(100)]
        [DisplayName("Contract data")]
        [Audit(DisplayName = "Contract data")]
        public string ContractData { get; set; }

        [DisplayName("Contract notify days")]
        [Audit(DisplayName = "Contract notify days")]
        public int ContractNotifyEndDays { get; set; }

        [DisplayName("Is payed")]
        [Audit(DisplayName = "Is payed")]
        public bool IsPayed { get; set; }

        [DisplayName("Payment Term")]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        [Audit(DisplayName = "Payment Term")]
        public DateTime? PaymentTerm { get; set; }

        [StringLength(100)]
        [DisplayName("Invoice data")]
        [Audit(DisplayName = "Invoice data")]
        public string InvoiceData { get; set; }

        [DisplayName("Payment notify days")]
        [Audit(DisplayName = "Payment notify days")]
        public int PaymentNotifyDays { get; set; }

        [DisplayName("Subscription canceled")]
        [Audit(DisplayName = "Subscription canceled")]
        public bool IsCanceled { get; set; }

        [StringLength(200)]
        [DisplayName("Cancel reason")]
        [Audit(DisplayName = "Cancel reason")]
        public string CancelReason { get; set; }


        [DisplayName("Timestamp created")]
        [Audit(DisplayName = "Timestamp created")]
        public DateTime TimestampCreated { get; set; }

        [StringLength(128)]
        [DisplayName("User created")]
        [Audit(DisplayName = "User created")]
        public string UserCreated { get; set; }


        [DisplayName("Timestamp udpated")]
        [Audit(DisplayName = "Timestamp updated")]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        [DisplayName("User updated")]
        [Audit(DisplayName = "User updated")]
        public string UserUpdated { get; set; }


        [InverseProperty("CustomerContracts")]
        public Customer Customer { get; set; }

    }
}
