# 1. Working certificate generation steps
## Generate private key
 ```cmd
   openssl genpkey -algorithm RSA -des3 -out private.key    

   genrsa -out as2client.key 2048
```
## Genrate signing Request
```cmd
    openssl req -new -key private.key -out request.csr  

    req -new -key as2client.key -out as2client.csr -config as2_openssl.conf // remove issuer for this command only
```
## Gerate Certificate
```cmd
oopenssl x509 -req -days 365 -in request.csr -signkey private.key -out certificate.crt

        x509 -req -in as2client.csr -signkey as2client.key -out as2client.crt -days 365 -extensions req_ext  -extfile cert.conf
```
## Check crt content

```cmd
openssl x509 -in certificate.crt -text -noout   
```
## Export pfx
```cmd
openssl pkcs12 -export -out certificate.pfx -inkey private.key -in certificate.crt -certfile ca_bundle.crt
    
openssl pkcs12 -export -out certificate.pfx -inkey private.key -in certificate.crt   
```
## Check content
```cmd
openssl pkcs12 -in certificate.pfx -info -noout        
```
## Convert to base64
```bash    
base64 certificate.pfx > certificate.pfx.b64  
```
```cmd
base64 -in certificate.p12 -out certifate.p12.b64
```
 

 # 2. Working tested convertion and p12 generation from private key and cert
- Combine private key and certificate in p12 
```cmd
pkcs12 -export -out certificate.p12 -inkey privateSg.key -in certificate.crt -password pass:SG2025@#$
```
 # 3. PKCS12 Generation to test
 - since the Mendelosn takes PKCS
 ```cmd
 # Create certificate compatible with .NET (PKCS#12)
openssl req -x509 -newkey rsa:2048 -keyout temp.key -out temp.crt -days 365 -nodes -subj "/CN=YourName"
openssl pkcs12 -export -out certificate.p12 -inkey temp.key -in temp.crt -password pass:yourpassword
 ``` 
 - Other optional considerations to test
 ```cmd
 # Create self-signed certificate with private key (PKCS#12 format - recommended)
openssl req -x509 -newkey rsa:2048 -keyout private.key -out certificate.crt -days 365 -nodes
openssl pkcs12 -export -out certificate.p12 -inkey private.key -in certificate.crt
```
```cmd
# Create self-signed certificate (PEM format)
openssl req -x509 -newkey rsa:2048 -keyout private.key -out certificate.crt -days 365 -nodes
```
```cmd
# Convert existing certificate to PKCS#12
openssl pkcs12 -export -out certificate.p12 -inkey private.key -in certificate.crt
```


##  06.06 -> Implementation Cert History

- Receiver cert key used and working (inknown):
MIIDZjCCAk4CCQDy/piqSDEKYjANBgkqhkiG9w0BAQsFADB1MQswCQYDVQQGEwJC RzEOMAwGA1UECAwFU29maWExDjAMBgNVBAcMBVNvZmlhMRIwEAYDVQQKDAlTb2Z0 R3JvdXAxEjAQBgNVBAsMCVNvZnRHcm91cDEeMBwGA1UEAwwVQVMyIENsaWVudCAt IHNhdHQubmV0MB4XDTI1MDYwNTEyNTM1N1oXDTI2MDYwNTEyNTM1N1owdTELMAkG A1UEBhMCQkcxDjAMBgNVBAgMBVNvZmlhMQ4wDAYDVQQHDAVTb2ZpYTESMBAGA1UE CgwJU29mdEdyb3VwMRIwEAYDVQQLDAlTb2Z0R3JvdXAxHjAcBgNVBAMMFUFTMiBD bGllbnQgLSBzYXR0Lm5ldDCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEB AMr2hIRIf/MWr8t6KUp9gCZ8q8p+9f7Epw5IJ9v0aQB5UvQMQd5ShTD1cQQb12fN 5Ou5I+0PS1lIvEvpGiVOQs6fI6vdlWKGQUVg9TZComjvYFha6TdXU9lVKj56n3R1 bOYdQpZKgwnbWKmCvlpCxJb9iN51d8X7aTodM9v0kmUbswX7mJeXNQB+kD9hkMnX qIyr+ruTcO0zYlzZm7ySi75oNsvRFxrlOTWGKVcNWprGPfWo4LniMzUE3clXN4Se /WrIee1BP6396Nvce1OLp84thzcb6hiOCTbRQkc/BPrmJcG4dzzj3n4QhoSasRwE dneOwFP9k1f6TvcCvKXN1JcCAwEAATANBgkqhkiG9w0BAQsFAAOCAQEAZcveZNPe 5CCUjMUN9xRXdb5neoAgBRjgAo2B5dGUnbq0r6vRv/fUKnOFqa0LStEjWBHhPXNM Gj7OMLfJmG2/bDlXwjkfMviSjfOvDO/mYIO1Sh4Ww3rA8dyVkbechjMdcoagYLtY kkzHMFOIKHsfLfNcnXmOAA/0qnFaMZs5DT0J20NpobTF8/WzxTKEeZBrhLUxzuDC /0km3X3U1iFuWpelCeh1pw/nBCbkuyGkVOOnoPyO06SxlafYdLGo6IRZ0wqeM76a Ftp2oA8LU4n8HMO2VYQfNlGU9/rOVDI6r1Ml85PUZlhwRLSwTuLPuUZlUTa9edNc LaROfZqin5b2AA==