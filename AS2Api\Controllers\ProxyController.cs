﻿using AS2Api.JsonModels;
using AzureCmdModels;
using AzureEventHistoryApiModels;
using AzureEventHistoryClient;
using AzureEventHistoryModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace AS2Api.Controllers
{
    [Route("api")]
    [ApiController]
#if DEBUG
    [Authorize]
#else
    [Authorize(Roles = Data.ADGroups.WebApi)]
#endif
    public class ProxyController : ControllerBase
    {
        private readonly GlobalVariables _globalVariables;
        private UserLogin userLogin { get; set; }
        private EventHistoryClient _eventHistoryClient;
        EventHistoryClientParams _eventHistoryClientParams;
        private Exception ConstructorException { get; set; }

        public ProxyController(GlobalVariables globalVariables, IConfiguration config)
        {
            try
            {
                _globalVariables = globalVariables;
                _eventHistoryClientParams = config.GetSection("EventHistoryClient").Get<EventHistoryClientParams>();
                _eventHistoryClient = new EventHistoryClient(_eventHistoryClientParams);

                if (_globalVariables.IsError)
                    throw new Exception(_globalVariables.ErrorMessage);
            }
            catch (Exception e)
            {
                ConstructorException = e;
            }
        }


        [HttpGet]
        [Produces(typeof(string))]
        public IActionResult Get()
        {
            try
            {
                if (ConstructorException != null)
                    return Problem(GetExceptionMessage(ConstructorException));

                if (!GetUserLogin())
                    return Unauthorized();

                return Ok($"SATT PLATFORM Global SNX Api - v1 {userLogin.LgnName}");
            }
            catch (Exception e)
            {
                return BadRequest(e.Message);
            }
        }

        [HttpPost]
        [Produces(typeof(string))]
        public IActionResult PostMessage(AS2Message aS2Message)
        {
            try
            {
                if (aS2Message == null
                    || string.IsNullOrEmpty(aS2Message.Message))

                {
                    return BadRequest("Empty or invalid message data.");
                }

                if (_globalVariables.IsError)
                    return BadRequest(_globalVariables.ErrorMessage ?? "Initialization error.");



                throw new NotImplementedException();
            }
            catch (Exception e)
            {
                return BadRequest(e.Message);
            }
        }

        private void CreateEventHistoryRecord(string methodName,
                                                    string masterName,
                                                    string masterValue,
                                                    EventHistoryStateEnum state,
                                                    string lgnName,
                                                    int? idCustomer,
                                                    string resultMessage,
                                                    object detailsObject = null)
        {
            try
            {
                EventHistoryApi newEventHistoryApi = new EventHistoryApi
                {
                    State = state,
                    EventID = Guid.NewGuid().ToString(),
                    IDCustomer = idCustomer == null ? 0 : idCustomer,
                    CustomerName = string.IsNullOrEmpty(lgnName) ? "N/A" : lgnName,
                    Sender = "GSNX API",
                    Receiver = "", //когато е през SFTP и във файла има това поле
                    ApplicationName = "SATT PLATFORM GSNX", //името на приложението
                    EventName = methodName, //името на метода
                    MasterKeyName = masterName,
                    MasterKeyValue = masterValue,
                    WorkFolder = "", // когато е през SFTP
                    TimestampCreated = DateTime.Now,
                    UserCreated = userLogin.LgnName,
                    EventResults = new List<EventResultApi>(),
                    EventDetails = GetObjectDetails(detailsObject)
                };

                newEventHistoryApi.EventResults.Add(new EventResultApi
                {
                    Result = resultMessage
                });

                _eventHistoryClient.SendHistory(newEventHistoryApi);
            }
            catch (Exception)
            {

            }
        }

        private static List<EventDetailApi> GetObjectDetails(object obj, string parent = "")
        {
            List<EventDetailApi> result = new List<EventDetailApi>();
            try
            {
                if (obj == null)
                    return result;

                foreach (var property in obj.GetType().GetProperties())
                {
                    if (property.PropertyType == typeof(string)
                        || property.PropertyType == typeof(int)
                        || property.PropertyType == typeof(short)
                        || property.PropertyType == typeof(decimal)
                        || property.PropertyType == typeof(bool))
                    {
                        var value = property.GetValue(obj);
                        if (value != null)
                            result.Add(new EventDetailApi() { Key = parent + property.Name, Value = value.ToString() });
                    }
                    else if (property.PropertyType.IsClass)
                    {
                        var value = property.GetValue(obj);
                        if (value != null)
                        {
                            var classValue = GetObjectDetails(value, parent + property.Name + ".");
                            if (classValue != null && classValue.Count > 0)
                                result.AddRange(classValue);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                result.Add(new EventDetailApi() { Key = "Exception", Value = e.Message });
            }

            return result;
        }

        private bool GetUserLogin()
        {
            try
            {
                userLogin = _globalVariables.UserLogin;
                if (userLogin != null)
                    return true;
                else
                    return false;

            }
            catch (Exception)
            {
                return false;
            }
        }

        private static string GetExceptionMessage(Exception e)
        {
            if (e == null)
                return "Empty exception";

            return $"{e.Message} {e.InnerException?.Message}";
        }
    }
}
