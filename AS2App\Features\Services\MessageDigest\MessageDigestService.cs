using System.Security.Cryptography;
using System.Text;
using AS2Test.Models;
using AS2Test_master.Models;
using MimeKit;


namespace AS2Test.Features.Services.MessageDigest
{
    public class MessageDigestService
    {

        /// <summary>
        /// Calculates the original message digest for a given MIME message using the specified algorithm.
        /// </summary>
        /// <param name="mimeMessage">The MIME message to calculate the digest for.</param>
        /// <param name="algorithm">The algorithm to use for calculating the digest.</param>
        /// <returns>The calculated original message digest.</returns>
        public static string CalculateOriginalMessageDigest(string mimeMessage, MessageIntegrityCheckAlgorithms algorithm)
        {
            var message = MimeMessage.Load(new MemoryStream(Encoding.ASCII.GetBytes(mimeMessage)));
            byte[] content;
            
            using (var memoryStream = new MemoryStream())
            {
                message.WriteTo(memoryStream);
                content = memoryStream.ToArray();
            }
            return CalculateHash(content, algorithm);
        }


        public static string CalculateOriginalMessageDigest(MimeMessage mimeMessage, MessageIntegrityCheckAlgorithms algorithm)
        {
            byte[] content;
            
            using (var memoryStream = new MemoryStream())
            {
                mimeMessage.WriteTo(memoryStream);
                content = memoryStream.ToArray();
            }
            return CalculateHash(content, algorithm);
        }


        private static string CalculateHash(byte[] content, MessageIntegrityCheckAlgorithms algorithm)
        {
            byte[] hash;
            switch (algorithm)
            {
                case MessageIntegrityCheckAlgorithms.SHA1:
                    using (var sha1 = SHA1.Create())
                    {
                        hash = sha1.ComputeHash(content);
                    }
                    break;
                case MessageIntegrityCheckAlgorithms.SHA256:
                    using (var sha256 = SHA256.Create())
                    {
                        hash = sha256.ComputeHash(content);
                    }
                    break;
                case MessageIntegrityCheckAlgorithms.SHA384:
                    using (var sha384 = SHA384.Create())
                    {
                        hash = sha384.ComputeHash(content);
                    }
                    break;
                case MessageIntegrityCheckAlgorithms.SHA512:
                    using (var sha512 = SHA512.Create())
                    {
                        hash = sha512.ComputeHash(content);
                    }
                    break;
                default:
                    throw new ArgumentOutOfRangeException(nameof(algorithm), algorithm, null);
            }
            return Convert.ToBase64String(hash);
        }

        /*
        public static string CalculaetSignedMassageMic(string message, MicAlgorithm algorithm)
        {
            var mimeMessage = MimeMessage.Load(new MemoryStream(Encoding.ASCII.GetBytes(message)));

            byte[] content;

            using(var memoryStream = new MemoryStream())
            {
                if(mimeMessage.Body is ApplicationPkcs7Mime pkcs7Mime)
                {
                    using var mimeContent = new MemoryStream();
                    pkcs7Mime.Content.DecodeTo(mimeContent);
                    mimeContent.Position = 0;
                    // Todo: Decrypt and verify signature and content here
                    using var streamReader = new StreamReader(mimeContent);
                   
                    var signedContent = streamReader.ReadToEnd();
                }
            }
            
            return CalculateHash(content, algorithm);
        }
        */
    }
}