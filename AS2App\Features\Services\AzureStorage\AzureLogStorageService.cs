using System;
using System.IO;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using AS2App.Models.ConfigModels;

namespace Features.Services.AzureLogStorageService
{

    public class AzureLogStorageService : IAzureLogStorageService
    {
        private readonly ILogger<AzureLogStorageService> _logger;
        private readonly IOptionsMonitor<AS2Settings> _as2Settings;

        public AzureLogStorageService(
            ILogger<AzureLogStorageService> logger,
            IOptionsMonitor<AS2Settings> as2Settings)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _as2Settings = as2Settings ?? throw new ArgumentNullException(nameof(as2Settings));
        }

        public void LogObjectToStorage<T>(T obj, string fileNamePrefix, string messageId)
        {
            try
            {
                if (_as2Settings.CurrentValue.AzureStorageSystemLogEnabled)
                {
                    string datePart = DateTime.Now.ToString("yyyy-MM-dd");
                    string timePart = DateTime.Now.ToString("yyyyMMdd-HHmmss");
                    string sanitizedMessageId = messageId.Replace("<", "").Replace(">", "");

                    string azureFilePath = $@"/as2-azure-{datePart}/{fileNamePrefix}-{sanitizedMessageId}-{timePart}.json";

                    string storageSystem = _as2Settings.CurrentValue.AzureStorageSystem;
                    string azureStorageConnectionString = _as2Settings.CurrentValue.AzureStorageConnectionString;

                    if (!Enum.TryParse(storageSystem, out Softgroup.FileStorage.StorageTypeEnum storageType))
                    {
                        _logger.LogError($"Unsupported file storage system [{storageSystem}]");
                        return;
                    }

                    if (string.IsNullOrEmpty(azureStorageConnectionString))
                    {
                        _logger.LogError("Azure Storage Connection String is not configured.");
                        return;
                    }

                    using var stream = new MemoryStream();
                    JsonSerializer.Serialize(stream, obj);
                    stream.Position = 0;

                    Softgroup.FileStorage.FileStorage.Save(storageType, stream, azureFilePath, azureStorageConnectionString);
                }
                else
                {
                    _logger.LogInformation($"Azure storage logging is disabled. Not logging {fileNamePrefix} to Azure storage.");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to log {fileNamePrefix} to Azure storage");
            }
        }
    }
}
