﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureCmdModels
{
    [Table("SupportedFormat")]
    public class SupportedFormat
    {

        public SupportedFormat()
        {
           // ConvertedFiles = new HashSet<ConvertedFile>();
            CustomerSupportedFormats = new HashSet<CustomerSupportedFormat>();
            AdditionalParams = new HashSet<AdditionalParam>();
        }

        [Key]
        public int IDSupportedFormat { get; set; }

        [Required]
        [StringLength(100)]
        public string FileFormat { get; set; }

        [StringLength(150)]
        public string? DisplayFormat { get; set; }

        [Required]
        [StringLength(100)]
        public string sgConvertToolName { get; set; }

        [DisplayFormat(DataFormatString = "{0: dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0: dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string? UserUpdated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [StringLength(500)]
        public string? Parameters { get; set; }

        public bool IsTargetSgXml { get; set; }

//        [InverseProperty("SupportedFormat")]
  //      public ICollection<ConvertedFile> ConvertedFiles { get; set; }


        [InverseProperty("SupportedFormat")]
        public ICollection<CustomerSupportedFormat> CustomerSupportedFormats { get; set; }

        [InverseProperty("SupportedFormat")]
        public ICollection<AdditionalParam> AdditionalParams { get; set; }


    }
}
