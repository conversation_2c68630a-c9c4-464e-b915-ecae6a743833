﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SGAuditTrail;

namespace AzureCmdModels
{
    [Table("Manufacturer")]
    [Audit(DisplayName = "Manufacturer")]
    public class Manufacturer
    {
        public Manufacturer()
        {
            UserLogins = new HashSet<UserLogin>();
            ServiceUserLogins = new HashSet<ServiceUserLogin>();
        }

        public override bool Equals(object obj)
        {
            if (obj == null)
                return false;

            if (obj.GetType() == this.GetType())
            {
                if (((Manufacturer)obj).IDMah == this.IDMah)
                    return true;
                else
                    return false;
            }
            else
                return false;
        }

        [Key]
        [DisplayName("IDMah")]
        public int IDMah { get; set; }

        [StringLength(100)]
        [DisplayName("GS1 Company Prefix")]
        [Audit(DisplayName = "GS1 Company Prefix")]
        public string GS1CompanyPrefix { get; set; }

        [StringLength(20)]
        [DisplayName("SGLN")]
        [Audit(DisplayName = "SGLN")]
        public string SGLN { get; set; }

        [StringLength(13)]
        [DisplayName("GLN")]
        [Audit(DisplayName = "GLN")]
        public string GLN { get; set; }


        [Column("IDCustomer", TypeName = "id")]
        [DisplayName("Customer")]
        [Audit(DisplayName = "Customer", InversePropertyName = "Customer", InversePropertyValue = "Name")]
        public int IDCustomer { get; set; }

        [Column("IDMahType")]
        [DisplayName("Type")]
        [Audit(DisplayName = "Manufacturer Type", InversePropertyName = "MahType", InversePropertyValue = "Type")]
        public int? IDMahType { get; set; }

        [Column("MahID")]
        [StringLength(50)]
        [DisplayName("ID")]
        public string MahID { get; set; }

        [Required]
        [StringLength(100)]
        [DisplayName("Name")]
        [Audit(DisplayName = "Name")]
        public string MahName { get; set; }

        [StringLength(255)]
        [DisplayName("Street")]
        [Audit(DisplayName = "Street")]
        public string MahStreet { get; set; }

        [StringLength(255)]
        [DisplayName("Street 2")]
        [Audit(DisplayName = "Street 2")]
        public string MahStreet2 { get; set; }

        [StringLength(100)]
        [DisplayName("City")]
        [Audit(DisplayName = "City")]
        public string MahCity { get; set; }

        [StringLength(50)]
        [DisplayName("Post code")]
        [Audit(DisplayName = "Post Code")]
        public string MahPostCode { get; set; }

        [Column(TypeName = "char(2)")]
        [DisplayName("Country")]
        [Audit(DisplayName = "Country Code")]
        public string MahCountryCode { get; set; }

        [Column(TypeName = "datetime")]
        [DataType(DataType.Date)]
        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        [DisplayName("Created")]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        [DisplayName("User Created")]
        public string UserCreated { get; set; }

        [Column(TypeName = "datetime")]
        [DataType(DataType.Date)]
        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        [ForeignKey(nameof(IDMahType))]
        [InverseProperty("Manufacturers")]
        [DisplayName("Type")]
        public MahType MahType { get; set; }

        [InverseProperty("Manufacturers")]
        public Customer Customer { get; set; }

        [InverseProperty("Manufacturer")]
        public ICollection<UserLogin> UserLogins { get; set; }

        [InverseProperty("Manufacturer")]
        public ICollection<ServiceUserLogin> ServiceUserLogins { get; set; }

        [DisplayName("Active")]
        [Audit(DisplayName = "Active", ValueTrue = "Yes", ValueFalse = "No")]
        public bool IsActive { get; set; }

        public AuditTrailHeader GetAuditTrailHeader(string user, int page, int pages)
        {
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

            AuditTrailHeader header = new AuditTrailHeader()
            {
                Controler = "Manufacturers",
                ID = IDMah.ToString(),
                UserName = user,
                Page = page,
                Pages = pages
            };

            header.AddItem("Manufacturer", MahName);
            header.AddItem("User", user);
            header.AddItem("Timestamp", timestamp);

            return header;
        }
    }

}
