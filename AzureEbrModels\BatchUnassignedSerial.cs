﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEbrModels
{
    [Table("BatchUnassignedSerials")]
    public class BatchUnassignedSerial
    {
        [Key]
        public long IDBatchUnassignedSerial { get; set; }

        [Required]
        public long IDBatchSerializationSerial { get; set; }

        [Required]
        public int IDBatchSerialization { get; set; }

        [Required]
        [StringLength(100)]
        public string RequestId { get; set; }

        [Required]
        [StringLength(100)]
        public string PoolCode { get; set; }

        [Required]
        [StringLength(100)]
        public string SerialNumber { get; set; }

        [Required]
        public bool Returned { get; set; }

        [Required]
        public int IDBatchSerialsRequest { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }
    }
}
