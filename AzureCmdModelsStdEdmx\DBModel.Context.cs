﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace AzureCmdModelsStdEdmx
{
    using System;
    using System.Data.Entity;
    using System.Data.Entity.Infrastructure;
    
    public partial class DBModel : DbContext
    {
        public DBModel()
            : base("name=DBModel")
        {
            Database.CommandTimeout = 600;
        }
    
        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            
        }
    
        public virtual DbSet<AdditionalCodeType> AdditionalCodeTypes { get; set; }
        public virtual DbSet<AuditTrail> AuditTrails { get; set; }
        public virtual DbSet<AuditTrailItem> AuditTrailItems { get; set; }
        public virtual DbSet<BusinessSegment> BusinessSegments { get; set; }
        public virtual DbSet<CommunicationChannel> CommunicationChannels { get; set; }
        public virtual DbSet<Contract> Contracts { get; set; }
        public virtual DbSet<ContractStatu> ContractStatus { get; set; }
        public virtual DbSet<ContractType> ContractTypes { get; set; }
        public virtual DbSet<Country> Countries { get; set; }
        public virtual DbSet<Customer> Customers { get; set; }
        public virtual DbSet<CustomerAggregationLevel> CustomerAggregationLevels { get; set; }
        public virtual DbSet<CustomerContract> CustomerContracts { get; set; }
        public virtual DbSet<CustomerLabelDataSource> CustomerLabelDataSources { get; set; }
        public virtual DbSet<CustomerPartner> CustomerPartners { get; set; }
        public virtual DbSet<CustomerPartnerParameter> CustomerPartnerParameters { get; set; }
        public virtual DbSet<CustomerService> CustomerServices { get; set; }
        public virtual DbSet<InternalCodeType> InternalCodeTypes { get; set; }
        public virtual DbSet<LabelTemplate> LabelTemplates { get; set; }
        public virtual DbSet<MahType> MahTypes { get; set; }
        public virtual DbSet<Manufacturer> Manufacturers { get; set; }
        public virtual DbSet<Product> Products { get; set; }
        public virtual DbSet<ProductAdditionalCode> ProductAdditionalCodes { get; set; }
        public virtual DbSet<ProductCodeType> ProductCodeTypes { get; set; }
        public virtual DbSet<ProductLabel> ProductLabels { get; set; }
        public virtual DbSet<SerializationType> SerializationTypes { get; set; }
        public virtual DbSet<SerialNumberSourceType> SerialNumberSourceTypes { get; set; }
        public virtual DbSet<Service> Services { get; set; }
        public virtual DbSet<ServiceContract> ServiceContracts { get; set; }
        public virtual DbSet<ServiceEnvironment> ServiceEnvironments { get; set; }
        public virtual DbSet<ServiceRole> ServiceRoles { get; set; }
        public virtual DbSet<ServiceUserLogin> ServiceUserLogins { get; set; }
        public virtual DbSet<TargetMarket> TargetMarkets { get; set; }
        public virtual DbSet<UserLogin> UserLogins { get; set; }
        public virtual DbSet<UserLoginServiceRole> UserLoginServiceRoles { get; set; }
        public virtual DbSet<UserPrinterSetting> UserPrinterSettings { get; set; }
    }
}
