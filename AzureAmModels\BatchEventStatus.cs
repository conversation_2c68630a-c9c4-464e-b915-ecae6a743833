﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureAmModels
{
    [Table("BatchEventStatus")]
    public class BatchEventStatus
    {

        [Key]
        public int IDBatchEventStatus { get; set; }

        [Required]
        public int IDBatchEvent { get; set; }

        [StringLength(256)]
        public string FieldName { get; set; }

        [StringLength(256)]
        public string FieldValue { get; set; }

        public bool IsError { get; set; }

        public bool IsInfo { get; set; }

        public bool IsWarrning { get; set; }

        [Required]
        [DisplayName("Timestamp created")]
        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [DisplayName("User created")]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [InverseProperty("BatchEventStatuses")]
        public BatchEvent BatchEvent { get; set; }


        public override bool Equals(object obj)
        {
            if (obj == null)
                return false;

            if (obj.GetType() == this.GetType())
            {
                if (((BatchEventStatus)obj).IDBatchEventStatus == this.IDBatchEventStatus)
                    return true;
                else
                    return false;
            }
            else
                return false;
        }

    }
}
