//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace AzureCmdModelsStdEdmx
{
    using System;
    using System.Collections.Generic;
    
    public partial class TargetMarket
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public TargetMarket()
        {
            this.ProductLabels = new HashSet<ProductLabel>();
        }
    
        public int IDTargetMarket { get; set; }
        public string Name { get; set; }
        public string ShortName { get; set; }
        public Nullable<int> GS1RNCode { get; set; }
        public System.DateTime TimestampCreated { get; set; }
        public string UserCreated { get; set; }
        public Nullable<System.DateTime> TimestampUpdated { get; set; }
        public string UserUpdated { get; set; }
        public string SerializationType { get; set; }
        public string SrcSystem { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<ProductLabel> ProductLabels { get; set; }
    }
}
