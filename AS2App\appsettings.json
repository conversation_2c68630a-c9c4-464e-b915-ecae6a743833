{"AzureAd": {"Instance": "https://login.microsoftonline.com/", "Domain": "softgroup.eu", "TenantId": "6db202c3-153d-4367-864a-8b7a54d46b00", "ClientId": "d004fb86-ed51-4a7f-a189-e8ebe245dc95", "ClientSecret": "*************************************", "CallbackPath": "/signin-oidc"}, "AzureAd_Test": {"Instance": "https://login.microsoftonline.com/", "Domain": "satt.systems", "TenantId": "febb5383-d8e4-4bb9-8393-d08f0e71a005", "ClientId": "610a8780-7b62-4a7a-9991-56c695c0d561", "CallbackPath": "/signin-oidc", "SignedOutCallbackPath": "/signout-callback-oidc"}, "AzureAd_Prod": {"Instance": "https://login.microsoftonline.com/", "Domain": "satt.systems", "TenantId": "febb5383-d8e4-4bb9-8393-d08f0e71a005", "ClientId": "488d06cf-876b-4400-be46-8efecd7e180f", "CallbackPath": "/signin-oidc", "SignedOutCallbackPath": "/signout-callback-oidc"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "CertiicatePath": "", "As2Settings": {"ServerTimeoutSetting": 5, "MicExpireTime": 6, "AzureStorageSystemLogEnabled": false, "AzureStorageSystem": "AZURE_BLOBSTORAGE", "AzureStorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=sg75storaccount;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"}, "KeyVault": {"VaultUri": "https://key-vault-name.vault.azure.net/", "ClientId": "client-id", "ClientSecret": "client-secret", "TenantId": "tenant-id"}, "ConnectionStrings": {"AzureAS2Proxy": "Data Source=s1sr074v; Initial Catalog=AS2Proxy-Work;Trusted_Connection=True;MultipleActiveResultSets=True; Persist Security Info=True;TrustServerCertificate=True;", "AzureAS2Proxy_Test": "Data Source=s1sr074v; Initial Catalog=AS2Proxy-Work;Trusted_Connection=True;MultipleActiveResultSets=True; Persist Security Info=True;TrustServerCertificate=True;", "AzureAS2Proxy_Prod": "Data Source=s1sr074v; Initial Catalog=AS2Proxy-Work;Trusted_Connection=True;MultipleActiveResultSets=True; Persist Security Info=True;TrustServerCertificate=True;"}, "AllowedHosts": "*"}