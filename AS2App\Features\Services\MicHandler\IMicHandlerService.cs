﻿namespace AS2App.Features.Services.MicHandler
{
    public interface IMicHandlerService
    {
        /// <summary>
        /// Validates the MIC (Message Integrity Check) of the provided message.
        /// </summary>
        /// <param name="message">The message to validate.</param>
        /// <param name="mic">The MIC to validate against the message.</param>
        /// <returns>True if the MIC is valid, otherwise false.</returns>
        bool ValidateMic(string mic, string messageID);
        /// <summary>
        /// Generates a MIC for the provided message.
        /// </summary>
        /// <param name="message">The message to generate a MIC for.</param>
        /// <returns>The generated MIC as a string.</returns>
        void GenerateSaveMic(byte[] byteMessage, string messageId, string integrityCheckAlgorithm);
    }
}
