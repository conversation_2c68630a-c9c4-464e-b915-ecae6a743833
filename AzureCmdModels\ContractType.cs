﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SGAuditTrail;

namespace AzureCmdModels
{
    [Table("ContractType")]
    [Audit(DisplayName = "Contract Type")]
    public partial class ContractType
    {

        public ContractType()
        {
            Contracts = new HashSet<Contract>();
        }

        [Key]
        public int IDContractType { get; set; }

        [DisplayName("Contract Type")]
        [StringLength(128)]
        public string Name { get; set; }

        [InverseProperty("ContractType")]
        public ICollection<Contract> Contracts { get; set; }
    }
}
