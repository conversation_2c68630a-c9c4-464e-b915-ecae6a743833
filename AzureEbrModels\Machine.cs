﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEbrModels
{
    [Table("Machine")]
    public class Machine
    {
        public Machine()
        {
            MachineSerials = new HashSet<MachineSerial>();
        }

        [Key]
        public int IDMachine { get; set; }

        [Required]
        [StringLength(50)]
        [DisplayName("Code")]
        public string Code { get; set; }

        [Required]
        [StringLength(255)]
        [DisplayName("Name")]
        public string Name { get; set; }

        [StringLength(255)]
        [DisplayName("Location")]
        public string Location { get; set; }

        [Required(ErrorMessage = "Field Line is required")]
        public int? IDLine { get; set; }

        [StringLength(50)]
        [DisplayName("User")]
        public string User { get; set; }

        [StringLength(50)]
        [DisplayName("User Verify")]
        public string UserVerify { get; set; }

        [Required(ErrorMessage = "Field Accepeted State is required")]
        [DisplayName("Accepted State")]
        public int? IDStateInProgress { get; set; }

        public int? IDBatch { get; set; }

        public int? IDVMOrder { get; set; }

        [Required(ErrorMessage = "Field Aggregation Level is required")]
        public int? IDAggregationLevel { get; set; }

        public DateTime? LastStateDateTime { get; set; }

        [Required]
        [DisplayName("Timestamp created")]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        [DisplayName("User Created")]
        public string UserCreated { get; set; }

        [DisplayName("Timestamp Updated")]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        [DisplayName("User Updated")]
        public string UserUpdated { get; set; }

        [InverseProperty("Machines")]
        public Line Line { get; set; }

        [InverseProperty("MachinesInProgress")]
        public BatchState BatchStateInProgress { get; set; }

        [InverseProperty("Machines")]
        public Batch Batch { get; set; }

        [InverseProperty("Machines")]
        public AggregationLevel AggregationLevel { get; set; }

        [InverseProperty("Machine")]
        public ICollection<MachineSerial> MachineSerials { get; set; }

    }
}
