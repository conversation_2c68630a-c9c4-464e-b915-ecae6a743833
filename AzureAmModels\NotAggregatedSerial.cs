﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureAmModels
{
    [Table("NotAggregatedSerial")]
    public class NotAggregatedSerial
    {
        [Key]
        public int IDNotAggregatedSerial { get; set; }

        [Required]
        public int IDBatch { get; set; }

        [Required]
        [StringLength(30)]
        public string SerialNumber { get; set; }

        [Required]
        [StringLength(20)]
        public string Level { get; set; }

        [Required]
        [StringLength(20)]
        public string ItemType { get; set; }

        [StringLength(50)]
        public string State { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [InverseProperty("NotAggregatedSerials")]
        public Batch Batch { get; set; }
    }
}
