﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;

namespace AzureBhModels
{

    [Table("SgFile")]
    public class SgFile : INotifyPropertyChanged
    {
        public SgFile()
        {
            BatchAggregations = new HashSet<BatchAggregation>();
            SgFileNotAggregatedNumbers = new HashSet<SgFileNotAggregatedNumber>();
            SgFileVerificationErrors = new HashSet<SgFileVerificationError>();
        }

        [Key]
        public int IDSgFile { get; set; }

        public int? IDBatch { get; set; }

        [Required]
        [StringLength(500)]
        public string FileName { get; set; }

        [Required]
        [StringLength(1000)]
        public string FilePath { get; set; }

        [StringLength(200)]
        public string StorageSystem { get; set; }

        public bool IsChanged { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [InverseProperty("SgFiles")]
        public Batch Batch { get; set; }

        [InverseProperty("SgFile")]
        public ICollection<BatchAggregation> BatchAggregations { get; set; }

        [InverseProperty("SgFile")]
        public ICollection<SgFileNotAggregatedNumber> SgFileNotAggregatedNumbers { get; set; }


        [InverseProperty("SgFile")]
        public ICollection<SgFileVerificationError> SgFileVerificationErrors { get; set; }

        /*  [InverseProperty("SgFile")]
          public ICollection<AuditTrailFile> AuditTrailFiles { get; set; } */ //todo


        public event PropertyChangedEventHandler PropertyChanged;
        private void NotifyPropertyChanged([CallerMemberName] string propertyName = "")
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /* public SgFile UpdateSgFileVerificationErrors(int idSgFile, SgVerificationResult sgVerificationResult, UserLogin userLogin, IDBService dbService) //todo
         {
             try
             {
                 var sgFile = dbService.UpdateSgFileVerificationErrors(idSgFile, sgVerificationResult, userLogin);
                 NotifyPropertyChanged(nameof(SgFileVerificationErrors));

                 return sgFile;
             }
             catch (Exception e)
             {

                 return null;
             }
         } */
        //TODO
        public string GetFilePath()
        {
            string changedFileSuffix = "_Changed";
            string filePath = this.FilePath;
            if (this.IsChanged)
            {
                string path = Path.GetDirectoryName(filePath);
                string fnwe = Path.GetFileNameWithoutExtension(this.FileName);
                string fe = Path.GetExtension(this.FileName);
                filePath = Path.Combine(path, $"{fnwe}{changedFileSuffix}{fe}");
            }
            return filePath;
        }
    }
}
