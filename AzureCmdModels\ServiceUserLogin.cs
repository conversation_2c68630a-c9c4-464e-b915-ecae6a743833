﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SGAuditTrail;

namespace AzureCmdModels
{
    [Table("ServiceUserLogin")]
    [Audit(DisplayName = "ServiceUserLogin")]
    public class ServiceUserLogin
    {
        [Key]
        public int IDServiceUserLogin { get; set; }

        [DisplayName("Service")]
        public int IDService { get; set; }

        [StringLength(50)]
        [DisplayName("Login Name")]
        public string LgnName { get; set; }

        [StringLength(250)]
        [DisplayName("External Sender Code")]
        public string ExternalSenderCode { get; set; }

        [StringLength(250)]
        [DisplayName("External Reciever Code")]
        public string ExternalRecieverCode { get; set; }

        [Column("IDManufacturer")]
        [DisplayName("Manufacturer")]
        [Audit(DisplayName = "Manufacturer", InversePropertyName = "Manufacturer", InversePropertyValue = "MahName", NullValue = "No Manufacturer")]
        public int? IDManufacturer { get; set; }

        [InverseProperty("ServiceUserLogins")]
        public Service Service { get; set; }

        [InverseProperty("ServiceUserLogins")]
        public Manufacturer Manufacturer { get; set; }

        [InverseProperty("ServiceUserLogins")]
        public UserLogin UserLogin { get; set; }

    }
}
