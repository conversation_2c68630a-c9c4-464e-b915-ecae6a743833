﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureAmModels
{
    [Table("Order")]
    public class Order
    {
        public Order()
        {
            OrdersHistory = new HashSet<OrderHistory>();
            OrderProductCryptoCodes = new HashSet<OrderCryptoCode>();
            Serials = new HashSet<OrderSerial>();
        }

        [Key]
        public int IDOrder { get; set; }

        [Required]
        public int IDProduct { get; set; }

        [DisplayName("Expected Complete Time")]
        [DisplayFormat(DataFormatString = "{0: dd.MM.yyyy HH:mm:ss}")]
        public DateTime? ExpectedCompleteTimestamp { get; set; }

        [Required]
        public int Quantity { get; set; }

        public int TemplateID { get; set; }

        [DisplayName("Order ID")]
        public string OrderID { get; set; }

        [StringLength(50)]
        [DisplayName("Production Order Id")]
        public string ProductionOrderId { get; set; }


        [StringLength(128)]
        [DisplayName("Contact Person")]
        public string ContactPerson { get; set; }

        [StringLength(300)]
        [DisplayName("Description")]
        public string Description { get; set; }

        public string OrderHistory { get; set; }

        public OrderStatusEnum Status { get; set; }

        [DisplayName("Buffer status")]
        public BufferStatusEnum? BufferStatus { get; set; }

        public bool IsApi { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        [DisplayName("Created")]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        [DisplayName("User Created")]
        public string UserCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        [InverseProperty("Order")]
        public ICollection<OrderHistory> OrdersHistory { get; set; }

        [InverseProperty("Order")]
        public ICollection<OrderSerial> Serials { get; set; }


        [InverseProperty("Order")]
        public ICollection<OrderCryptoCode> OrderProductCryptoCodes { get; set; }


        public override bool Equals(object obj)
        {
            if (obj == null)
                return false;

            if (obj.GetType() == this.GetType())
            {
                if (((Order)obj).IDOrder == this.IDOrder)
                    return true;
                else
                    return false;
            }
            else
                return false;
        }
    }
}
