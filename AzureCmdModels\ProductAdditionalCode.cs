﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SGAuditTrail;

namespace AzureCmdModels
{
    [Table("ProductAdditionalCode")]
    [Audit(DisplayName = "Product Additional Code")]
    public class ProductAdditionalCode
    {

        [Key]
        public int IDProductAdditionalCode { get; set; }

        public int IDProduct { get; set; }

        [StringLength(50)]
        [DisplayName("Code Type")]
        [Audit(DisplayName = "Code Type")]
        public string CodeType { get; set; }

        [Required]
        [StringLength(50)]
        [Audit(DisplayName = "Code")]
        public string Code { get; set; }

        [Required]
        [DisplayName("Timestamp created")]
        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [DisplayName("User created")]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [DisplayName("Timestamp updated")]
        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [DisplayName("User updated")]
        [StringLength(128)]
        public string UserUpdated { get; set; }

        [InverseProperty("ProductAdditionalCodes")]
        public AdditionalCodeType AdditionalCodeType { get; set; }

        [InverseProperty("ProductAdditionalCodes")]
        public Product Product { get; set; }

    }
}
