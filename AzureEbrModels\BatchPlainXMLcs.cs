﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Serialization;

namespace AzureEbrModels
{
    [Serializable()]
    [XmlRoot("EBR")]
    public class BatchPlainXMLcs
    {
        public ThermalLabelDataSource Label { get; set; }

        public string XmlToString()
        {
            MemoryStream stream = new MemoryStream();
            XmlSerializer serializer = new XmlSerializer(this.GetType());
            using (StreamWriter writer = new StreamWriter(stream))
            {
                serializer.Serialize(writer, this);
            }

            var bytes = stream.ToArray();
            string text = Encoding.UTF8.GetString(bytes);

            return text;
        }

        public Stream XmlToStream()
        {
            MemoryStream stream = new MemoryStream();
            XmlSerializer serializer = new XmlSerializer(this.GetType());
            StreamWriter writer = new StreamWriter(stream);
            serializer.Serialize(writer, this);
            stream.Position = 0;
            return stream;
        }
    }
    [Serializable]
    public class ThermalLabelDataSource
    {

        public string BatchID { get; set; }
        public string ProductCode { get; set; }
        public string ProductName { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public DateTime? ManufacturingDate { get; set; }
        public string DayFormat { get; set; }
        public string DateFormat { get; set; }
        public string Type { get; set; }
        public string AggregationLevel { get; set; }
        public string SerialNumber { get; set; }
        public int? UnitCapacity { get; set; }
        public string ItemLevel { get; set; }
        public string ItemType { get; set; }
        public int ItemCount { get; set; }
        public string TargetMarketShortName { get; set; }
        public string TargetMarketName { get; set; }
        public string SrcSystem { get; set; }

        public string InternalCodeType { get; set; }

        public string InternalCode { get; set; }

        public string Form { get; set; }

        public string Strength { get; set; }

        public int PackSize { get; set; }

        public string PackType { get; set; }
    }
}

