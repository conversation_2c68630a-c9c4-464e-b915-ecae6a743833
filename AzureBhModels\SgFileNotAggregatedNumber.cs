﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureBhModels
{
    [Table("SgFileNotAggregatedNumber")]
    public class SgFileNotAggregatedNumber
    {
        [Key]
        public int IDSgFileNotAggregatedNumber { get; set; }

        [Required]
        public int IDSgFile { get; set; }

        [Required]
        public string SerialNumber { get; set; }

        public string Type { get; set; }

        public string Level { get; set; }

        public string State { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [InverseProperty("SgFileNotAggregatedNumbers")]
        public SgFile SgFile { get; set; }
    }
}
