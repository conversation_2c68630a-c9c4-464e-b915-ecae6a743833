﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureAmModels
{
    [Table("BatchEventAggregationItem")]
    public class BatchEventAggregationItem
    {
        [Key]
        public long IDBatchEventAggregationItem { get; set; }

        [Required]
        public int IDBatchEventAggregation { get; set; }

        [StringLength(128)]
        public string SerialNumber { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [InverseProperty("BatchEventAggregationItems")]
        public BatchEventAggregation BatchEventAggregation { get; set; }
    }
}
