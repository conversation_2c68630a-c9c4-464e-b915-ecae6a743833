using System.Runtime.Serialization;
using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace AS2Test_master.Helpers
{
   /// <summary>
   /// Represents a schema filter for enumerations.
   /// </summary>
   public class EnumSchemaFilter : ISchemaFilter
   {
      /// <summary>
      /// Applies the schema filter to the given OpenApiSchema.
      /// </summary>
      /// <param name="model">The OpenApiSchema to apply the filter to.</param>
      /// <param name="context">The SchemaFilterContext containing information about the schema.</param>
      public void Apply(OpenApiSchema model, SchemaFilterContext context)
      {
         if (context.Type.IsEnum)
         {
            model.Enum.Clear();
            foreach (string enumName in Enum.GetNames(context.Type))
            {
               System.Reflection.MemberInfo memberInfo = context.Type.GetMember(enumName)?.FirstOrDefault(m => m.DeclaringType == context.Type) ?? null;
               EnumMemberAttribute enumMemberAttribute = memberInfo == null
                  ? null
                  : memberInfo.GetCustomAttributes(typeof(EnumMemberAttribute), false).OfType<EnumMemberAttribute>().FirstOrDefault();
               string label = enumMemberAttribute == null || string.IsNullOrWhiteSpace(enumMemberAttribute.Value)
                  ? enumName
                  : enumMemberAttribute.Value;
               model.Enum.Add(new OpenApiString(label));
            }
         }
      }
   }
}