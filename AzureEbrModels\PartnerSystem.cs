﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEbrModels
{
    [Table("PartnerSystem")]
    public class PartnerSystem
    {
        [Key]
        public int IDPartnerSystem { get; set; }

        [Required]
        public int IDCustomerPartner { get; set; }

        /// <summary>
        /// 0 - SNX request
        /// 1 - SSCC request
        /// 2 - EBR import
        /// </summary>
        [Required]
        public int RequestType { get; set; }

        [StringLength(500)]
        public string Url { get; set; }

        public int? PortNumber { get; set; }

        [StringLength(50)]
        public string Method { get; set; }

        [StringLength(500)]
        public string Folder { get; set; }

        [StringLength(50)]
        public string Username { get; set; }

        [StringLength(50)]
        public string Password { get; set; }

        [StringLength(500)]
        public string SpRequest { get; set; }

        [StringLength(500)]
        public string SpResponseOk { get; set; }

        [StringLength(500)]
        public string SpResponseFail { get; set; }

        [StringLength(500)]
        public string SpFileName { get; set; }
    }
}
