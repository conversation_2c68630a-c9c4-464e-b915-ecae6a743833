﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureAmModels
{
    [Table("BatchEvent")]
    public class BatchEvent
    {
        public BatchEvent()
        {
            BatchEventCodes = new HashSet<BatchEventCode>();
            BatchEventAggregations = new HashSet<BatchEventAggregation>();
            BatchEventStatuses = new HashSet<BatchEventStatus>();
        }

        [Key]
        public int IDBatchEvent { get; set; }

        [Required]
        public int IDBatch { get; set; }

        [Required]
        public Int16 IDBatchEventType { get; set; }

        public DistributionStateEnum DistributionState { get; set; }

        public string ReportID { get; set; }

        public string Error { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        [InverseProperty("BatchEvents")]
        public Batch Batch { get; set; }

        [InverseProperty("BatchEvents")]
        public BatchEventType BatchEventType { get; set; }

        [InverseProperty("BatchEvent")]
        public ICollection<BatchEventCode> BatchEventCodes { get; set; }

        [InverseProperty("BatchEvent")]
        public ICollection<BatchEventAggregation> BatchEventAggregations { get; set; }

        [InverseProperty("BatchEvent")]
        public ICollection<BatchEventStatus> BatchEventStatuses { get; set; }


        public override bool Equals(object obj)
        {
            if (obj == null)
                return false;

            if (obj.GetType() == this.GetType())
            {
                if (((BatchEvent)obj).IDBatchEvent == this.IDBatchEvent)
                    return true;
                else
                    return false;
            }
            else
                return false;
        }


    }

    public enum DistributionStateEnum
    {
        UNKNOWN,
        NEW,
        PUBLISHING,
        PUBLISHED,
        DRAFT,
        PENDING,
        READY_TO_SEND,
        REJECTED,
        SENT,
        CANCELED
    }
}
