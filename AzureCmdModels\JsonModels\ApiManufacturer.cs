﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace AzureCmdModels.JsonModels
{

    [DataContract]
    public class ApiManufacturer
    {
        public ApiManufacturer()
        {
            
        }
        public ApiManufacturer(string json)
        {
            var obj = CustomJSON.Deserialize(json, typeof(ApiManufacturer)) as ApiManufacturer;
            if (obj == null)
                throw new Exception("Deserialize error");

            this.IDMah = obj.IDMah;
            this.MahId = obj.MahId;
            this.MahName = obj.MahName;
            this.GLN = obj.GLN;
            this.GS1CompanyPrefix = obj.GS1CompanyPrefix;
            this.MahCity = obj.MahCity;
            this.MahStreet2 = obj.MahStreet2;
            this.SGLN = obj.SGLN;
            this.MahStreet = obj.MahStreet;
            this.MahPostCode = obj.MahPostCode;
            this.MahCountryCode = obj.MahCountryCode;
            this.IsActive = obj.IsActive;
            this.IdMahType = obj.IdMahType;
            this.IDCustomer = obj.IDCustomer;
            this.User = obj.User;
        }

        [DataMember(Name = "idMah")]
        public int IDMah { get; set; }


        [DataMember(Name = "idCustomer")]
        public int IDCustomer { get; set; }

        [MaxLength(100)]
        [DataMember(Name = "gs1CompanyPrefix")]
        public string GS1CompanyPrefix { get; set; }

        [MaxLength(20)]
        [DataMember(Name = "sgln")]
        public string SGLN { get; set; }

        [MaxLength(13)]
        [DataMember(Name = "gln")]
        public string GLN { get; set; }

        [DataMember(Name = "idMahType")]
        public int? IdMahType { get; set; }

        [MaxLength(50)]
        [DataMember(Name = "mahId")]
        public string MahId { get; set; }

        [Required]
        [MaxLength(100)]
        [DataMember(Name = "mahName")]
        public string MahName { get; set; }

        [MaxLength(255)]
        [DataMember(Name = "street")]
        public string MahStreet { get; set; }

        [MaxLength(255)]
        [DataMember(Name = "street2")]
        public string MahStreet2 { get; set; }

        [StringLength(100)]
        [DataMember(Name = "city")]
        public string MahCity { get; set; }

        [StringLength(50)]
        [DataMember(Name = "postCode")]
        public string MahPostCode { get; set; }

        [DataMember(Name = "country")]
        public string MahCountryCode { get; set; }

        [DataMember(Name = "isActive")]
        public bool IsActive { get; set; }

        [DataMember(Name = "user")]
        public string User { get; set; }
   
        public Manufacturer ToManufacturerModel(string lgnName)
        {
            Manufacturer manufacturer = new Manufacturer()
            {
                GLN = this.GLN,
                GS1CompanyPrefix = this.GS1CompanyPrefix,
                IDCustomer = this.IDCustomer,
                IDMah = this.IDMah,
                IDMahType = this.IdMahType,
                IsActive = this.IsActive,
                MahID = this.MahId,
                MahCity = this.MahCity,
                MahCountryCode = this.MahCountryCode,
                MahName = this.MahName,
                MahPostCode = this.MahPostCode,
                MahStreet = this.MahStreet,
                MahStreet2 = this.MahStreet2,
                SGLN = this.SGLN
            };

            if (manufacturer.IDMah == 0)
            {
                manufacturer.UserCreated = lgnName;
                manufacturer.TimestampCreated = DateTime.Now;
            }
            else
            {
                manufacturer.UserUpdated = lgnName;
                manufacturer.TimestampUpdated = DateTime.Now;
            }

            return manufacturer;
        }
        public string GetJSONText()
        {
            MemoryStream stream = new MemoryStream();
            CustomJSON.SerializeToStream(this, stream);
            stream.Position = 0;
            using (StreamReader reader = new StreamReader(stream))
            {
                return reader.ReadToEnd();
            }
        }
    }
}
