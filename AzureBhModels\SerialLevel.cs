﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureBhModels
{
    [Table("SerialLevel")]
    public class SerialLevel
    {
        [Key]
        public int IDSerialLevel { get; set; }

        [Required]
        public int IDBatch { get; set; }

        [Required]
        [StringLength(50)]
        public string Type { get; set; }

        [Required]
        [StringLength(50)]
        public string Name { get; set; }

        [Required]
        public int Count { get; set; }

        public int? UnitCapacity { get; set; }

        public string UnitName { get; set; }

        [Required]
        public bool IsAggregationLevel { get; set; }

        public string SubLevel { get; set; }

        public string SerialType { get; set; }

        public string SubLevelItemType { get; set; }

        public bool IsManaullyCreated { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [InverseProperty("SerialLevels")]
        public Batch Batch { get; set; }
    }
}
