using System.Diagnostics;
using AS2Test.Models;
using AS2Test_master.Features.Services.Certificates;
using MimeKit;
using MimeKit.Cryptography;

namespace AS2Test.Features.Services.MdnHandler
{

    public class MdnVerifyAndHandle
    {
        private readonly ICertificateService _certificateService;

        public MdnVerifyAndHandle(ICertificateService certificateService)
        {
            _certificateService = certificateService ?? throw new ArgumentNullException(nameof(certificateService));
        }

        public async Task<MdnVerificationResult> VerifyMessage(MimeMessage mimeMessageWithHttpHeaders)
        {
            var result = new MdnVerificationResult();

            try
            {
                var mdnType = DetermineMdnType(mimeMessageWithHttpHeaders);
                result.MdnType = mdnType;

                switch (mdnType)
                {
                    case MdnType.Plain:
                        await HandlePlainMessage(mimeMessageWithHttpHeaders, result);
                        break;

                    case MdnType.MultipartSigned:
                        await HandleSignedMessage(mimeMessageWithHttpHeaders, result);
                        break;

                    case MdnType.ApplicationPkcs7MimeEnveloped:
                        await HandleEncryptedMessage(mimeMessageWithHttpHeaders, result);
                        break;

                    case MdnType.ApplicationPkcs7MimeSigned:
                        await HandleEncryptedAndSignedMessage(mimeMessageWithHttpHeaders, result);
                        break;
                }
            }
            catch (Exception ex)
            {
                result.Error = $"Message processing failed: {ex.Message}";
                result.IsSignatureValid = false;
            }

            return result;
        }

        private MdnType DetermineMdnType(MimeMessage message)
        {
            var pkcs7 = message.Body as ApplicationPkcs7Mime;

            bool isEncrypted = message.Body is MultipartEncrypted ||
                              (pkcs7?.SecureMimeType == SecureMimeType.EnvelopedData);

            bool isSigned = message.Body is MultipartSigned ||
                           (pkcs7?.SecureMimeType == SecureMimeType.SignedData);

            if (isEncrypted && isSigned)
                return MdnType.MultipartSigned;
            else if (isEncrypted)
                return MdnType.ApplicationPkcs7MimeEnveloped;
            else if (isSigned)
                return MdnType.ApplicationPkcs7MimeSigned;
            else
                return MdnType.Plain;
        }

        private async Task HandlePlainMessage(MimeMessage message, MdnVerificationResult result)
        {
            if (message.Body is TextPart textPart)
            {
                result.MdnText = textPart.Text;
                ParseMdnContent(result);
            }
        }

        private async Task HandleEncryptedAndSignedMessage(MimeMessage message, MdnVerificationResult result)
        {
            // First decrypt, then verify signature
            var decryptedMessage = await DecryptMessage(message);
            if (decryptedMessage != null)
            {
                result.IsDecrypted = true;
                await HandleSignedMessage(decryptedMessage, result);
            }
            else
            {
                result.Error = "Failed to decrypt message";
                result.IsDecrypted = false;
            }
        }

        private async Task<MimeMessage> DecryptMessage(MimeMessage message)
        {
            var encrypted = message.Body as MultipartEncrypted;
            var pkcs7 = message.Body as ApplicationPkcs7Mime;

            using (var ctx = new TemporarySecureMimeContext())
            {
                await ctx.ImportAsync(await _certificateService.GetSenderCertificateAsync(""));

                MimeEntity decrypted;
                if (encrypted != null)
                {
                    // Handle MultipartEncrypted
                    return null; // Implement MultipartEncrypted handling if needed
                }
                else if (pkcs7?.SecureMimeType == SecureMimeType.EnvelopedData)
                {
                    decrypted = pkcs7.Decrypt(ctx);
                    var decryptedMessage = new MimeMessage();
                    decryptedMessage.Body = decrypted;
                    return decryptedMessage;
                }
            }

            return null;
        }

        private async Task HandleSignedMessage(MimeMessage message, MdnVerificationResult result)
        {
            var signed = message.Body as MultipartSigned;
            if (signed == null)
            {
                var pkcs7 = message.Body as ApplicationPkcs7Mime;
                if (pkcs7?.SecureMimeType == SecureMimeType.SignedData)
                {
                    using (var ctx = new TemporarySecureMimeContext())
                    {
                        await ctx.ImportAsync(await _certificateService.GetPartnerCertificate(""));
                        CryptographyContext.Register(typeof(TemporarySecureMimeContext));

                        MimeEntity signedContent;
                        pkcs7.Verify(ctx, out signedContent);
                        result.IsSignatureValid = true;

                        if (signedContent is TextPart textPart)
                        {
                            result.MdnText = textPart.Text;
                            ParseMdnContent(result);
                        }
                    }
                }
            }
            else
            {
                using (var ctx = new TemporarySecureMimeContext())
                {
                    await ctx.ImportAsync(await _certificateService.GetPartnerCertificate(""));
                    CryptographyContext.Register(typeof(TemporarySecureMimeContext));

                    foreach (var signature in signed.Verify(ctx))
                    {
                        try
                        {
                            var isValidSignature = signature.Verify(verifySignatureOnly: true);
                            if (isValidSignature)
                            {
                                result.IsSignatureValid = true;

                                if (signed.Count > 0 && signed[0] is TextPart textPart)
                                {
                                    result.MdnText = textPart.Text;
                                    ParseMdnContent(result);
                                }
                                break;
                            }
                        }
                        catch (DigitalSignatureVerifyException ex)
                        {
                            result.Error = $"Signature verification failed: {ex.Message}";
                            Debug.WriteLine("Verify signature failed with: " + ex);
                        }
                    }
                }
            }
        }

        private async Task HandleEncryptedMessage(MimeMessage message, MdnVerificationResult result)
        {
            var decryptedMessage = await DecryptMessage(message);
            if (decryptedMessage != null)
            {
                result.IsDecrypted = true;
                if (decryptedMessage.Body is TextPart textPart)
                {
                    result.MdnText = textPart.Text;
                    ParseMdnContent(result);
                }
            }
            else
            {
                result.Error = "Failed to decrypt message";
                result.IsDecrypted = false;
            }
        }

        private void ParseMdnContent(MdnVerificationResult result)
        {
            // Implement MDN content parsing logic here
            // This would parse the MDN text and populate additional fields in the result
        }
    }
}