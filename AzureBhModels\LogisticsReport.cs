﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureBhModels
{
    [Table("LogisticsReport")]
    public class LogisticsReport
    {
        public LogisticsReport()
        {
            LogisticsReportItems = new HashSet<LogisticsReportItem>();
            LogisticsReportLogs = new HashSet<LogisticsReportLog>();
        }

        [Key]
        public int IDLogisticsReport { get; set; }

        [NotMapped]
        public int IDBatch { get; set; }

        public LogisticsReportsEnum Type { get; set; }

        public ReportStateEnum State { get; set; }

        public int? IDProduct { get; set; }

        public int? IDSender { get; set; }

        public int? IDReceiver { get; set; }

        [DisplayName("Batch ID")]
        [RegularExpression(@"^([\u0021;\u0022;\u0025;\u0026;\u0027;\u0028;\u0029;\u002A;\u002B;\u002C;\u002D;\u002E;\u002F;\u003A;\u003B;\u003C;\u003D;\u003E;\u003F;\u005F;a-zA-Z0-9]{0,20})$",
         ErrorMessage = "Batch ID is expected to be alphanumeric values of up to 20 characters, limited to those characters defined in the GS1 General  Specifications.")]
        [StringLength(20)]
        public string BatchID { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? ExpiryDate { get; set; }
        public string ReasonCode { get; set; }

        /// <summary>
        /// Alphanumberic. Starts with yymmdd + GUID
        /// </summary>
        [Required]
        [StringLength(40)]
        public string InstanceIdentifier { get; set; }

        [Required]
        public bool IsAutoPublish { get; set; } //todo

        public DateTime? PublishTimestamp { get; set; }

        [StringLength(1)]
        public string ResponseStatusType { get; set; } //todo - enum?

        public int? ResponseCode { get; set; }

        [StringLength(50)]
        public string ResponseDate { get; set; }

        [StringLength(50)]
        public string MessageID { get; set; }

        //todo: nvarchar max
        public string ResponseStatusReason { get; set; }

        [StringLength(50)]
        public string ResponseStatusCode { get; set; }

        //todo: nvarchar(max)
        public string QueryResponseStatus { get; set; }

        public DateTime? QueryStatusTimestamp { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [InverseProperty("LogisticsReports")]
        public Sender Sender { get; set; }

        [InverseProperty("LogisticsReports")]
        public Receiver Receiver { get; set; }

        [InverseProperty("LogisticsReport")]
        public ICollection<LogisticsReportItem> LogisticsReportItems { get; set; }

        [InverseProperty("LogisticsReport")]
        public ICollection<LogisticsReportLog> LogisticsReportLogs { get; set; }

    }
}
