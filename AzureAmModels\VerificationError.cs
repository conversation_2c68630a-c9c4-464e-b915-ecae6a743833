﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureAmModels
{
    [Table("VerificationError")]
    public class VerificationError
    {
        [Key]
        public int IDVerificationError { get; set; }

        [Required]
        public int IDBatch { get; set; }

        [StringLength(50)]
        public string Level { get; set; }

        [Required]
        [StringLength(200)]
        public string Error { get; set; }

        [StringLength(20)]
        public string SerialNumber { get; set; }

        [StringLength(50)]
        public string SerialType { get; set; }

        [StringLength(20)]
        public string ItemSerialNumber { get; set; }

        [StringLength(50)]
        public string ItemSerialType { get; set; }

        public bool IsSkipAllowed { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [InverseProperty("VerificationErrors")]
        public Batch Batch { get; set; }
    }
}
