﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureCmdModels
{
    public static class SGADGroups
    {
        public const string Admin = @"SATT\Group SGCloud Admin,87be55f7-cef8-4d74-8942-3ac0dff8890b,SG\Department RND";
        public const string SuperUser = @"SATT\Group SGCloud SuperUser,dfd322e9-9730-4e38-a210-8447740c7c40,SG\Department RND";
        public const string User = @"SATT\Group SGCloud User,70d39629-2c73-4cc0-80e5-52525bb70195,SG\Department RND";
        public const string UserPmd = @"SATT\Group SGCloud User PMD,0756a7a3-cff4-4d8c-a9dc-a516e28f2e93,SG\Department RND";
        public const string UserPpd = @"SATT\Group SGCloud User PPD,8f73dcc6-7a3a-432e-960d-0289670dafcc,SG\Department RND";
        public const string WebApi = @"SATT\Group SGCloud WebAPI,e3f7c22b-61db-4e81-87ef-f39a0e762c00,SG\Department RND";
        public const string SNXManagement = @"SATT\Group SGCloud SNX Managment, 1959943f-cf48-4427-8d69-2a84973a33c8,SG\Department RND";
        public const string SNXUser = @"SATT\Group SGCloud SNX Pool Reader,fb8c1147-efb8-4655-b205-b9cd72542e4b,SG\Department RND";
        public const string PowerUser = @"SATT\Group SGCloud SNX Managment, 1959943f-cf48-4427-8d69-2a84973a33c8,SG\Department RND";

        //Specifically for EU hub
        public const string BatchRecall = @"SATT\Group SGCloud Batch Recall,d34ecc53-4423-46dd-b634-4328bc28a1c1,SG\Department RND";
        public const string ProductWithdraw = @"SATT\Group SGCloud Product Withdraw,8a87aa40-d425-4f2b-9c91-67be0e6d2bac,SG\Department RND";
    }
}
