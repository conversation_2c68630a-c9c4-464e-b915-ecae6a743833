﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SGAuditTrail;

namespace AzureCmdModels
{
    [Table("BusinessSegment")]
    [Audit(DisplayName = "Business Segment")]
    public class BusinessSegment
    {
        public BusinessSegment()
        {
            Customers = new HashSet<Customer>();
        }

        [Key]
        [Column("IDBusinessSegment")]
        public int IDBusinessSegment { get; set; }

        [StringLength(50)]
        [Audit(DisplayName = "Business Segment")]
        public string Segment { get; set; }

        [InverseProperty("BusinessSegment")]
        public ICollection<Customer> Customers { get; set; }
    }
}
