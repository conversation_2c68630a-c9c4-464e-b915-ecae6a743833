﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureCmdModels
{
    [Table("ServiceRole")]
    public partial class ServiceRole
    {
        public ServiceRole()
        {
            UserLoginServiceRoles = new HashSet<UserLoginServiceRole>();
        }

        [Key]
        public int IDServiceRole { get; set; }

        [Required]
        [StringLength(128)]
        public string RoleName { get; set; }

        [StringLength(128)]
        public string GroupName { get; set; }

        [InverseProperty("ServiceRole")]
        public ICollection<UserLoginServiceRole> UserLoginServiceRoles { get; set; }

    }
}
