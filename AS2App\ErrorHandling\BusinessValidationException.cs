using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AS2Test.ErrorHandling
{
    /// <summary>
    /// Represents an exception that occurs during business validation.
    /// </summary>
    public class BusinessValidationException : Exception
    {
        /// <summary>
        /// Gets the HTTP status code for the business validation exception.
        /// <summary>
        /// Initializes a new instance of the <see cref="BusinessValidationException"/> class with a specified error message.
        /// </summary>
        /// <param name="message">The message that describes the error.</param>
        
        public int StatusCode => StatusCodes.Status422UnprocessableEntity;
        public string Type => "https://example.com/probs/business-error";
        public BusinessValidationException(string message) : base(message) { }
        
    }
}