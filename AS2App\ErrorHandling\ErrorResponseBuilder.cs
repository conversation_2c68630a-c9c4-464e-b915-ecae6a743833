using System.ComponentModel.DataAnnotations;
using AS2Test.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.OpenApi.Exceptions;

namespace AS2Test.ErrorHandling
{
    /// <summary>
    /// Provides methods to build error responses for exceptions.
    /// </summary>
        public static class ErrorResponseBuilder
    {
        /// <summary>
        /// Creates an error response based on the provided exception and request path.
        /// </summary>
        /// <param name="exception">The exception that occurred.</param>
        /// <param name="path">The path of the request that caused the exception.</param>
        /// <returns>A service response containing problem details.</returns>
        public static ServiceResponse<ProblemDetails> CreateErrorResponse(Exception exception, string path)
        {
            ProblemDetails problemDetails;

            switch (exception)
            {
                case ValidationException validationException:
                    var valdiationProblem = new ValidationProblemDetails
                    {
                        Type = "Validation error",
                        Title = "Validation Failed",
                        Status = StatusCodes.Status400BadRequest,
                        Detail = validationException.Message,
                    };
                    problemDetails = valdiationProblem;
                break;

                case ApiException apiException:
                    problemDetails = new ProblemDetails
                {
                    Type = "Api exception",
                    Title = apiException.Message,
                    Status  = apiException.StatusCode,
                    //Instance = instancePath
                };
                break;

                default:
                    problemDetails = new ProblemDetails
                    {
                        Type = "Internal-server-error",
                        Title = "An unexpected error occurred.",
                        Status = StatusCodes.Status500InternalServerError,
                        Detail = exception.Message,
                       // Instance = instancePath
                    };
                    break;
            };
            return new ServiceResponse<ProblemDetails>
            {
                IsSuccess = false,
                Error = problemDetails,
                ErrorMessage = problemDetails.Detail,
                
            };
        }
    }
}