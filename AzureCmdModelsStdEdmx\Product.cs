//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace AzureCmdModelsStdEdmx
{
    using System;
    using System.Collections.Generic;
    
    public partial class Product
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public Product()
        {
            this.CustomerAggregationLevels = new HashSet<CustomerAggregationLevel>();
            this.ProductLabels = new HashSet<ProductLabel>();
        }
    
        public int IDProduct { get; set; }
        public int IDServiceMasterRecord { get; set; }
        public int IDCustomer { get; set; }
        public string CodeType { get; set; }
        public string Code { get; set; }
        public string InternalCodeType { get; set; }
        public string InternalCode { get; set; }
        public string Name { get; set; }
        public string CommonName { get; set; }
        public string Form { get; set; }
        public string PackType { get; set; }
        public Nullable<int> PackSize { get; set; }
        public string Strength { get; set; }
        public bool IsActive { get; set; }
        public System.DateTime TimestampCreated { get; set; }
        public string UserCreated { get; set; }
        public Nullable<System.DateTime> TimestampUpdated { get; set; }
        public string UserUpdated { get; set; }
        public string SerialNumberSourceType { get; set; }
        public string SerializationType { get; set; }
        public Nullable<int> IDCustomerPartner { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<CustomerAggregationLevel> CustomerAggregationLevels { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<ProductLabel> ProductLabels { get; set; }
    }
}
