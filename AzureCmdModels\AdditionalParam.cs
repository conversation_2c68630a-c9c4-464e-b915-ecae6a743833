﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureCmdModels
{
    [Table("AdditionalParam")]
    public partial class AdditionalParam
    {
        public AdditionalParam()
        {
            CustomerAdditionalParams = new HashSet<CustomerAdditionalParam>();
           // ConvertedFileAdditionalParams = new HashSet<ConvertedFileAdditionalParam>();
        }

        [Key]
        public int IDAdditionalParam { get; set; }

        public int IDSupportedFormat { get; set; }

        [Required]
        [StringLength(128)]
        public string Name { get; set; }

        [StringLength(128)]
        public string? Caption { get; set; }

        public string? Description { get; set; }

        [StringLength(128)]
        public string? ParamType { get; set; }

        [StringLength(int.MaxValue)]
        public string? Regex { get; set; }

        [StringLength(int.MaxValue)]
        public string? Sql { get; set; }

        [StringLength(int.MaxValue)]
        public string? DefaultValue { get; set; }

        public bool IsOptional { get; set; }

        public bool IsReadOnly { get; set; }


        public int? MaxLength { get; set; }

        public bool IsHidden { get; set; }

        [StringLength(1024)]
        public string? ErrorMsg { get; set; }

        [StringLength(256)]
        public string? ValidationFQDN { get; set; }

        [StringLength(256)]
        public string? ValidationMethod { get; set; }


        [InverseProperty("AdditionalParams")]
        public SupportedFormat SupportedFormat { get; set; }

        [InverseProperty("AdditionalParam")]
        public ICollection<CustomerAdditionalParam> CustomerAdditionalParams { get; set; }

      //  [InverseProperty("AdditionalParam")]
//        public ICollection<ConvertedFileAdditionalParam> ConvertedFileAdditionalParams { get; set; }

    }

}
