﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEbrModels
{
    [Table("TemplateLevel")]
    public class TemplateLevel
    {
        public TemplateLevel()
        {
            TemplateExtendProperties = new HashSet<TemplateExtendProperty>();
            TemplateLevelFiles = new HashSet<TemplateLevelFile>();
        }

        [Key]
        public int IDTemplateLevel { get; set; }

        [Required]
        public int? IDTemplate { get; set; }

        /// <summary>
        /// Serialization, Case, Pallet (трябва да дойдат или от CMD или EBR -> само общите за клиент, без тези за продукта)
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Name { get; set; }

        public int? IDAggregationLevel { get; set; }

        /// <summary>
        /// TRADE, LOGISTIC 
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Type { get; set; }

        /// <summary>
        ///SERIAL/SSCC
        /// </summary>
        [Required]
        [StringLength(10)]
        public string SerialType { get; set; }

        /// <summary>
        /// Serialization, Case, Pallet (трябва да дойдат или от CMD или EBR -> само общите за клиент, без тези за продукта)
        /// </summary>
        [StringLength(20)]
        public string SubLevel { get; set; }

        /// <summary>
        ///SERIAL/SSCC
        /// </summary>
        [StringLength(10)]
        public string SubLevelType { get; set; }

        public int? UnitCapacity { get; set; }

        public bool IsSerialization { get; set; }

        public bool IsActive { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        [InverseProperty("TemplateLevel")]
        public ICollection<TemplateExtendProperty> TemplateExtendProperties { get; set; }

        [InverseProperty("TemplateLevel")]
        public ICollection<TemplateLevelFile> TemplateLevelFiles { get; set; }

        [InverseProperty("TemplateLevels")]
        public Template Template { get; set; }
    }
}
