﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureBhModels
{
    [Table("Sender")]
    public class Sender
    {
        [Key]
        public int IDSender { get; set; }

        [StringLength(100)]
        public string Name { get; set; }

        [StringLength(100)]
        public string CountryAddress { get; set; }

        [Required]
        public string GLN { get; set; }

        public string SGLN { get; set; }

        [Required]
        [StringLength(300)]
        public string Location { get; set; }

        [StringLength(300)]
        public string Transaction { get; set; }

        [Required]
        public bool IsDefault { get; set; }


        [Required]
        public bool IsActive { get; set; }

        public int? IDAS2Certificate { get; set; }



        [Required]
        [DisplayFormat(DataFormatString = "{0:yyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        public string UserCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        public string UserUpdated { get; set; }

        [InverseProperty("Sender")]
        public ICollection<BatchReport> BatchReports { get; set; }

        [InverseProperty("Sender")]
        public ICollection<LogisticsReport> LogisticsReports { get; set; }
    }
}
