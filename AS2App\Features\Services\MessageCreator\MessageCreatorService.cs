﻿using System.Data.SqlTypes;
using System.Diagnostics;
using System.Net.Http.Headers;
using System.Security.Cryptography;
using System.Security.Cryptography.Pkcs;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using AS2App.Features.Services.MicHandler;
using AS2Test_master.Features.Services.Certificates;
using AS2Test_master.Models;
using MimeKit;
using MimeKit.Cryptography;
using static AS2Test.Extensions.Helpers.ExtensionMethods;

namespace AS2Test.Features.Services.MessageCreator
{
    public class MessageCreatorService : IMessageCreatorService
    {
        private readonly ICertificateService _certificateService;
        private readonly ILogger<MessageCreatorService> _logger;
        private readonly IMicHandlerService _micHandlerService;


        public MessageCreatorService(ICertificateService certificaterService, ILogger<MessageCreatorService> logger, IMicHandlerService micHandlerService)
        {
            _certificateService = certificaterService ?? throw new ArgumentNullException(nameof(_certificateService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _micHandlerService = micHandlerService ?? throw new ArgumentNullException(nameof(micHandlerService));
        }

        public async Task<ByteArrayContent> CreateAs2MessageAsync(MessageRequest request)
        {
            try
            {
                string password = Encoding.UTF8.GetString(Convert.FromBase64String(request.PrivateCertificatePassword));

                var senderCert = await _certificateService.ParseSenderCertificate(request.SenderCert, password);
                var receiverCert = await _certificateService.GetPartnerCertificate(request.ReceiverCert);

                MimePart mimePart = CreateMimePart(request);

                using var tmpStream = new MemoryStream();
                await mimePart.WriteToAsync(tmpStream);
                tmpStream.Position = 0;

                _micHandlerService.GenerateSaveMic(tmpStream.ToArray(), request.MessageId, request.MdnOption.MessageIntegrityCheckAlgorithm.ToString().ToUpper());

                MimeEntity entity;

                if (request.IsSigned)
                {
                    _logger.LogInformation("=== STEP 2: Before Signing ===");

                    entity = await SignAsync(mimePart, senderCert, request.IsMultipart, request);
                    _logger.LogInformation("=== STEP 3: After Signing ===");
                }
                else
                {
                    entity = mimePart;
                }

                byte[] messageBytes;

                if (request.IsEncrypted)
                {
                    _logger.LogInformation("=== STEP 4: Before encrypting ===");

                    messageBytes = await EncryptMimeEntityAsync(entity, receiverCert, request);

                    _logger.LogInformation("=== STEP 5: After encrypting ===");

                }
                else
                {
                    messageBytes = await MimeToByteArrayAsync(entity);
                }

                return CreateHttpContent(messageBytes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating AS2 message");
                throw new  ("Error creating AS2 message", ex);

            }
        }

        // RFC 4130 Section 7.1: Create proper MIME part with EDI/XML content
        public MimePart CreateMimePart(MessageRequest messageRequest)
        {
            var xmlBytes = Convert.FromBase64String(messageRequest.Message);
            var xmlStream = new MemoryStream(xmlBytes);
            xmlStream.Position = 0;

            var mimePart = new MimePart("application", "xml")
            {
                Content = new MimeContent(xmlStream),
                ContentDisposition = new ContentDisposition(ContentDisposition.Attachment),
                ContentTransferEncoding = ContentEncoding.Base64,
                FileName = $"{messageRequest.FileName}.xml"
            };

            _logger.LogInformation("Created MimePart with ContentTransferEncoding: {encoding}", mimePart.ContentTransferEncoding);

            return mimePart;
        }

        public async Task<byte[]> MimeToByteArrayAsync(MimeEntity part)
        {
            using var stream = new MemoryStream();
            await part.WriteToAsync(stream);
            return stream.ToArray();
        }
        private async Task<MimeEntity> SignAsync(MimePart xmlPart, X509Certificate2 senderCert, bool useMultipart, MessageRequest request)
        {
            using var tmpStream = new MemoryStream();
            await xmlPart.WriteToAsync(tmpStream);
            tmpStream.Position = 0;

            var signedCms = new SignedCms(new ContentInfo(tmpStream.ToArray()), true);
            var signer = new System.Security.Cryptography.Pkcs.CmsSigner(System.Security.Cryptography.Pkcs.SubjectIdentifierType.IssuerAndSerialNumber, senderCert)
            {
                DigestAlgorithm = new Oid("2.16.840.*********.2.1") // SHA-256
            };
            signedCms.ComputeSignature(signer);
            var signatureBytes = signedCms.Encode();
            if (useMultipart)
            {
                var signaturePart = new MimePart("application", "pkcs7-signature")
                {
                    Content = new MimeContent(new MemoryStream(signatureBytes)),
                    ContentDisposition = new ContentDisposition(ContentDisposition.Attachment),
                    ContentTransferEncoding = TransferEncodingConvert(request.TransferEncoding.ToString()).Item2, 
                    FileName = "smime.p7s"
                };
                signaturePart.ContentType.Parameters["name"] = "smime.p7s";
                signaturePart.ContentType.Parameters["smime-type"] = "signed-data";

                var multipartSigned = new Multipart("signed")
                {
                    ContentType = {
                                Parameters = {
                                    ["protocol"] = "application/pkcs7-signature",
                                    ["micalg"] = "sha-256"
                                }
                            }
                };

                multipartSigned.Add(xmlPart);
                multipartSigned.Add(signaturePart);

                return multipartSigned;

            }
            else
            {
                // If not using multipart, just return the signed part directly
                var signedPart = new MimePart("application", "pkcs7-mime")
                {
                    Content = new MimeContent(new MemoryStream(signatureBytes)),
                    ContentDisposition = new ContentDisposition(ContentDisposition.Attachment),
                    ContentTransferEncoding = ContentEncoding.Base64,
                    FileName = "smime.p7m"
                };
                signedPart.ContentType.Parameters["name"] = "smime.p7m";
                signedPart.ContentType.Parameters["smime-type"] = "signed-data";
                return signedPart;
            }

        }

        private async Task<byte[]> EncryptMimeEntityAsync(MimeEntity entity, X509Certificate2 receiverCert, MessageRequest messageRequest)
        {
            using var signedStream = new MemoryStream();
            await entity.WriteToAsync(signedStream);
            signedStream.Position = 0;


            if (!EncryptionOids.OidMap.TryGetValue(messageRequest.EncryptionAlgorithm.ToString(), out string oidValue))
                throw new ArgumentException($"Unsupported encryption algorithm: {messageRequest.EncryptionAlgorithm.ToString()}");

            var algorithmOid = new Oid(oidValue);

            var envelopedCms = new EnvelopedCms(new ContentInfo(signedStream.ToArray()), new AlgorithmIdentifier(algorithmOid));

            envelopedCms.Encrypt(new System.Security.Cryptography.Pkcs.CmsRecipient(System.Security.Cryptography.Pkcs.SubjectIdentifierType.IssuerAndSerialNumber, receiverCert));
            var encryptedBytes = envelopedCms.Encode();


            return encryptedBytes;
        }
        private ByteArrayContent CreateHttpContent(byte[] messageBytes)
        {

            var content = new ByteArrayContent(messageBytes);
            content.Headers.Clear();
            content.Headers.ContentType = new MediaTypeHeaderValue("application/pkcs7-mime");
            content.Headers.ContentType.Parameters.Add(new NameValueHeaderValue("smime-type", "enveloped-data"));
            content.Headers.ContentType.Parameters.Add(new NameValueHeaderValue("name", "smime.p7m"));
            content.Headers.ContentDisposition = new ContentDispositionHeaderValue("attachment") { FileName = "smime.p7m" };

            return content;
        }

  

        // Helper method to log raw content and detect Base64 encoding
        private async Task LogRawContent(MimeEntity entity, string description)
        {
            try
            {
                using var memStream = new MemoryStream();
                await entity.WriteToAsync(memStream);
                var rawBytes = memStream.ToArray();
                var content = Encoding.UTF8.GetString(rawBytes);

                _logger.LogInformation("=== {description} Raw Analysis ===", description);
                _logger.LogInformation("Total bytes: {bytes}", rawBytes.Length);
                _logger.LogInformation("First 200 characters:\n{content}", content.Length > 200 ? content[..200] + "..." : content);

                // Check if content looks like Base64
                var base64Pattern = @"^[A-Za-z0-9+/]*={0,2}$";
                var lines = content.Split('\n');
                var dataLines = lines.Where(l => !l.StartsWith("Content-") && !l.StartsWith("MIME-") &&
                                                !l.StartsWith("--") && !string.IsNullOrWhiteSpace(l)).ToArray();

                if (dataLines.Any())
                {
                    var sampleDataLine = dataLines.FirstOrDefault(l => l.Length > 10);
                    if (!string.IsNullOrEmpty(sampleDataLine))
                    {
                        var isBase64Like = System.Text.RegularExpressions.Regex.IsMatch(sampleDataLine.Replace(" ", "").Replace("\r", ""), base64Pattern);
                        _logger.LogInformation("Content appears to be Base64 encoded: {isBase64}", isBase64Like);
                        _logger.LogInformation("Sample data line: {sample}", sampleDataLine.Length > 100 ? sampleDataLine[..100] + "..." : sampleDataLine);
                    }
                }

                // Try to detect when MimeKit actually applies Base64 encoding
                if (content.Contains("Content-Transfer-Encoding: base64"))
                {
                    _logger.LogInformation("*** MimeKit has applied Base64 Content-Transfer-Encoding header ***");
                }
                else if (content.Contains("Content-Transfer-Encoding: binary"))
                {
                    _logger.LogInformation("*** Content-Transfer-Encoding is still binary ***");
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Could not analyze raw content for {description}", description);
            }
        }
    }
}