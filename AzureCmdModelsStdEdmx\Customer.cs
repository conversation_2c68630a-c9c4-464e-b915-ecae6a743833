//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace AzureCmdModelsStdEdmx
{
    using System;
    using System.Collections.Generic;
    
    public partial class Customer
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public Customer()
        {
            this.CustomerAggregationLevels = new HashSet<CustomerAggregationLevel>();
            this.ProductLabels = new HashSet<ProductLabel>();
        }
    
        public int IDCustomer { get; set; }
        public string Name { get; set; }
        public System.DateTime TimestampCreated { get; set; }
        public string UserCreated { get; set; }
        public Nullable<System.DateTime> TimestampUpdated { get; set; }
        public string UserUpdated { get; set; }
        public string GS1CompanyPrefix { get; set; }
        public string SGLN { get; set; }
        public string GLN { get; set; }
        public string CustomerStreet { get; set; }
        public string CustomerStreet2 { get; set; }
        public string CustomerCity { get; set; }
        public string CustomerPostCode { get; set; }
        public string CustomerCountryCode { get; set; }
        public string Code { get; set; }
        public int IDBusinessSegment { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<CustomerAggregationLevel> CustomerAggregationLevels { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<ProductLabel> ProductLabels { get; set; }
    }
}
