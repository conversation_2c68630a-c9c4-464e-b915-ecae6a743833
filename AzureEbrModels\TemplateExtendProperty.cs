﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEbrModels
{
    [Table("TemplateExtendProperty")]
    public class TemplateExtendProperty
    {
        [Key]
        public int IDTemplateExtendProperty { get; set; }

        /// <summary>
        /// IDTemplate or IDTemplateLevel, only one can be selected, if IDTemplate.hasValue -> than TemplateExtendProperty is for Batch
        /// </summary>
        public int? IDTemplate { get; set; }

        public int? IDTemplateLevel { get; set; }

        [Required]
        public int IDExtendProperty { get; set; }

        public bool IsTemplateValue { get; set; }

        [StringLength(100)]
        public string StringValue { get; set; }

        [StringLength(100)]
        public string DisplayText { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        [InverseProperty("TemplateExtendProperties")]
        public Template Template { get; set; }

        [InverseProperty("TemplateExtendProperties")]
        public TemplateLevel TemplateLevel { get; set; }


        [InverseProperty("TemplateExtendProperties")]
        public ExtendProperty ExtendProperty { get; set; }

	
		[NotMapped]
		public string ConversionError { get; set; }

		[NotMapped]
		public bool? BoolValue
		{
			get
			{
				if (this.ExtendProperty.Type == ExtendPropertyEnum.BOOLEAN && !string.IsNullOrEmpty(this.StringValue)
						&& bool.TryParse(this.StringValue, out bool bvalue))
					return bvalue;
				else
					return this.ExtendProperty.IsRequired ? false : null;
			}
			set
			{
				if (value == null)
					this.StringValue = string.Empty;
				else
					this.StringValue = value.ToString();
			}
		}

		[NotMapped]
		public DateTime? DateTimeValue
		{
			get
			{

				if (this.ExtendProperty.Type == ExtendPropertyEnum.DATETIME && !string.IsNullOrEmpty(this.StringValue))
				{
					DateTime dtValue;
					if (DateTime.TryParseExact(this.StringValue, "yyyy-MM-ddTHH:mm:ss.fff", null,
						System.Globalization.DateTimeStyles.None, out dtValue))
						return dtValue;
					else
						return null;
				}
				else if (this.ExtendProperty.Type == ExtendPropertyEnum.DATE && !string.IsNullOrEmpty(this.StringValue))
				{
					DateTime dtValue;
					if (DateTime.TryParseExact(this.StringValue, "yyyy-MM-dd", null,
						System.Globalization.DateTimeStyles.None, out dtValue))
						return dtValue.Date;
					else
						return null;
				}
				else
					return null;
			}
			set
			{
				if (value == null)
					this.StringValue = string.Empty;
				else if (this.ExtendProperty.Type == ExtendPropertyEnum.DATETIME)
					this.StringValue = ((DateTime)value).ToString("yyyy-MM-ddTHH:mm:ss.fff");
				else if (this.ExtendProperty.Type == ExtendPropertyEnum.DATE)
					this.StringValue = ((DateTime)value).ToString("yyyy-MM-dd");
				else
					this.StringValue = string.Empty;

			}
		}

		[NotMapped]
		public decimal? DecimalValue
		{
			get
			{
				if (this.ExtendProperty.Type == ExtendPropertyEnum.DECIMAL && !string.IsNullOrEmpty(this.StringValue))
				{
					ConversionError = string.Empty;
					if (decimal.TryParse(this.StringValue.Replace(",", "."),
											NumberStyles.Number,
											CultureInfo.InvariantCulture,
											out decimal res))
						return res;
					else
					{
						ConversionError = $"Error converting [{StringValue}] to DECIMAL";
						return null;
					}

				}
				else
					return null;
			}
			set
			{
				if (value == null)
					this.StringValue = string.Empty;
				else
					this.StringValue = value.Value.ToString(CultureInfo.InvariantCulture);
			}
		}

		[NotMapped]
		public int? IntValue
		{
			get
			{
				if (this.ExtendProperty.Type == ExtendPropertyEnum.INT && !string.IsNullOrEmpty(this.StringValue)
					&& int.TryParse(this.StringValue, out int ivalue))
					return ivalue;
				else
					return null;
			}
			set
			{
				if (value == null)
					this.StringValue = string.Empty;
				else
					this.StringValue = value.ToString();
			}
		}

		[NotMapped]
		public string Value
		{
			get
			{
				if (ExtendProperty.Type == ExtendPropertyEnum.DATETIME && DateTimeValue.HasValue)
					return DateTimeValue?.ToString("yyyy-MM-dd HH:mm:ss");
				else if (ExtendProperty.Type == ExtendPropertyEnum.DATE && DateTimeValue.HasValue)
					return DateTimeValue?.ToString("yyyy-MM-dd");
				else
				{
					if (ExtendProperty.IsSelectValue && !string.IsNullOrEmpty(StringValue))
						return $"({StringValue}) {DisplayText}";
					else
						return StringValue;
				}

			}
		}
	}
}
