using AS2Test.Models;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Microsoft.Identity.Web;
using AS2App.Models.ConfigModels;

namespace AS2Test.Extensions
{
    /// <summary>
    /// Provides extension methods for configuring authentication in the application.
    /// </summary>
    public static class AuthenticationExtensions
    {
        /// <summary>
        /// Adds authentication configurations to the specified <see cref="IServiceCollection"/>.
        /// </summary>
        /// <param name="services">The <see cref="IServiceCollection"/> to add the configurations to.</param>
        /// <param name="configuration">The <see cref="IConfiguration"/> containing the authentication configuration settings.</param>
        /// <returns>The modified <see cref="IServiceCollection"/>.</returns>
        //public static IServiceCollection AddAuthenticationConfigurations(this IServiceCollection services, IConfiguration configuration)
        //{
        //services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)


        //.AddJwtBearer(opt =>
        //{
        //    opt.TokenValidationParameters = new TokenValidationParameters
        //    {
        //        ValidateIssuer = true,
        //        ValidateAudience = true,
        //        ValidateLifetime = true,
        //        ValidateIssuerSigningKey = true,
        //        ValidIssuer = configuration["Jwt:Issuer"],
        //        ValidAudience = configuration["Jwt:Audience"],
        //        IssuerSigningKey = new SymmetricSecurityKey(System.Text.Encoding.UTF8.GetBytes(configuration.GetSection("Jwt:Key").Value))
        //    };

        //}); 

        //    

        //    return services;
        //}
        public static IServiceCollection AddAuthenticationConfigurations(this IServiceCollection services, IConfiguration configuration)
        {

            services.Configure<KeyVaultConfig>(configuration.GetSection("KeyVault"));

            services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
                .AddMicrosoftIdentityWebApi(configuration.GetSection("AzureAd"));

            var azureSettings = configuration.GetSection("AzureAd").Get<AzureModel>();

            services.Configure<JwtBearerOptions>(JwtBearerDefaults.AuthenticationScheme, options =>
            {
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    ValidateIssuer = true,
                    ValidIssuer = $"https://sts.windows.net/{azureSettings.TenantId}/",
                    ValidateAudience = false,
                };

                options.Events = new JwtBearerEvents();

                options.Events.OnTokenValidated = async context =>
                {
                    await Task.FromResult(0);
                };
                options.Events.OnAuthenticationFailed = async context =>
                {
                    await Task.CompletedTask;
                };
                options.Events.OnMessageReceived = async context =>
                {
                    await Task.CompletedTask;
                };
            });

            services.AddControllers();
            services.AddEndpointsApiExplorer();

            //services.AddAuthentication(options =>
            //{
            //    options.DefaultScheme = "Keycloak";
            //    options.DefaultChallengeScheme = "Keycloak";
            //})
            //    .AddJwtBearer("Keycloak", options =>
            //    {
            //        options.Audience = configuration["Authentication:Audience"];
            //        options.RequireHttpsMetadata = false;
            //        options.MetadataAddress = configuration["Authentication:MetadataAddress"]
            //                                   ?? throw new InvalidOperationException("MetadataAddress is not configured.");
            //        options.TokenValidationParameters = new Microsoft.IdentityModel.Tokens.TokenValidationParameters
            //        {
            //            ValidIssuer = configuration["Authentication:ValidIssuer"],
            //        };
            //    })
            //    .AddJwtBearer("EntraID", options =>
            //    {
            //        options.Authority = "https://login./(tenant-id)";
            //        options.Audience = "client-id";
            //        options.RequireHttpsMetadata = true;//todo from settings
            //    });


            //services.AddAuthorization(options =>
            //    {
            //        options.AddPolicy("AllowBothSchemas", policy =>
            //        {
            //            policy.AddAuthenticationSchemes("Keycloak", "EntraID");
            //            policy.RequireAuthenticatedUser();
            //        });
            //    }
            //);
            return services;
        }
    }
}