{"AzureAd": {"Instance": "https://login.microsoftonline.com/", "Domain": "softgroup.eu", "TenantId": "6db202c3-153d-4367-864a-8b7a54d46b00", "ClientId": "d004fb86-ed51-4a7f-a189-e8ebe245dc95", "ClientSecret": "****************************************", "CallbackPath": "/signin-oidc"}, "AzureAd_Work": {"Instance": "https://login.microsoftonline.com/", "Domain": "satt.systems", "TenantId": "febb5383-d8e4-4bb9-8393-d08f0e71a005", "ClientId": "ba8c15c9-0401-4363-8758-2f5301e08c14", "ClientSecret": "QMI8Q~WslTgwJJPvAvE~BPStEP7hSJccwy8gncIh", "CallbackPath": "/signin-oidc"}, "AzureAd_Prod": {"Instance": "https://login.microsoftonline.com/", "Domain": "satt.systems", "TenantId": "febb5383-d8e4-4bb9-8393-d08f0e71a005", "ClientId": "977ce94d-fdb1-49a8-9391-04ce9c801b95", "ClientSecret": "****************************************", "CallbackPath": "/signin-oidc"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "EventHistoryClient": {"AzureEventHistoryConnectionUrl": "https://eventhistoryapitest.satt.systems/", "Scope": "api://9ebf6b76-b772-4103-895a-2e94b97979a5/ReadAsUser", "Authority": "https://login.microsoftonline.com/febb5383-d8e4-4bb9-8393-d08f0e71a005", "User": "<EMAIL>", "ClientId": "9ebf6b76-b772-4103-895a-2e94b97979a5", "Password": "2$bM=EG6?7K!#zzd"}, "EventHistoryClient_Prod": {"AzureEventHistoryConnectionUrl": "https://eventhistoryapi.satt.systems/", "Scope": "api://bf87d17c-ace2-4eba-b5ff-2f352beee2a1/ReadAsUser", "Authority": "https://login.microsoftonline.com/febb5383-d8e4-4bb9-8393-d08f0e71a005", "User": "<EMAIL>", "ClientId": "bf87d17c-ace2-4eba-b5ff-2f352beee2a1", "Password": "2$bM=EG6?7K!#zzd"}, "ConnectionStrings": {"AzureCmd": "Server=tcp:sgsqlsrv.database.windows.net,1433;Initial Catalog=CloudMasterData;Persist Security Info=False;User ID=SgAzureUserCMD;Password=****$$3umvir@T;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;", "AzureCmd_Test": "Server=tcp:satt-test.database.windows.net,1433;Initial Catalog=CloudMasterData;Persist Security Info=False;User ID=satt-test-cmdrepl;Password=****************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;", "AzureCmd_Prod": "Server=tcp:satt-prod.database.windows.net,1433;Initial Catalog=CloudMasterData;Persist Security Info=False;User ID=satt-prod-cmdrepl;Password=****************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"}, "AllowedHosts": "*"}