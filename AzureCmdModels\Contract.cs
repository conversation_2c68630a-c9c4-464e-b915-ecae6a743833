﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SGAuditTrail;

namespace AzureCmdModels
{
    [Table("Contract")]
    [Audit(DisplayName = "Contract")]
    public class Contract
    {
        public Contract()
        {
            ServiceContracts = new HashSet<ServiceContract>();
        }

        [Key]
        public int IDContract { get; set; }

        [DisplayName("Customer")]
        public int IDCustomer { get; set; }

        [StringLength(100)]
        [Audit(DisplayName = "Name")]
        [DisplayName("Name")]
        [Required]
        public string Name { get; set; }

        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy}", ApplyFormatInEditMode = true)]
        [Audit(DisplayName = "Start Date")]
        [DisplayName("Start Date")]
        public DateTime StartDate { get; set; }

        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy}", ApplyFormatInEditMode = true)]
        [Audit(DisplayName = "End Date")]
        [DisplayName("End Date")]
        public DateTime EndDate { get; set; }

        [Column("IDContractStatus")]
        [DisplayName("Contract Status")]
        [Audit(DisplayName = "Contract Status", InversePropertyName = "ContractStatus", InversePropertyValue = "Name")]
        public int IDContractStatus { get; set; }


        [Column("IDContractType")]
        [DisplayName("Contract Type")]
        [Audit(DisplayName = "Contract Type", InversePropertyName = "ContractType", InversePropertyValue = "Name")]
        public int IDContractType { get; set; }

        [DisplayName("Maximum Number of Users")]
        [Audit(DisplayName = "Maximum Number of Users")]
        [Range(0, int.MaxValue, ErrorMessage = "Invalid value for maximum number of users.")]
        public int MaxUsers { get; set; }

        [DisplayName("Expiration Warning Days")]
        [Audit(DisplayName = "Expiration Warning Days")]
        public int ExpirationWarningDays { get; set; }

        [Audit(DisplayName = "Serial Numbers")]
        [DisplayName("Serial Numbers")]
        public int SerialNumberCount { get; set; }

        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        [DisplayName("Date Created")]
        public DateTime TimestampCreated { get; set; }

        [StringLength(128)]
        [DisplayName("User Created")]
        [Required]
        public string UserCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        [InverseProperty("Contracts")]
        public Customer Customer { get; set; }

        [InverseProperty("Contracts")]
        public ContractStatus ContractStatus { get; set; }

        [InverseProperty("Contracts")]
        public ContractType ContractType { get; set; }

        [InverseProperty("Contract")]
        public ICollection<ServiceContract> ServiceContracts { get; set; }

        public AuditTrailHeader GetAuditTrailHeader(string user, int page, int pages)
        {
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

            AuditTrailHeader header = new AuditTrailHeader()
            {
                Controler = "Contracts",
                ID = IDContract.ToString(),
                UserName = user,
                Page = page,
                Pages = pages
            };

            header.AddItem("Contract", Name);
            header.AddItem("User", user);
            header.AddItem("Timestamp", timestamp);

            return header;
        }
    }
}
