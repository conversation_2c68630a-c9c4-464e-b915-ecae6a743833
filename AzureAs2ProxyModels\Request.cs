﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AzureAs2ProxyModels
{
    [Table("Request ")]
    public class Request
    {
        [Key]
        public Guid MessageId { get; set; }             

        public int IDCustomer { get; set; }

        public int IDReport { get; set; }

        [Required]
        [StringLength(100)]
        public string UrlEndpoint { get; set; }

        [Required]
        [StringLength(100)]
        public string State { get; set; }

        [StringLength(100)]
        public string StateModifier { get; set; }

        [StringLength(1024)]
        public string ErrorMessage { get; set; }

        public string ResultText { get; set; }
        public bool? IsMicValid { get; set; }

        public bool? IsSignatureValid { get; set; }

        [Required]
        public DateTime TimestampCreated { get; set; }
        public DateTime? TimestampUpdated { get; set; }
    }
}
