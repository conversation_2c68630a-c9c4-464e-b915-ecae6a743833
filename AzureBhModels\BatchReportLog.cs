﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureBhModels
{
    [Table("BatchReportLog")]
    public class BatchReportLog
    {
        [Key]
        public int IDBatchReportLog { get; set; }

        public int IDBatchReport { get; set; }

        public string Type { get; set; }

        public string Message { get; set; }


        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [InverseProperty("BatchReportLogs")]
        public BatchReport BatchReport { get; set; }
    }
}
