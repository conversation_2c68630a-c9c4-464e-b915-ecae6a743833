﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureBhModels
{
    
    public class DBModel : DbContext
    { 
        public virtual DbSet<AuditTrail> AuditTrails { get; set; }     
        public virtual DbSet<Batch> Batches { get; set; }
        public virtual DbSet<BatchSerialization> BatchSerializations { get; set; }
        public virtual DbSet<BatchAggregation> BatchAggregations { get; set; }
        public virtual DbSet<BatchAggregationItem> BatchAggregationItems { get; set; }
        public virtual DbSet<BatchReport> BatchReports { get; set; }
        public virtual DbSet<BatchReportItem> BatchReportItems { get; set; }
        public virtual DbSet<LogisticsReport> LogisticsReports { get; set; }
        public virtual DbSet<LogisticsReportLog> LogisticsReportLogs { get; set; }
        public virtual DbSet<LogisticsReportItem> LogisticsReportItems { get; set; }
        public virtual DbSet<LogisticsReportItemChild> LogisticsReportItemChilds { get; set; }
        public virtual DbSet<Receiver> Receivers { get; set; }
        public virtual DbSet<Sender> Senders { get; set; }
        public virtual DbSet<Setting> Settings { get; set; }
        public virtual DbSet<SgFile> SgFiles { get; set; }
        public virtual DbSet<SgFileVerificationError> SgFileVerificationErrors { get; set; }
        public virtual DbSet<SerialLevel> SerialLevels { get; set; }
        public virtual DbSet<BatchSerialState> BatchSerialStates { get; set; }
        public virtual DbSet<NotAggregatedSerial> NotAggregatedSerials { get; set; }
        public virtual DbSet<SgFileNotAggregatedNumber> SgFileNotAggregatedNumbers { get; set; }
        public virtual DbSet<ValidationStep> ValidationSteps { get; set; }
        public virtual DbSet<BatchReportLog> BatchReportLogs { get; set; }

        public virtual DbSet<ExtendProperty> ExtendProperties { get; set; }
        public virtual DbSet<ExtendPropertySelect> ExtendPropertySelects { get; set; }
        public virtual DbSet<BatchReportExtendProperty> BatchReportExtendProperties { get; set; }
        public virtual DbSet<BatchReportEvent> BatchReportEvents { get; set; }
        public virtual DbSet<AS2Certificate> AS2Certificates { get; set; }


        private readonly string _connectionString;

        public DBModel(string connectionString)
        {
            _connectionString = connectionString;
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseSqlServer(_connectionString);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Batch>(entity =>
            {
                entity.Property(b => b.ExpiryDate).HasColumnType("datetime");
                entity.Property(b => b.ManufacturingDate).HasColumnType("datetime");
            });

            modelBuilder.Entity<BatchReportExtendProperty>(entity =>
            {
                entity.HasOne(p => p.BatchReport)
                  .WithMany(b => b.BatchReportExtendProperties)
                  .HasForeignKey(f => f.IDBatchReport)
                  .OnDelete(DeleteBehavior.Restrict)
                  .HasConstraintName("FK_BatchExtendProperty_BatchReport");
            });

            modelBuilder.Entity<BatchReportExtendProperty>(entity =>
            {
                entity.HasOne(ba => ba.BatchReport)
                   .WithMany(b => b.BatchReportExtendProperties)
                   .HasForeignKey(ba => ba.IDBatchReport)
                   .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<AuditTrail>(entity =>
            {
                entity.Property(at => at.TimestampCreated).HasColumnType("datetime");
            });

            modelBuilder.Entity<BatchSerialization>(entity =>
            {
                entity.HasOne(ba => ba.Batch)
                    .WithMany(b => b.BatchSerializations)
                    .HasForeignKey(ba => ba.IDBatch)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_BatchSerialization_Batch");

                entity.Property(b => b.TimestampCreated).HasColumnType("datetime");
            });

            modelBuilder.Entity<BatchAggregation>(entity =>
            {
                entity.HasOne(ba => ba.Batch)
                    .WithMany(b => b.BatchAggregations)
                    .HasForeignKey(ba => ba.IDBatch)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_BatchAggregation_Batch");

                entity.HasOne(a => a.SgFile)
                    .WithMany(f => f.BatchAggregations)
                    .HasForeignKey(a => a.IDSgFile)
                    .OnDelete(DeleteBehavior.Restrict)
                    .HasConstraintName("FK_Aggregation_SgFile");

                entity.Property(b => b.TimestampCreated).HasColumnType("datetime");
            });

            modelBuilder.Entity<BatchAggregationItem>(entity =>
            {
                entity.HasOne(ai => ai.BatchAggregation)
                    .WithMany(a => a.BatchAggregationItems)
                    .HasForeignKey(ai => ai.IDBatchAggregation)
                    .OnDelete(DeleteBehavior.Restrict)
                    .HasConstraintName("FK_BatchAggregationItem_BatchAggregation");

                entity.Property(b => b.TimestampCreated).HasColumnType("datetime");
            });

            modelBuilder.Entity<BatchReportItem>(entity =>
            {
                entity.HasOne(ba => ba.BatchReport)
                    .WithMany(b => b.BatchReportItems)
                    .HasForeignKey(ba => ba.IDBatchReport)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.Property(b => b.TimestampCreated).HasColumnType("datetime");
            });

            modelBuilder.Entity<BatchReportEvent>(entity =>
            {
                entity.HasOne(ba => ba.BatchReport)
                    .WithMany(b => b.BatchReportEvents)
                    .HasForeignKey(ba => ba.IDBatchReport)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.Property(b => b.TimestampCreated).HasColumnType("datetime");
            });


            modelBuilder.Entity<BatchReport>(entity =>
            {
                entity.HasOne(br => br.Receiver)
                    .WithMany(br => br.BatchReports)
                    .HasForeignKey(br => br.IDReceiver)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_BatchReport_Receiver");

                entity.HasOne(br => br.Sender)
                    .WithMany(br => br.BatchReports)
                    .HasForeignKey(br => br.IDSender)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_BatchReport_Sender");

                entity.HasOne(ba => ba.Batch)
                   .WithMany(b => b.BatchReports)
                   .HasForeignKey(ba => ba.IDBatch)
                   .OnDelete(DeleteBehavior.ClientSetNull)
                   .HasConstraintName("FK_BatchReport_Batch");

                entity.Property(b => b.TimestampCreated).HasColumnType("datetime");
                entity.Property(b => b.PublishTimestamp).HasColumnType("datetime");
                entity.Property(b => b.QueryStatusTimestamp).HasColumnType("datetime");

            });

            modelBuilder.Entity<BatchReportLog>(entity =>
            {
                entity.HasOne(bl => bl.BatchReport)
                    .WithMany(b => b.BatchReportLogs)
                    .HasForeignKey(bl => bl.IDBatchReport)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_BatchReportLog_BatchReport");

                entity.Property(b => b.TimestampCreated).HasColumnType("datetime");

            });

            modelBuilder.Entity<LogisticsReportLog>(entity =>
            {
                entity.HasOne(bl => bl.LogisticsReport)
                    .WithMany(b => b.LogisticsReportLogs)
                    .HasForeignKey(bl => bl.IDLogisticsReport)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_LogisticsReportLog_LogisticReport");

                entity.Property(b => b.TimestampCreated).HasColumnType("datetime");
            });

            modelBuilder.Entity<SgFileVerificationError>(entity =>
            {
                entity.HasOne(e => e.SgFile)
                  .WithMany(r => r.SgFileVerificationErrors)
                  .HasForeignKey(r => r.IDSgFile)
                  .OnDelete(DeleteBehavior.Restrict)
                  .HasConstraintName("FK_SgFileVerificationError_SgFile");

                entity.Property(b => b.TimestampCreated).HasColumnType("datetime");
            });

            modelBuilder.Entity<SgFile>(entity =>
            {
                entity.HasOne(f => f.Batch)
                  .WithMany(b => b.SgFiles)
                  .HasForeignKey(f => f.IDBatch)
                  .OnDelete(DeleteBehavior.Restrict)
                  .HasConstraintName("FK_SgFile_Batch");

                entity.Property(b => b.TimestampCreated).HasColumnType("datetime");
                entity.Property(b => b.TimestampUpdated).HasColumnType("datetime");
            });

            modelBuilder.Entity<VerificationError>(entity =>
            {
                entity.HasOne(vr => vr.Batch)
                  .WithMany(b => b.VerificationErrors)
                  .HasForeignKey(vr => vr.IDBatch)
                  .OnDelete(DeleteBehavior.Restrict)
                  .HasConstraintName("FK_VerificationError_Batch");

                entity.Property(b => b.TimestampCreated).HasColumnType("datetime");
            });

            modelBuilder.Entity<SerialLevel>(entity =>
            {
                entity.HasOne(sl => sl.Batch)
                    .WithMany(b => b.SerialLevels)
                    .HasForeignKey(sl => sl.IDBatch)
                    .OnDelete(DeleteBehavior.Restrict)
                    .HasConstraintName("FK_SerialLevel_Batch");

                entity.Property(b => b.TimestampCreated).HasColumnType("datetime");
            });

            modelBuilder.Entity<BatchSerialState>(entity =>
            {
                entity.HasOne(bs => bs.Batch)
                    .WithMany(b => b.BatchSerialStates)
                    .HasForeignKey(bs => bs.IDBatch)
                    .OnDelete(DeleteBehavior.Restrict)
                    .HasConstraintName("FK_BatchSerialState_Batch");

                entity.Property(b => b.TimestampCreated).HasColumnType("datetime");
            });

            modelBuilder.Entity<NotAggregatedSerial>(entity =>
            {
                entity.HasOne(nas => nas.Batch)
                    .WithMany(b => b.NotAggregatedSerials)
                    .HasForeignKey(nas => nas.IDBatch)
                    .OnDelete(DeleteBehavior.Restrict)
                    .HasConstraintName("FK_NotAggregatedSerial_Batch");

                entity.Property(b => b.TimestampCreated).HasColumnType("datetime");
            });

            modelBuilder.Entity<SgFileNotAggregatedNumber>(entity =>
            {
                entity.HasOne(a => a.SgFile)
                  .WithMany(r => r.SgFileNotAggregatedNumbers)
                  .HasForeignKey(r => r.IDSgFile)
                  .OnDelete(DeleteBehavior.Restrict)
                  .HasConstraintName("FK_SgFileNotAggregatedNumber_SgFile");

                entity.Property(b => b.TimestampCreated).HasColumnType("datetime");

            });

            modelBuilder.Entity<LogisticsReport>(entity =>
            {
                entity.HasOne(l => l.Sender)
                    .WithMany(l => l.LogisticsReports)
                    .HasForeignKey(l => l.IDSender)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_LogisticsReports_Sender");

                entity.HasOne(l => l.Receiver)
                  .WithMany(l => l.LogisticsReports)
                  .HasForeignKey(l => l.IDReceiver)
                  .OnDelete(DeleteBehavior.ClientSetNull)
                  .HasConstraintName("FK_LogisticsReports_Receiver");

                entity.Property(b => b.TimestampCreated).HasColumnType("datetime");
                entity.Property(b => b.ExpiryDate).HasColumnType("datetime");
                entity.Property(b => b.PublishTimestamp).HasColumnType("datetime");
                entity.Property(b => b.QueryStatusTimestamp).HasColumnType("datetime");
            });

            modelBuilder.Entity<LogisticsReportItem>(entity =>
            {
                entity.HasOne(l => l.LogisticsReport)
                    .WithMany(l => l.LogisticsReportItems)
                    .HasForeignKey(l => l.IDLogisticsReport)
                    .OnDelete(DeleteBehavior.Restrict)
                    .HasConstraintName("FK_LogisticsReportItem_LogisticsReport");

                entity.Property(b => b.TimestampCreated).HasColumnType("datetime");

            });

            modelBuilder.Entity<LogisticsReportItemChild>(entity =>
            {
                entity.HasOne(l => l.LogisticsReportItem)
                    .WithMany(l => l.LogisticsReportItemChilds)
                    .HasForeignKey(l => l.IDLogisticsReportItem)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_LogisticsReportItemChild_LogisticsReportItem");

                entity.Property(b => b.TimestampCreated).HasColumnType("datetime");
            });

            modelBuilder.Entity<ExtendPropertySelect>(entity =>
            {
                entity.HasOne(eps => eps.ExtendProperty)
                    .WithMany(ep => ep.ExtendPropertySelects)
                    .HasForeignKey(eps => eps.IDExtendProperty)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_ExtendPropertySelect_ExtendProperty");
            });

        }
    
    }
}
