using System.Text;
using MimeKit;

public class As2HeaderExtractor
{
    public static Dictionary<string, string> ExtractAllHeaders(HttpResponseMessage mdnResponseMessage)
    {
        var headers = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
        MimeMessage mimeMessage;
        var mdnBody = mdnResponseMessage.Content.ReadAsStringAsync().Result;
        using (var stream = new MemoryStream(Encoding.ASCII.GetBytes(mdnBody)))
        {
            stream.Position = 0;
            mimeMessage = MimeMessage.Load(stream);
        }
        // Extract headers from the message itself
        foreach (var header in mimeMessage.Headers)
        {
            AddUniqueHeader(headers, header.Field, header.Value);
        }

        // Extract headers from message body parts
        ExtractHeadersFromParts(mimeMessage.Body, headers);

        return headers;
    }

    
    /// <summary>
    /// Recursively extracts headers from a MIME entity and adds them to the provided dictionary.
    /// </summary>
    /// <param name="entity">The MIME entity to extract headers from. Can be null.</param>
    /// <param name="headers">
    /// A dictionary to store the extracted headers. The header field names are used as keys, 
    /// and their corresponding values are used as dictionary values.
    /// </param>
    /// <remarks>
    /// If the provided entity is a multipart message, this method will recursively process 
    /// each part of the multipart entity to extract headers.
    /// </remarks>
    private static void ExtractHeadersFromParts(MimeEntity entity, Dictionary<string, string> headers)
    {
        if (entity == null) return;

        // Add headers from current entity
        foreach (var header in entity.Headers)
        {
            AddUniqueHeader(headers, header.Field, header.Value);
        }

        // Recursively process if it's a multipart message
        if (entity is Multipart multipart)
        {
            foreach (var part in multipart)
            {
                ExtractHeadersFromParts(part, headers);
            }
        }
    }

    private static void AddUniqueHeader(Dictionary<string, string> headers, string key, string value)
    {
        string uniqueKey = key;
        int counter = 1;

        while (headers.ContainsKey(uniqueKey))
        {
            uniqueKey = $"{key}_{counter++}";
        }

        headers[uniqueKey] = value?.Trim() ?? string.Empty;
    }
}