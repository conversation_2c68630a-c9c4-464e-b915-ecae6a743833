﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEbrModels
{
    [Table("Line")]
    public partial class Line
    {
        public Line()
        {
            Batches = new HashSet<Batch>();
            Machines = new HashSet<Machine>();
            LineTemplates = new HashSet<TemplateLine>();
        }

        [Key]
        [DisplayName("ID")]
        public int IDLine { get; set; }

        [Required]
        [StringLength(100)]
        [DisplayName("Name")]
        public string Name { get; set; }

        [Required]
        [StringLength(50)]
        [DisplayName("Code")]
        public string Code { get; set; }

        [Required]
        [DisplayName("Serialization")]
        public bool HasSerialization { get; set; }

        [Required]
        [DisplayName("Aggregation")]
        public bool HasAggregation { get; set; }

        [Required]
        [DisplayName("Active")]
        public bool IsActive { get; set; }

        [Required]
        [DisplayName("Timestamp created")]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        [DisplayName("User Created")]
        public string UserCreated { get; set; }

        [DisplayName("Timestamp Updated")]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        [DisplayName("User Updated")]
        public string UserUpdated { get; set; }

        [InverseProperty("Line")]
        public ICollection<Batch> Batches { get; set; }

        [InverseProperty("Line")]
        public ICollection<Machine> Machines { get; set; }

        [InverseProperty("Line")]
        public ICollection<TemplateLine> LineTemplates { get; set; }
    }
}
