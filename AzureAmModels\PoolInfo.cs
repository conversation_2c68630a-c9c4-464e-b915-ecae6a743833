﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureAmModels
{
    [Table("PoolInfo")]
    public class PoolInfo
    {
        public PoolInfo()
        {

        }

        [Key]
        public long IDPoolInfo { get; set; }

        public int IDBufferHistory { get; set; }

        [StringLength(100)]
        public string Status { get; set; }

        public int Quantity { get; set; }

        public int LeftInRegistrar { get; set; }

        [StringLength(200)]
        public string RegistrarId { get; set; }

        public bool? IsRegistrarReady { get; set; }

        public int? RegistrarErrorCount { get; set; }

        public long? LastRegistrarErrorTimestamp { get; set; }

        [StringLength(300)]
        public string RejectionReason { get; set; }

        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [InverseProperty("PoolInfos")]
        public BufferHistory BufferHistory { get; set; }



        //public PoolInfo(UZB_PharmaConnector.Json.PoolInfo pool, string user)
        //{
        //    this.Status = pool.Status;
        //    this.Quantity = pool.Quantity;
        //    this.LeftInRegistrar = pool.LeftInRegistrar;
        //    this.RegistrarId = pool.RegistrarId;
        //    this.IsRegistrarReady = pool.IsRegistrarReady;
        //    this.RegistrarErrorCount = pool.RegistrarErrorCount;
        //    this.LastRegistrarErrorTimestamp = pool.LastRegistrarErrorTimestamp;
        //    this.RejectionReason = pool.RejectionReason;
        //    this.TimestampCreated = DateTime.Now;
        //    this.UserCreated = user;
        //}
    }
}
