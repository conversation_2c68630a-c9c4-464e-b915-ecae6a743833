﻿using AzureBhModels;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Globalization;

namespace AzureBhModels
{

    [Table("BatchReportExtendProperty")]
    public class BatchReportExtendProperty
    {
		[Key]
		public int IDBatchReportExtendProperty { get; set; }

		[Required]
		public int IDBatchReport { get; set; }

		[Required]
		[StringLength(100)]
		public string Name { get; set; }

		[StringLength(100)]
		public string StringValue { get; set; }

		[Required]
		[StringLength(100)]
		public ExtendPropertyEnum Type { get; set; }

		[StringLength(1000)]
		public string Regex { get; set; }

		public bool IsRequired { get; set; }

		public bool IsSelectValue { get; set; }

		[StringLength(100)]
		public string DisplayText { get; set; }

		public int IDExtendProperty { get; set; }

        public string BatchReportPropertyReference { get; set; }

		/// <summary>
		/// if == null -> emtry value error
		/// if == true -> wrong value error
		/// </summary>
        public bool? IsError { get; set; }

		[StringLength(500)]
        public string Error { get; set; }

        [Required]
		[DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
		public DateTime TimestampCreated { get; set; }

		[Required]
		[StringLength(128)]
		public string UserCreated { get; set; }

		[DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
		public DateTime? TimestampUpdated { get; set; }

		[StringLength(128)]
		public string UserUpdated { get; set; }

		[InverseProperty("BatchReportExtendProperties")]
		public BatchReport BatchReport { get; set; }

		[NotMapped]
		public List<ExtendPropertySelect> SelectValues { get; set; }

        [NotMapped]
        public bool? BoolValue
        {
            get
            {

                if (this.Type == ExtendPropertyEnum.BOOLEAN && !string.IsNullOrEmpty(this.StringValue))
                    return bool.Parse(this.StringValue);
                else
                    return this.IsRequired ? false : null;
            }
            set
            {
                if (value == null)
                    this.StringValue = string.Empty;
                else
                    this.StringValue = value.ToString();
            }
        }

        [NotMapped]
        public DateTime? DateTimeValue
        {
            get
            {

                if (this.Type == ExtendPropertyEnum.DATETIME && !string.IsNullOrEmpty(this.StringValue))
                {
                    DateTime dtValue;
                    if (DateTime.TryParseExact(this.StringValue, "yyyy-MM-ddTHH:mm:ss.fff", null,
                        System.Globalization.DateTimeStyles.None, out dtValue))
                        return dtValue;
                    else
                        return null;
                }
                else if (this.Type == ExtendPropertyEnum.DATE && !string.IsNullOrEmpty(this.StringValue))
                {
                    DateTime dtValue;
                    if (DateTime.TryParseExact(this.StringValue, "yyyy-MM-dd", null,
                        System.Globalization.DateTimeStyles.None, out dtValue))
                        return dtValue.Date;
                    else
                        return null;
                }
                else
                    return null;
            }
            set
            {
                if (value == null)
                    this.StringValue = string.Empty;
                else
                    this.StringValue = ((DateTime)value).ToString("yyyy-MM-ddTHH:mm:ss.fff");
            }
        }

        [NotMapped]
        public string ConversionError { get; set; }

        [NotMapped]
        public decimal? DecimalValue
        {
            get
            {
                if (this.Type == ExtendPropertyEnum.DECIMAL && !string.IsNullOrEmpty(this.StringValue))
                {
                    ConversionError = string.Empty;
                    if (decimal.TryParse(this.StringValue.Replace(",", "."),
                                            NumberStyles.Number,
                                            CultureInfo.InvariantCulture,
                                            out decimal res))
                        return res;
                    else
                    {
                        ConversionError = $"Error converting [{StringValue}] to DECIMAL";
                        return null;
                    }

                }
                else
                    return null;
            }
            set
            {
                if (value == null)
                    this.StringValue = string.Empty;
                else
                    this.StringValue = value.Value.ToString(CultureInfo.InvariantCulture);
            }
        }

        [NotMapped]
        public int? IntValue
        {
            get
            {
                if (this.Type == ExtendPropertyEnum.INT && !string.IsNullOrEmpty(this.StringValue))
                    return int.Parse(this.StringValue);
                else
                    return null;
            }
            set
            {
                if (value == null)
                    this.StringValue = string.Empty;
                else
                    this.StringValue = value.ToString();
            }
        }

        [NotMapped]
        public long? LongValue
        {
            get
            {
                if (this.Type == ExtendPropertyEnum.INT && !string.IsNullOrEmpty(this.StringValue))
                    return long.Parse(this.StringValue);
                else
                    return null;
            }
            set
            {
                if (value == null)
                    this.StringValue = string.Empty;
                else
                    this.StringValue = value.ToString();
            }
        }
    }
}
