﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureBhModels
{
    [Table("VerificationError")]
    public class VerificationError
    {
        [Key]
        public int IDVerificationError { get; set; }

        [Required]
        public int IDBatch { get; set; }

        public string Level { get; set; }

        [Required]
        public string Error { get; set; }


        public string SerialNumber { get; set; }

        public string SerialType { get; set; }

        public string ItemSerialNumber { get; set; }

        public string ItemSerialType { get; set; }

        public bool IsSkipAllowed { get; set; }


        [Required]
        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [InverseProperty("VerificationErrors")]
        public Batch Batch { get; set; }
    }
}
