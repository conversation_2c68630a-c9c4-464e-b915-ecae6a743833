﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureCmdModels
{
    [Table("ServiceEnvironment")]
    public class ServiceEnvironment
    {
        [Key]
        public int IDServiceEnvironment { get; set; }

        public int IDService { get; set; }

        [StringLength(255)]
        public string Name { get; set; }

        [InverseProperty("ServiceEnvironments")]
        public Service Service { get; set; }
    }
}
