﻿using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Reflection.Emit;

namespace AzureCmdModels
{
    public class DBModel : DbContext
    {
        public virtual DbSet<AuditTrail> AuditTrail { get; set; }
        public virtual DbSet<AuditTrailItem> AuditTrailItem { get; set; }
        public virtual DbSet<UserLogin> UserLogins { get; set; }
        public virtual DbSet<Customer> Customers { get; set; }
        public virtual DbSet<BusinessSegment> BusinessSegments { get; set; }
        public virtual DbSet<Manufacturer> Manufacturers { get; set; }
        public virtual DbSet<MahType> MahTypes { get; set; }
        public virtual DbSet<Country> Countries { get; set; }
        public virtual DbSet<Product> Products { get; set; }
        public virtual DbSet<ProductCodeType> ProductCodeTypes { get; set; }
        public virtual DbSet<CustomerService> CustomerServices { get; set; }
        public virtual DbSet<Service> Services { get; set; }
        public virtual DbSet<ServiceEnvironment> ServiceEnvironments { get; set; }
        public virtual DbSet<Contract> Contracts { get; set; }
        public virtual DbSet<ContractStatus> ContractStatuses { get; set; }
        public virtual DbSet<ServiceContract> ServiceContracts { get; set; }
        public virtual DbSet<ContractType> ContractTypes { get; set; }
        public virtual DbSet<ServiceRole> ServiceRoles { get; set; }
        public virtual DbSet<UserLoginServiceRole> UserLoginServiceRoles { get; set; }
        public virtual DbSet<AdditionalCodeType> AdditionalCodeTypes { get; set; }
        public virtual DbSet<InternalCodeType> InternalCodeTypes { get; set; }
        public virtual DbSet<ProductAdditionalCode> ProductAdditionalCodes { get; set; }
        public virtual DbSet<SerializationTypes> SerializationTypes { get; set; }

        public virtual DbSet<ServiceUserLogin> ServiceUserLogins { get; set; }
        public virtual DbSet<TargetMarket> TargetMarkets { get; set; }

        public virtual DbSet<CustomerContract> CustomerContracts { get; set; }
        public virtual DbSet<SerialNumberSourceType> SerialNumberSourceTypes { get; set; }

        public virtual DbSet<CustomerPartner> CustomerPartners { get; set; }
        public virtual DbSet<CommunicationChannel> CommunicationChannels { get; set; }
        public virtual DbSet<CustomerPartnerParameter> CustomerPartnerParameters { get; set; }

        public virtual DbSet<CustomerAggregationLevel> CustomerAggregationLevels { get; set; }
        public virtual DbSet<LabelTemplate> LabelTemplates { get; set; }

        public virtual DbSet<ProductLabel> ProductLabels { get; set; }
        public virtual DbSet<UserPrinterSetting> UserPrinterSettings { get; set; }
        public virtual DbSet<CustomerLabelDataSource> CustomerLabelDataSources { get; set; }
        public virtual DbSet<FileWatcherConfig> FileWatcherConfigs { get; set; }
        public virtual DbSet<RouterConfig> RouterConfigs { get; set; }
        public virtual DbSet<RouterElement> RouterElements { get; set; }

        public virtual DbSet<SupportedFormat> SupportedFormat { get; set; }
        public virtual DbSet<CustomerSupportedFormat> CustomerSupportedFormat { get; set; }
        public virtual DbSet<AdditionalParam> AdditionalParam { get; set; }
        public virtual DbSet<CustomerAdditionalParam> CustomerAdditionalParam { get; set; }
        public virtual DbSet<EventType> EventTypes { get; set; }
        public virtual DbSet<DbList> DbList { get; set; }
        public virtual DbSet<DbString> DbString { get; set; }

        public DBModel(DbContextOptions<DBModel> options) : base(options)
        {
        }
        public DBModel()
        {
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<LabelTemplate>(entity =>
            {
                entity.HasOne(lt => lt.Customer)
                    .WithMany(c => c.LabelTemplates)
                    .HasForeignKey(lt => lt.IDCustomer)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_LabelTemplate_Customer");
            });

            modelBuilder.Entity<UserLogin>(entity =>
            {
                entity.HasOne(e => e.Customer)
                    .WithMany(e => e.UserLogins)
                    .HasForeignKey(e => e.IDCustomer)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_UserLogin_Customer");

                entity.HasOne(e => e.Manufacturer)
                   .WithMany(e => e.UserLogins)
                   .HasForeignKey(e => e.IDManufacturer)
                   .OnDelete(DeleteBehavior.ClientSetNull)
                   .HasConstraintName("FK_UserLogin_Manufacturer");
            });




            modelBuilder.Entity<Customer>(entity =>
            {
                entity.HasOne(c => c.BusinessSegment)
                  .WithMany(c => c.Customers)
                  .HasForeignKey(c => c.IDBusinessSegment)
                  .OnDelete(DeleteBehavior.ClientSetNull)
                  .HasConstraintName("FK_Customer_BusinessSegment");
            });

            modelBuilder.Entity<ProductAdditionalCode>(entity =>
            {
                entity.HasOne(e => e.Product)
                    .WithMany(e => e.ProductAdditionalCodes)
                    .HasForeignKey(e => e.IDProduct)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Product_ProductAdditionalCodes");

            });


            modelBuilder.Entity<UserLoginServiceRole>(entity =>
            {
                entity.HasOne(e => e.UserLogin)
                    .WithMany(e => e.UserLoginServiceRoles)
                    .HasForeignKey(e => e.LgnName)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_UserLoginServiceRole_UserLogin");

                entity.HasOne(e => e.ServiceRole)
                   .WithMany(e => e.UserLoginServiceRoles)
                   .HasForeignKey(e => e.IDServiceRole)
                   .OnDelete(DeleteBehavior.ClientSetNull)
                   .HasConstraintName("FK_UserLoginServiceRole_ServiceRole");
            });

            modelBuilder.Entity<ServiceUserLogin>(entity =>
            {
                entity.HasOne(e => e.UserLogin)
                    .WithMany(e => e.ServiceUserLogins)
                    .HasForeignKey(e => e.LgnName)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ServiceUserLogin_UserLogin");

                entity.HasOne(e => e.Service)
                   .WithMany(e => e.ServiceUserLogins)
                   .HasForeignKey(e => e.IDService)
                   .OnDelete(DeleteBehavior.ClientSetNull)
                   .HasConstraintName("FK_ServiceUserLogin_Service");

                entity.HasOne(e => e.Manufacturer)
                   .WithMany(e => e.ServiceUserLogins)
                   .HasForeignKey(e => e.IDManufacturer)
                   .OnDelete(DeleteBehavior.ClientSetNull)
                   .HasConstraintName("FK_ServiceUserLogin_Manufacturer");
            });

            modelBuilder.Entity<Contract>(entity =>
            {
                entity.HasOne(e => e.Customer)
                   .WithMany(e => e.Contracts)
                   .HasForeignKey(e => e.IDCustomer)
                   .OnDelete(DeleteBehavior.ClientSetNull)
                   .HasConstraintName("FK_Contract_Customer");

                entity.HasOne(e => e.ContractStatus)
                  .WithMany(e => e.Contracts)
                  .HasForeignKey(e => e.IDContractStatus)
                  .OnDelete(DeleteBehavior.ClientSetNull)
                  .HasConstraintName("FK_Contract_ContractStatus");

                entity.HasOne(e => e.ContractType)
                  .WithMany(e => e.Contracts)
                  .HasForeignKey(e => e.IDContractType)
                  .OnDelete(DeleteBehavior.ClientSetNull)
                  .HasConstraintName("FK_Contract_ContractType");

            });


            modelBuilder.Entity<AuditTrailItem>(entity =>
            {
                entity.HasOne(e => e.AuditTrail)
                    .WithMany(e => e.Items)
                    .HasForeignKey(e => e.IDAuditTrail)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_AuditTrailItem_AuditTrail");

            });

            modelBuilder.Entity<Manufacturer>(entity =>
            {
                entity.HasOne(m => m.Customer)
                    .WithMany(m => m.Manufacturers)
                    .HasForeignKey(m => m.IDCustomer)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Manufacturer_Customer");

                entity.HasOne(m => m.MahType)
                    .WithMany(m => m.Manufacturers)
                    .HasForeignKey(m => m.IDMahType)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Manufacturer_MahType");

            });

            modelBuilder.Entity<ProductLabel>(entity =>
            {
                entity.HasOne(pl => pl.Product)
                    .WithMany(p => p.ProductLabels)
                    .HasForeignKey(pl => pl.IDProduct)
                    .HasPrincipalKey(pl => pl.IDProduct)
                    .OnDelete(DeleteBehavior.ClientSetNull);

                entity.HasOne(pl => pl.TargetMarket)
                    .WithMany(tm => tm.ProductLabels)
                    .HasForeignKey(pl => pl.IDTargetMarket)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasPrincipalKey(pl => pl.IDTargetMarket);
            });

            modelBuilder.Entity<Product>(entity =>
            {
                entity.HasOne(p => p.Customer)
                    .WithMany(p => p.Products)
                    .HasForeignKey(p => p.IDCustomer)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Product_Customer");

                entity.HasOne(p => p.Service)
                   .WithMany(p => p.Products)
                   .HasForeignKey(p => p.IDServiceMasterRecord)
                   .OnDelete(DeleteBehavior.ClientSetNull)
                   .HasConstraintName("FK_Product_Service");

                entity.HasOne(p => p.ProductCodeType)
                  .WithMany(p => p.Products)
                  .HasForeignKey(p => p.CodeType)
                  .OnDelete(DeleteBehavior.ClientSetNull)
                  .HasConstraintName("FK_Product_ProductCodeType");

                entity.HasOne(m => m.CustomerPartner)
                  .WithMany(m => m.Products)
                  .HasForeignKey(m => m.IDCustomerPartner)
                  .OnDelete(DeleteBehavior.ClientSetNull)
                  .HasConstraintName("FK_Product_CustomerPartner");

            });

            modelBuilder.Entity<Service>(entity =>
            {
                entity.HasOne(p => p.SerialNumberSourceType)
                  .WithMany(p => p.Services)
                  .HasForeignKey(p => p.IDService)
                  .OnDelete(DeleteBehavior.ClientSetNull)
                  .HasConstraintName("FK_Service_SerialNumberSourceType");

            });

            modelBuilder.Entity<CustomerPartnerParameter>(entity =>
            {
                entity.HasOne(p => p.CustomerPartner)
                  .WithMany(p => p.CustomerPartnerParameters)
                  .HasForeignKey(p => p.IDCustomerPartner)
                  .OnDelete(DeleteBehavior.ClientSetNull)
                  .HasConstraintName("FK_CustomerPartnerParameter_CustomerPartner");

            });

            modelBuilder.Entity<CustomerService>(entity =>
            {
                entity.HasOne(e => e.Service)
                    .WithMany(e => e.CustomerServices)
                    .HasForeignKey(e => e.IDService)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_CustomerService_Service");

                entity.HasOne(e => e.Customer)
                  .WithMany(e => e.CustomerServices)
                  .HasForeignKey(e => e.IDCustomer)
                  .OnDelete(DeleteBehavior.ClientSetNull)
                  .HasConstraintName("FK_CustomerService_Customer");

            });

            modelBuilder.Entity<ServiceContract>(entity =>
            {
                entity.HasOne(e => e.Service)
                    .WithMany(e => e.ServiceContracts)
                    .HasForeignKey(e => e.IDService)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ServiceContract_Service");

                entity.HasOne(e => e.Contract)
                  .WithMany(e => e.ServiceContracts)
                  .HasForeignKey(e => e.IDContract)
                  .OnDelete(DeleteBehavior.ClientSetNull)
                  .HasConstraintName("FK_ServiceContract_Contract");

            });

            modelBuilder.Entity<ServiceEnvironment>(entity =>
            {
                entity.HasOne(e => e.Service)
                    .WithMany(e => e.ServiceEnvironments)
                    .HasForeignKey(e => e.IDService)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ServiceEnvironments_Service");

            });

            modelBuilder.Entity<ProductAdditionalCode>(entity =>
            {
                entity.HasOne(p => p.AdditionalCodeType)
                  .WithMany(p => p.ProductAdditionalCodes)
                  .HasForeignKey(p => p.CodeType)
                  .OnDelete(DeleteBehavior.ClientSetNull)
                  .HasConstraintName("FK_ProductAdditionalCode_AdditionalCodeType");

            });

            modelBuilder.Entity<CustomerContract>(entity =>
            {
                entity.HasOne(t => t.Customer)
                    .WithMany(c => c.CustomerContracts)
                    .HasForeignKey(t => t.IDCustomer)
                    .OnDelete(DeleteBehavior.Restrict)
                    .HasConstraintName("FK_CustomerContracts_Customer");
            });


            modelBuilder.Entity<CustomerPartner>(entity =>
            {
                entity.HasOne(p => p.Customer)
                    .WithMany(p => p.CustomerPartners)
                    .HasForeignKey(p => p.IDCustomer)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_CustomerPartner_Customer");
            });

            modelBuilder.Entity<CustomerAggregationLevel>(entity =>
            {
                entity.HasOne(cal => cal.Customer)
                    .WithMany(c => c.CustomerAggregationLevels)
                    .HasForeignKey(cal => cal.IDCustomer)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_CustomerAggregationLevel_Customer");

                entity.HasOne(cal => cal.Product)
                   .WithMany(c => c.ProductAggregationLevels)
                   .HasForeignKey(cal => cal.IDProduct)
                   .OnDelete(DeleteBehavior.ClientSetNull)
                   .HasConstraintName("FK_CustomerAggregationLevel_Product");
            });

            modelBuilder.Entity<CustomerLabelDataSource>(entity =>
            {
                entity.HasOne(cds => cds.Customer)
                    .WithMany(cds => cds.CustomerLabelDataSources)
                    .HasForeignKey(cds => cds.IDCustomer)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_CustomerDataSource_Customer");
            });

            modelBuilder.Entity<RouterElement>(entity =>
            {
                entity.HasOne(e => e.RouterConfig)
                   .WithMany(e => e.RouterElements)
                   .HasForeignKey(e => e.IDRouterConfig)
                   .OnDelete(DeleteBehavior.ClientSetNull)
                   .HasConstraintName("FK_RouterElement_RouterConfig");
            });

            modelBuilder.Entity<CustomerSupportedFormat>(entity =>
            {
                entity.HasOne(e => e.SupportedFormat)
               .WithMany(e => e.CustomerSupportedFormats)
               .HasForeignKey(e => e.IDSupportedFormat)
               .OnDelete(DeleteBehavior.ClientSetNull)
               .HasConstraintName("FK_CustomerSupportedFormat_SupportedFormat");
            }
         );


            modelBuilder.Entity<CustomerAdditionalParam>(entity =>
            {
                entity.HasOne(e => e.AdditionalParam)
                    .WithMany(e => e.CustomerAdditionalParams)
                    .HasForeignKey(e => e.IDAdditionalParam)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_CustomerAdditionalParam_AdditionalParam");


            });

            modelBuilder.Entity<AdditionalParam>(entity =>
            {
                entity.HasOne(e => e.SupportedFormat)
                    .WithMany(e => e.AdditionalParams)
                    .HasForeignKey(e => e.IDSupportedFormat)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_AdditionalParam_SupportedFormat");

            });

            modelBuilder.Entity<CustomerSupportedFormat>(entity =>
            {
                entity.HasOne(e => e.SupportedFormat)
                    .WithMany(e => e.CustomerSupportedFormats)
                    .HasForeignKey(e => e.IDSupportedFormat)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_CustomerSupportedFormat_SupportedFormat");

            });

            modelBuilder.Entity<FileWatcherConfig>(entity =>
            {
                entity.HasOne(m => m.EventType)
                    .WithMany(m => m.FileWatcherConfigs)
                    .HasForeignKey(m => m.IDFileWatcherConfig)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_FileWatcherConfig_EventType");

                entity.HasOne(e => e.Customer)
                  .WithMany(e => e.FileWatcherConfigs)
                  .HasForeignKey(e => e.IDCustomer)
                  .OnDelete(DeleteBehavior.ClientSetNull)
                  .HasConstraintName("FK_FileWatcherConfig_Customer");

            });
        }
    }
}
