﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureAmModels
{
    [Table("OrderSerial")]
    public class OrderSerial
    {

        [Key]
        public long IDOrderSerial { get; set; }

        [Required]
        public int IDOrder { get; set; }

        [StringLength(20)]
        public string SerialNumber { get; set; }

        [InverseProperty("Serials")]
        public Order Order { get; set; }


        public override bool Equals(object obj)
        {
            if (obj == null)
                return false;

            if (obj.GetType() == this.GetType())
            {
                if (((OrderSerial)obj).IDOrderSerial == this.IDOrderSerial)
                    return true;
                else
                    return false;
            }
            else
                return false;
        }
    }
}