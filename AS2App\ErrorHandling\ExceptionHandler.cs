using AS2Test.ErrorHandling;
using AS2Test.Models;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Mvc;

namespace AS2Test.Helpers
{
    /// <summary>
    /// Provides functionality to handle exceptions asynchronously.
    /// </summary>
    public class ExceptionHandler : IExceptionHandler
    {
        /// <summary>
        /// Tries to handle the exception asynchronously.
        /// </summary>
        /// <param name="httpContext">The HttpContext.</param>
        /// <param name="exception">The exception.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A boolean indicating whether the exception was handled successfully.</returns>
        public async ValueTask<bool> TryHandleAsync(
            HttpContext httpContext, 
            Exception exception, 
            CancellationToken cancellationToken)
        {
            var serviceResponce = ErrorResponseBuilder.CreateErrorResponse(exception, httpContext.Request.Path);
            httpContext.Response.StatusCode = serviceResponce.Error?.Status ?? StatusCodes.Status500InternalServerError;
            await httpContext.Response.WriteAsJsonAsync(serviceResponce,cancellationToken);
            
            return true;
        }
    }
}