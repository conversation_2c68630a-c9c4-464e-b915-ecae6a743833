﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEbrModels
{
    [Table("MachineSerial")]
    public class MachineSerial
    {
        [Key]
        public int IDMachineSerial { get; set; }

        public int IDMachine { get; set; }

        public int IDBatch { get; set; }

        [Required]
        [StringLength(20)]
        [DisplayName("Serial Number")]
        public string SerialNumber { get; set; }

        [StringLength(500)]
        public string Barcode { get; set; }

        [InverseProperty("MachineSerials")]
        public Machine Machine { get; set; }
    }
}
