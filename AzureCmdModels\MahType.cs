﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SGAuditTrail;

namespace AzureCmdModels
{
    [Table("MahType")]
    [Audit(DisplayName = "Manufacturer Type")]
    public class MahType
    {
        public MahType()
        {
            Manufacturers = new HashSet<Manufacturer>();
        }

        [Key]
        [Column("IDMahType")]
        public int IDMahType { get; set; }

        [StringLength(50)]
        [Audit(DisplayName = "Type")]
        public string Type { get; set; }

        [InverseProperty("MahType")]
        public ICollection<Manufacturer> Manufacturers { get; set; }

        //TODO: Delete table;
    }
}
