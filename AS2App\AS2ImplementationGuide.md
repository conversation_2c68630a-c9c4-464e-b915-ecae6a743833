|Project|
|----|
|AS2 Test Sender|
 

 ### ToDo:
 - 28.09.24 
    * Add case logic depending on request data to call different types of AS2 content types
    * Add Header logic if request to content tyoes 
    * Refine MDN handle 

 https://aayutechnologies.com/docs/as2/mime-smime-and-as2/
 https://aayutechnologies.com/docs/as2/how-as2-works/
 > Add all common AS2 fetures as methods without implementation NotImplementedException exception

 > Add Secure storeage API calls for partners key /Azure, or HashiCorp Keyvault/

 > Add authentication with two type s of authentication Bearer and EntraIF

|Project Description|
|----|
|The Service aim is to handle the API communication with the Mobile App front-end for the one side and the worker services communication /per regulation/ on the other. It handles the Authorization and Authentication, also, provides basic API data validation. |

|Component|Definition|
|----|-----|
|Encryption|The sender encrypts the payload with the receiver public key. Commonly used alorithms of encryption are 3DES and AES-256|
|Digital Signatures - Sign|AS2 also uses digital signatures, which allow the user to guarantee the authenticity of the sender/receiver. First, the sender signs the payload with a private key. The receiver then verifies the origin and authenticity of the message using the sender’s public key. Commonly used alg. are SHA1, SHA256, SHA512|
|MDN - Acknowledgment|Message Disposition Notification (MDN) serves as an acknowledgement of the message transfer to ensure non-repudiation. It is a digitally signed receipt of a file which is received by the recipient and sent back to the message sender.|
|MIC - Message Integrity Check|Hash function. The message integrity check (MIC) is connected to the MDN, and ensures the integrity of the message content. It is calculated with a secure hash function over the payload. - The receiver calculates the MIC over the received payload and sends the MDN, including the MIC value, back to the sender. If the returned MIC value equals the original calculated MIC value, the payload is an integer.|

## 1. Referneces

- RFC4130: https://www.ietf.org/rfc/rfc4130.txt
- RFC4130: https://www.rfc-editor.org/rfc/rfc4130.txt
- https://dzone.com/articles/how-to-verify-as2-message-smime-signature-with-opehttps://dzone.com/articles/how-to-verify-as2-message-smime-signature-with-ope
## 2. General AS2 Guidance

 At the Sender
  --
  ##### Note: MIC is done on the payload before it si signed, encrypted or compressed. AS2 body can content MIME /Multipurpose Internet Mail Extension/ payload or S/MIME /Secure Multipurpose Internet Mail Extension/. MIME mayload can only be compressed. S/MIME can be signed, ancrypted and cmpressed.


- Use a secure hash function to calculate the Message Integrity Check (MIC) or the Message Digest. The hash algorithms most commonly used are MD5, SHA-1 or SHA-256
- Digitally sign the file content using the sender’s private key, and place the file contents and signature into a MIME message.
Encrypt the MIME message with the file contents and signature, using the receiver’s public key or certificate. Overwrite the MIME message with the now encrypted content. (Note when decrypted, this the contents of step #2 above will be available, including the signature, and the file contents)
Add AS2 protocol specific headers such as AS2-From, AS2-To etc, and transmit S/MIME message as a HTTP POST to the partner’s URL

At the Receiver
- Check received message AS2 headers to verify that the received message is intended for the recipient, and the sender is known.
- Decrypt the outermost payload using the receiver’s private key
- Verify the digital signature placed by the sender, to verify the partner sending the message, and the payload was not altered
- Compute Message Integrity Check (MIC), by hashing the original payload received
- Create Message Disposition Notification (MDN) by digitally signing the received MIC with the receiver’s private key.
- Send the MDN back to the sender using the HTTP response (i.e. synchronous MDN) or a new HTTP message (i.e. asynchronous MDN)
 
At the Sender, receiving an MDN
- After receiving an MDN, verify its signature with the partner’s certificate to confirm it was digitally signed and sent by the claimed partner which received the original message intact.

HTTPS can be used instead of HTTP as the transport mechanism, although using HTTP still provides all the guarantees of AS2
Signing, Encryption and MDNs are not enforced by the AS2 specification, and are left for the individual partners to decide and use.
The certificates of each of the partners contain the public key of its asymmetric key pair. These are exchanged prior to receiving messages from such partners, usually over email.
Certificates used in AS2 does not require signing by a third party trusted Certification Authority (CA).

![A mushroom-head robot](Docs\assets\sch.jpg)

## 3. AS2 Specific Headers

|AS2 Header|Cardinality|Value|
|----|----|----|
|AS2-Version|No|1.1|
|AS2-From|yes|Sending party String  upt ot 128 chars|
|AS2-To|yes|Receiving party String  upt ot 128 chars|


## 4.  Structure of an Internet EDI MIME Message

   No encryption, no signature
      -RFC2616/2045
         -RFC1767/RFC3023 (application/EDIxxxx or /xml)

   No encryption, signature
      -RFC2616/2045
        -RFC1847 (multipart/signed)
          -RFC1767/RFC3023 (application/EDIxxxx or /xml)
          -RFC3851 (application/pkcs7-signature)

   Encryption, no signature
      -RFC2616/2045
        -RFC3851 (application/pkcs7-mime)
          -RFC1767/RFC3023  (application/EDIxxxx or /xml)(encrypted)

   Encryption, signature
      -RFC2616/2045
        -RFC3851 (application/pkcs7-mime)
          -RFC1847 (multipart/signed)(encrypted)
            -RFC1767/RFC3023  (application/EDIxxxx or /xml)(encrypted)
            -RFC3851 (application/pkcs7-signature)(encrypted)

   MDN over HTTP, no signature
      -RFC2616/2045
        -RFC3798 (message/disposition-notification)

   MDN over HTTP, signature
      -RFC2616/2045
        -RFC1847 (multipart/signed)
         -RFC3798 (message/disposition-notification)
         -RFC3851 (application/pkcs7-signature)

   MDN over SMTP, no signature
   MDN over SMTP, signature
     Refer to the EDI over SMTP standard [4].

   Although all MIME content types SHOULD be supported, the following
   MIME content types MUST be supported:

             Content-type: multipart/signed
             Content-Type: multipart/report
             Content-type: message/disposition-notification
             Content-Type: application/PKCS7-signature
             Content-Type: application/PKCS7-mime
             Content-Type: application/EDI-X12