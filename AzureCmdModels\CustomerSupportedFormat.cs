﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureCmdModels
{
    [Table("CustomerSupportedFormat")]
    public class CustomerSupportedFormat
    {

        [Key]
        public long IDCustomerSupportedFormat { get; set; }

        public int IDCustomer { get; set; }

        public int IDSupportedFormat { get; set; }

        [InverseProperty("CustomerSupportedFormats")]
        public SupportedFormat SupportedFormat { get; set; }


    }
}
