﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEbrModels
{
    [Table("BatchSerializationExtend")]
    public class BatchSerializationExtend
    {
        [Key]
        public int IDBatchSerializationExtend { get; set; }

        [Required]
        public int IDBatchSerialization { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        [Required]
        [StringLength(100)]
        public string StringValue { get; set; }

        [Required]
        public ExtendPropertyEnum Type { get; set; }

        [StringLength(1000)]
        public string Regex { get; set; }

        public bool IsRequired { get; set; }

        public bool IsSelectValue { get; set; }

        [StringLength(100)]
        public string DisplayText { get; set; }

        [StringLength(10)]
        public string GS1AICode { get; set; }

        [Required]
        public int IDExtendProperty { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        [InverseProperty("BatchSerializationExtends")]
        public BatchSerialization BatchSerialization { get; set; }

        [NotMapped]
        public List<ExtendPropertySelect> SelectValues { get; set; }

        [NotMapped]
        public string ConversionError { get; set; }

        [NotMapped]
        public bool? BoolValue
        {
            get
            {
                if (this.Type == ExtendPropertyEnum.BOOLEAN && !string.IsNullOrEmpty(this.StringValue)
                        && bool.TryParse(this.StringValue, out bool bvalue))
                    return bvalue;
                else
                    return this.IsRequired ? false : null;
            }
            set
            {
                if (value == null)
                    this.StringValue = string.Empty;
                else
                    this.StringValue = value.ToString();
            }
        }

        [NotMapped]
        public DateTime? DateTimeValue
        {
            get
            {

                if (this.Type == ExtendPropertyEnum.DATETIME && !string.IsNullOrEmpty(this.StringValue))
                {
                    DateTime dtValue;
                    if (DateTime.TryParseExact(this.StringValue, "yyyy-MM-ddTHH:mm:ss.fff", null,
                        System.Globalization.DateTimeStyles.None, out dtValue))
                        return dtValue;
                    else
                        return null;
                }
                else if (this.Type == ExtendPropertyEnum.DATE && !string.IsNullOrEmpty(this.StringValue))
                {
                    DateTime dtValue;
                    if (DateTime.TryParseExact(this.StringValue, "yyyy-MM-ddTHH:mm:ss.fff", null,
                        System.Globalization.DateTimeStyles.None, out dtValue))
                        return dtValue.Date;
                    else
                        return null;
                }
                else
                    return null;
            }
            set
            {
                if (value == null)
                    this.StringValue = string.Empty;
                else
                    this.StringValue = ((DateTime)value).ToString("yyyy-MM-ddTHH:mm:ss.fff");
            }
        }

        [NotMapped]
        public decimal? DecimalValue
        {
            get
            {
                if (this.Type == ExtendPropertyEnum.DECIMAL && !string.IsNullOrEmpty(this.StringValue))
                {
                    ConversionError = string.Empty;
                    if (decimal.TryParse(this.StringValue.Replace(",", "."),
                                            NumberStyles.Number,
                                            CultureInfo.InvariantCulture,
                                            out decimal res))
                        return res;
                    else
                    {
                        ConversionError = $"Error converting [{StringValue}] to DECIMAL";
                        return null;
                    }

                }
                else
                    return null;
            }
            set
            {
                if (value == null)
                    this.StringValue = string.Empty;
                else
                    this.StringValue = value.Value.ToString(CultureInfo.InvariantCulture);
            }
        }

        [NotMapped]
        public int? IntValue
        {
            get
            {
                if (this.Type == ExtendPropertyEnum.INT && !string.IsNullOrEmpty(this.StringValue)
                    && int.TryParse(this.StringValue, out int ivalue))
                    return ivalue;
                else
                    return null;
            }
            set
            {
                if (value == null)
                    this.StringValue = string.Empty;
                else
                    this.StringValue = value.ToString();
            }
        }

        [NotMapped]
        public string Value
        {
            get
            {
                if (Type == ExtendPropertyEnum.DATETIME && DateTimeValue.HasValue)
                    return DateTimeValue?.ToString("yyyy-MM-dd HH:mm:ss");
                else if (Type == ExtendPropertyEnum.DATE && DateTimeValue.HasValue)
                    return DateTimeValue?.ToString("yyyy-MM-dd");
                else
                {
                    if (IsSelectValue && !string.IsNullOrEmpty(StringValue))
                        return $"({StringValue}) {DisplayText}";
                    else
                        return StringValue;
                }

            }
        }
    }
}
