﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureAmModels
{
    public class ApiAggregationRequest
    {
        public string Gtin { get; set; }
        public string BatchId { get; set; }
        public DateTime ExpiryDate { get; set; }
        public string Line { get; set; }
        public string ParticipantId { get; set; }
        public string ProductionOrderId { get; set; }
        public List<ApiAggregationItem> AggregationItems { get; set; }
    }

    public class ApiAggregationItem
    {
        public string Level { get; set; }
        public int UnitCapacity { get; set; }
        public string SerialType { get; set; }
        public string SerialNumber { get; set; }
        public string ItemSerialType { get; set; }
        public List<string> Items { get; set; }
    }
}
