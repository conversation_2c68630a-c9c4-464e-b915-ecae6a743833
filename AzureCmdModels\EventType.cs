﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureCmdModels
{
    [Table("EventType")]
    public partial class EventType
    {
        [Key]
        [DisplayName("Event Type")]
        public int IDEventType { get; set; }

        [Required]
        [StringLength(128)]
        [DisplayName("Name")]
        public string Name { get; set; }

        [StringLength(128)]
        [DisplayName("Application Name")]
        public string AppName { get; set; }


        [InverseProperty("EventType")]
        public ICollection<FileWatcherConfig> FileWatcherConfigs { get; set; }
    }
}
