﻿using System.ComponentModel.DataAnnotations;
using System.Runtime.Serialization;
using System.Runtime.Serialization.Json;
using System.Text;

namespace AzureCmdModels.JsonModels
{
    [DataContract]
    public class ApiProduct
    {
        public ApiProduct()
        {
            AdditionalCodes = new List<ApiProductAdditionalCode>();
            Labels = new List<ApiProductLabel>();
            AggregationLevels = new List<ApiProductAggregationLevel>();
        }

        public ApiProduct(string json)
        {
            var obj = CustomJSON.Deserialize(json, typeof(ApiProduct)) as ApiProduct;
            if (obj == null)
                throw new Exception("Deserialize error");

            this.AdditionalCodes = obj.AdditionalCodes;
            this.IDServiceMasterRecord = obj.IDServiceMasterRecord;
            this.Code = obj.Code;
            this.CodeType = obj.CodeType;
            this.CommonName = obj.CommonName;
            this.Form = obj.Form;
            this.IDCustomer = obj.IDCustomer;
            this.IDCustomerPartner = obj.IDCustomerPartner;
            this.IDProduct = obj.IDProduct;
            this.IDServiceMasterRecord = obj.IDServiceMasterRecord;
            this.InternalCode = obj.InternalCode;
            this.InternalCodeType = obj.InternalCodeType;
            this.IsActive = obj.IsActive;
            this.Labels = obj.Labels;
            this.AggregationLevels = obj.AggregationLevels;
            this.Name = obj.Name;
            this.PackSize = obj.PackSize;
            this.PackType = obj.PackType;
            this.SerializationType = obj.SerializationType;
            this.SerialNumberSourceType = obj.SerialNumberSourceType;
            this.Strength = obj.Strength;
            this.User = obj.User;
        }

        [DataMember(Name ="idProduct")]
        public int IDProduct { get; set; }

        [DataMember(Name ="idCustomer")]
        public int IDCustomer { get; set; }

        [DataMember(Name ="idServiceMasterRecord")]
        [Required]
        [Range(1, int.MaxValue, ErrorMessage = "No service selected, or invalid value.")]
        public int IDServiceMasterRecord { get; set; }

        [DataMember(Name ="codeType")]
        [Required]
        [MaxLength(10)]
        public string CodeType { get; set; }

        [DataMember(Name ="code")]
        [Required]
        [MaxLength(14)]
        public string Code { get; set; }

        [DataMember(Name ="internalCodeType")]
        public string InternalCodeType { get; set; }

        [DataMember(Name ="internalCode")]
        public string InternalCode { get; set; }

        [DataMember(Name ="name")]
        [Required]
        [MaxLength(255)]
        public string Name { get; set; }

        [DataMember(Name ="commonName")]
        [MaxLength(255)]
        public string CommonName { get; set; }

        [DataMember(Name ="form")]
        [MaxLength(100)]
        public string Form { get; set; }

        [DataMember(Name ="packType")]
        [MaxLength(50)]
        public string PackType { get; set; }

        [DataMember(Name ="packSize")]
        public int? PackSize { get; set; }

        [DataMember(Name ="strength")]
        [MaxLength(100)]
        public string Strength { get; set; }

        [DataMember(Name ="isActive")]
        [Required]
        public bool IsActive { get; set; }

        [DataMember(Name ="user")]
        public string User { get; set; }

        [DataMember(Name ="serialNumberSourceType")]
        [MaxLength(50)]
        public string SerialNumberSourceType { get; set; }

        [DataMember(Name ="serializationType")]
        [MaxLength(128)]
        public string SerializationType { get; set; }

        [DataMember(Name ="idCustomerPartner")]
        public int? IDCustomerPartner { get; set; }

        [DataMember(Name ="additionalCodes")]
        public List<ApiProductAdditionalCode> AdditionalCodes { get; set; }

        [DataMember(Name ="labels")]
        public List<ApiProductLabel> Labels { get; set; }

        [DataMember(Name="aggregationLevels")]
        public List<ApiProductAggregationLevel> AggregationLevels { get; set; }

        public Product ToProductModel(string lgnName)
        {
            Product product = new Product()
            {
                Code = this.Code,
                CodeType = this.CodeType,
                CommonName = this.CommonName,
                Form = this.Form,
                IDCustomer = this.IDCustomer,
                IDCustomerPartner = this.IDCustomerPartner,
                IDProduct = this.IDProduct,
                IDServiceMasterRecord = this.IDServiceMasterRecord,
                InternalCode = this.InternalCode,
                InternalCodeType = this.InternalCodeType,
                IsActive = this.IsActive,
                Name = this.Name,
                PackSize = this.PackSize.HasValue ? this.PackSize.Value : 1,
                PackType = this.PackType,
                SerializationType = this.SerializationType,
                SerialNumberSourceType = this.SerialNumberSourceType,
                Strength = this.Strength
               
            };

            if (product.IDProduct == 0)
            {
                product.UserCreated = lgnName;
                product.TimestampCreated = DateTime.Now;
            }
            else
            {
                product.UserUpdated = lgnName;
                product.TimestampUpdated = DateTime.Now;
            }

            return product;
        }
    }
    [DataContract]
    public class ApiProductAdditionalCode
    {
        [DataMember(Name ="idProductAdditionalCode")]
        public int IDProductAdditionalCode { get; set; }

        [DataMember(Name ="codeType")]
        [Required]
        [MaxLength(50)]
        public string CodeType { get; set; }

        [DataMember(Name ="code")]
        [Required]
        [MaxLength(50)]
        public string Code { get; set; }

        public ProductAdditionalCode ToProductAdditionalCode(int idProduct, string user)
        {
            ProductAdditionalCode result = new ProductAdditionalCode()
            {
                IDProductAdditionalCode = this.IDProductAdditionalCode,
                IDProduct = idProduct,
                Code = this.Code,
                CodeType = this.CodeType,
            };

            if(this.IDProductAdditionalCode == 0)
            {
                result.UserCreated = user;
                result.TimestampCreated = DateTime.Now;
            }
            else
            {
                result.UserUpdated = user;
                result.TimestampUpdated = DateTime.Now;
            }

            return result;
        }
    }

    [DataContract]
    public class ApiProductLabel
    {
        [DataMember(Name ="idProductLabel")]
        public int IDProductLabel { get; set; }

        [DataMember(Name ="labelName")]
        [Required]
        [MaxLength(255)]
        public string LabelName { get; set; }

        [DataMember(Name ="label")]
        public string Label { get; set; }

        [DataMember(Name ="level")]
        [MaxLength(20)]
        public string Level { get; set; }

        [DataMember(Name ="isDefault")]
        public bool IsDefault { get; set; }

        [DataMember(Name ="idTargetMarket")]
        public int? IDTargetMarket { get; set; }

        [DataMember(Name ="printingSrcSystem")]
        [MaxLength(2)]
        public string PrintingSrcSystem { get; set; }

        [DataMember(Name ="printingSrcSystemName")]
        [MaxLength(100)]
        public string PrintingSrcSystemName { get; set; }

        public ProductLabel ToProductLabelModel(int idProduct, int idCustomer, string user)
        {
            ProductLabel label = new ProductLabel()
            {
                IDCustomer = idCustomer,
                IDProduct = idProduct,
                IDProductLabel = this.IDProductLabel,
                IDTargetMarket = this.IDTargetMarket,
                IsDefault = this.IsDefault,
                Label = this.Label,
                LabelName = this.LabelName,
                Level = this.Level,
                PrintingSrcSystem = this.PrintingSrcSystem,
                PrintingSrcSystemName = this.PrintingSrcSystemName
            };

            if(this.IDProductLabel == 0)
            {
                label.UserCreated = user;
                label.TimestampCreated = DateTime.Now;
            }
            
            return label;
        }
    }

    [DataContract]
     public class ApiProductAggregationLevel
    {
        [DataMember(Name = "idCustomerAggregationLevel")]
        public int IDCustomerAggregationLevel { get; set; }

        [DataMember(Name = "name")]
        [Required]
        [MaxLength(20)]        
        public string Name { get; set; }

        [DataMember(Name="type")]
        [Required]
        [MaxLength(20)]
        public string Type { get; set; }

        [DataMember(Name ="serialType")]
        [Required]
        [MaxLength(10)]
        public string SerialType { get; set; }

        [DataMember(Name ="unitCapacity")]
        public int? UnitCapacity { get; set; }

        [DataMember(Name ="subLevel")]
        [Required]
        [MaxLength(20)]
       public string SubLevel { get; set; }

        [DataMember(Name ="subLevelItemType")]
        [Required]
        [MaxLength(20)]
        public string SubLevelItemType { get; set; }

        public CustomerAggregationLevel ToAggregationLevel(int idCustomer, int idProduct, string user)
        {
            CustomerAggregationLevel aggregationLevel = new CustomerAggregationLevel()
            {
                IDCustomer = idCustomer,
                IDProduct = idProduct,
                IDCustomerAggregationLevel = this.IDCustomerAggregationLevel,
                Name = this.Name,
                SerialType = this.SerialType,
                Type = this.Type,
                UnitCapacity = this.UnitCapacity,
                SubLevel = this.SubLevel,
                SubLevelItemType = this.SubLevelItemType                
            };

            if(this.IDCustomerAggregationLevel == 0)
            {
                aggregationLevel.UserCreated = user;
                aggregationLevel.TimestampCreated = DateTime.Now;
            }
            else
            {
                aggregationLevel.UserUpdated = user;
                aggregationLevel.TimestampUpdated = DateTime.Now;
            }

            return aggregationLevel;
        }
    }

    public static class CustomJSON
    {
        public static Stream StrToStream(string str)
        {
            MemoryStream memoryStream = new MemoryStream();
            var bytes = Encoding.UTF8.GetBytes(str);
            memoryStream.Write(bytes, 0, bytes.Count());
            memoryStream.Position = 0;

            return memoryStream;
        }

        public static object Deserialize(Stream stream, Type type)
        {
            DataContractJsonSerializer ser = new DataContractJsonSerializer(type);
            return ser.ReadObject(stream);
        }

        public static object Deserialize(string str, Type type)
        {
            return Deserialize(StrToStream(str), type);
        }

        public static Stream SerializeToStream(object obj)
        {
            DataContractJsonSerializer ser = new DataContractJsonSerializer(obj.GetType());
            MemoryStream stream = new MemoryStream();
            ser.WriteObject(stream, obj);
            stream.Position = 0;

            return stream;
        }

        public static void SerializeToStream(object obj, Stream stream)
        {
            DataContractJsonSerializer ser = new DataContractJsonSerializer(obj.GetType());
            ser.WriteObject(stream, obj);
        }

    }
}
