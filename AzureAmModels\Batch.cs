﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Azure;

namespace AzureAmModels
{
    [Table("Batch")]
    public class Batch : INotifyPropertyChanged
    {
        public Batch()
        {
            NotAggregatedSerials = new HashSet<NotAggregatedSerial>();
            VerificationErrors = new HashSet<VerificationError>();
            BatchEvents = new HashSet<BatchEvent>();
        }

        public Batch(SgXmlEvent.SgXmlEvent file, int idProduct, string lgnName)
        {
            this.BatchID = file.LOT;
            this.ExpiryDate = file.EXP.Value;
            this.ManufacturingDate = file.MNF;
            this.IDProduct = idProduct;
            this.TimestampCreated = DateTime.Now;
            this.UserCreated = lgnName;
            this.BatchEvents = new HashSet<BatchEvent>();
        }


        public Batch(int idProduct, string batchID, DateTime expiryDate, string lgnName)
        { //TODO: Check property values
            this.IDProduct = idProduct;
            this.BatchID = batchID;
            this.ExpiryDate = expiryDate;
            this.UserCreated = lgnName;
            this.TimestampCreated = DateTime.Now;
        }

        public override bool Equals(object obj)
        {
            if (obj == null)
                return false;

            if (obj.GetType() == this.GetType())
            {
                if (((Batch)obj).IDBatch == this.IDBatch)
                    return true;
                else
                    return false;
            }
            else
                return false;
        }


        [Key]
        public int IDBatch { get; set; }

        [Required]
        public int? IDProduct { get; set; }

        [Required]
        [StringLength(20)]
        [RegularExpression(@"^([\u0021;\u0022;\u0025;\u0026;\u0027;\u0028;\u0029;\u002A;\u002B;\u002C;\u002D;\u002E;\u002F;\u003A;\u003B;\u003C;\u003D;\u003E;\u003F;\u005F;a-zA-Z0-9]{1,20})$",
         ErrorMessage = "Batch ID is expected to be alphanumeric values of up to 20 characters, limited to those characters defined in the GS1 General  Specifications.")]
        public string BatchID { get; set; }

        [Required]
        [DisplayName("Expairy date")]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        public DateTime? ExpiryDate { get; set; }

        [DisplayName("Manufacturing date")]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        public DateTime? ManufacturingDate { get; set; }

        public BatchStateType State
        {
            get
            {
                try
                {

                    if (BatchEvents == null || BatchEvents.Count == 0)
                    {
                        return new BatchStateType(BatchStateEnum.NEW, "No events exists");
                    }
                    else
                    {
                        int published = BatchEvents.Where(p => !string.IsNullOrEmpty(p.ReportID)).Count();
                        if (published < BatchEvents.Count)
                            return new BatchStateType(BatchStateEnum.PARTIALLY_REPORTED, $"{published} of {BatchEvents.Count} events are published");
                        else
                            return new BatchStateType(BatchStateEnum.REPORTED, $"All {BatchEvents.Count} events are published");
                    }
                }
                catch (Exception e)
                {
                    return new BatchStateType(BatchStateEnum.ERROR, $"Exception {e.Message}");
                }


            }
        }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        [InverseProperty("Batch")]
        public ICollection<BatchEvent> BatchEvents { get; set; }

        [InverseProperty("Batch")]
        public ICollection<VerificationError> VerificationErrors { get; set; }


        [InverseProperty("Batch")]
        public ICollection<NotAggregatedSerial> NotAggregatedSerials { get; set; }

        public event PropertyChangedEventHandler PropertyChanged;
    }

    public enum BatchStateEnum
    {
        //0 publish events
        NEW,
        //min 1 new event
        PARTIALLY_REPORTED,
        //else
        REPORTED,
        ERROR
    }

    public class BatchStateType
    {
        public BatchStateEnum State { get; set; }

        public string Description { get; set; }

        public BatchStateType()
        {

        }

        public BatchStateType(BatchStateEnum state, string description)
        {
            State = state;
            Description = description;
        }
    }
}
