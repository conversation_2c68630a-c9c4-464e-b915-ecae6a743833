using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AS2Test_master.Models;
using MimeKit;

namespace AS2Test.Features.Services.MimePayload
{
    public interface IMimeMessagePayloadService
    {
        Task<MimeMessage> CreateMultipartMessage(MessageRequest messageRequest);
        Task<MimeMessage> CreateMimeContent(MessageRequest messageRequest, string fileName, TransferEncodings transferEncoding);
    }
}