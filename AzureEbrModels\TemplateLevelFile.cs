﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEbrModels
{
    [Table("TemplateLevelFile")]
    public class TemplateLevelFile
    {
        public TemplateLevelFile()
        {
            BatchTemplateLevelFiles = new HashSet<BatchTemplateLevelFile>();
        }

        [Key]
        public int IDTemplateLevelFile { get; set; }

        public int? IDTemplate { get; set; }

        public int? IDTemplateLevel { get; set; }

        [Required]
        [StringLength(255)]
        public string FileName { get; set; }

        [Required]
        public TemplateLevelFileTypeEnum? Type { get; set; }

        [Required]
        [StringLength(10)]
        public string FileType { get; set; }

        [Required]
        public string Data { get; set; }

        public bool IsActive { get; set; }

        public int? FileSize { get; set; }

        public string FileSizeText 
        {
            get
            {
                if (!FileSize.HasValue)
                    return "N/A";
                else if (FileSize.Value < 1024)
                    return $"{FileSize.Value} B";
                else 
                    return $"{FileSize.Value / 1024} KB";
            }
        }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        [InverseProperty("TemplateLevelFiles")]
        public TemplateLevel TemplateLevel { get; set; }

        [InverseProperty("TemplateLevelFiles")]
        public Template Template { get; set; }

        [InverseProperty("TemplateLevelFile")]
        public ICollection<BatchTemplateLevelFile> BatchTemplateLevelFiles { get; set; }
    }
}
