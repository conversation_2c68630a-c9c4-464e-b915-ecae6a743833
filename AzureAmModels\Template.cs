﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureAmModels
{
    public class OrderTemplate
    {
        public int ID { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Note { get; set; }

        public OrderTemplate(int id, string name, string description, string note)
        {
            ID = id;
            Name = name;
            Description = description;
            Note = note;
        }
    }

    public static class OrderTemplates
    {
        public static List<OrderTemplate> Items
        {
            get
            {
                List<OrderTemplate> items = new List<OrderTemplate>();
                items.Add(new OrderTemplate(5, "Template 5", "01GTIN-21SN13 + 44 symbols crypto", "Used for pharma products with small data matrix code"));
                return items;
            }

        }
    }
}
