﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEbrModels
{
    [Table("BatchSerialization")]
    public class BatchSerialization
    {
        public BatchSerialization()
        {
            BatchSerializationExtends = new HashSet<BatchSerializationExtend>();
            BatchSerializationSerials = new HashSet<BatchSerializationSerial>();
            BatchSerialsRequests = new HashSet<BatchSerialsRequest>();
            LineRequests = new HashSet<LineRequest>();
            BatchTemplateLevelFiles = new HashSet<BatchTemplateLevelFile>();
        }

        [Key]
        public int IDBatchSerialization { get; set; }

        [Required]
        public int IDBatch { get; set; }

        [Required]
        public int? ProductionQuantity { get; set; }

        [Required]
        [StringLength(20)]
        public string SerializationType { get; set; }

        [StringLength(50)]
        public string DefPoolCode { get; set; }

        public bool DefPoolRequestElevation { get; set; }

        public string DefPoolName { get; set; }

        public int? DefPoolSize { get; set; }

        public int? IDTemplateLevel { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        [InverseProperty("BatchSerialization")]
        public Batch Batch { get; set; }

        [InverseProperty("BatchSerialization")]
        public ICollection<BatchSerializationExtend> BatchSerializationExtends { get; set; }

        [InverseProperty("BatchSerialization")]
        public ICollection<BatchSerializationSerial> BatchSerializationSerials { get; set; }

        [InverseProperty("BatchSerialization")]
        public ICollection<BatchSerialsRequest> BatchSerialsRequests { get; set; }

        [InverseProperty("BatchSerialization")]
        public ICollection<LineRequest> LineRequests { get; set; }

        [InverseProperty("BatchSerialization")]
        public ICollection<BatchTemplateLevelFile> BatchTemplateLevelFiles { get; set; }

        public BatchSerialization Clone()
        {
            BatchSerialization newBatch = new BatchSerialization
            {
                IDBatchSerialization = this.IDBatchSerialization,
                IDBatch = this.IDBatch,
                ProductionQuantity = this.ProductionQuantity,
                SerializationType = this.SerializationType,
                DefPoolCode = this.DefPoolCode,
                DefPoolRequestElevation = this.DefPoolRequestElevation,
                TimestampCreated = this.TimestampCreated,
                UserCreated = this.UserCreated,
                BatchSerializationExtends = this.BatchSerializationExtends
                    .Select(be => new BatchSerializationExtend
                    {
                        IDBatchSerializationExtend = be.IDBatchSerializationExtend,
                        IDBatchSerialization = be.IDBatchSerialization,
                        Name = be.Name,
                        GS1AICode = be.GS1AICode,
                        StringValue = be.StringValue,
                        Regex = be.Regex,
                        IsRequired = be.IsRequired,
                        IsSelectValue = be.IsSelectValue,
                        Type = be.Type,
                        IDExtendProperty = be.IDExtendProperty
                    }).ToList()
            };

            return newBatch;
        }
    }
}
