﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureAmModels
{
    [Table("BatchEventAggregation")]
    public class BatchEventAggregation
    {
        public BatchEventAggregation()
        {
            BatchEventAggregationItems = new HashSet<BatchEventAggregationItem>();
        }

        [Key]
        public int IDBatchEventAggregation { get; set; }

        [Required]
        public int IDBatchEvent { get; set; }

        [StringLength(50)]
        public string LevelType { get; set; }

        [StringLength(50)]
        public string LevelName { get; set; }

        public int? LevelCount { get; set; }

        public int? UnitCapacity { get; set; }

        [StringLength(50)]
        public string UnitName { get; set; }

        [StringLength(50)]
        public string SerialType { get; set; }

        [StringLength(50)]
        public string SerialItemType { get; set; }

        [StringLength(50)]
        public string SerialLevel { get; set; }

        [StringLength(50)]
        public string SerialCode { get; set; }

        public int? SerialItemCount { get; set; }

        public DateTime? TimestampProduced { get; set; }

        [StringLength(50)]
        public string SerialLineName { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        [InverseProperty("BatchEventAggregations")]
        public BatchEvent BatchEvent { get; set; }

        [InverseProperty("BatchEventAggregation")]
        public ICollection<BatchEventAggregationItem> BatchEventAggregationItems { get; set; }

        public override bool Equals(object obj)
        {
            if (obj == null)
                return false;

            if (obj.GetType() == this.GetType())
            {
                if (((BatchEventAggregation)obj).IDBatchEventAggregation == this.IDBatchEventAggregation)
                    return true;
                else
                    return false;
            }
            else
                return false;
        }

    }
}
