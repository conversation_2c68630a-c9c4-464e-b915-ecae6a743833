﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEbrModels
{
    [Table("BatchError")]
    public class BatchError
    {
        [Key]
        public int IDBatchError { get; set; }

        [Required]
        public int IDBatch { get; set; }

        public bool IsCrirical { get; set; }


        [StringLength(100)]
        public string ItemLevel { get; set; }

        [StringLength(100)]
        public string ItemValue { get; set; }

        [StringLength(100)]
        public string UnitLevel { get; set; }

        [StringLength(100)]
        public string UnitValue { get; set; }

        [StringLength(1000)]
        public string Message { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [InverseProperty("BatchErrors")]
        public Batch Batch { get; set; }
    }
}
