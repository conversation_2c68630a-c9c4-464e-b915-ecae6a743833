using System.Text.Json.Serialization;
using AS2App.Models.ConfigModels;
using AS2Test.ErrorHandling;
using AS2Test.Extensions;
using AS2Test.Helpers;
using AS2Test_master.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;


var builder = WebApplication.CreateBuilder(args);
var config = builder.Configuration;


builder.Services.AddAuthenticationConfigurations(config);
builder.Services.AddApplicationServices(config); ;
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwagger(config);
builder.Services.AddHttpContextAccessor();

builder.Services.AddControllers(opt =>
{
    opt.Filters.Add<ModelValidationFilter>();
})
    .AddJsonOptions(options =>
    {
        options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());

    })

    .ConfigureApiBehaviorOptions(opt =>
    {
        opt.SuppressModelStateInvalidFilter = true;
    });

builder.Services.AddHttpContextAccessor();
builder.Services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();

builder.Services.AddDbContext<AzureAs2ProxyModels.DBModel>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("AzureAS2Proxy")));

builder.Services.AddExceptionHandler<ExceptionHandler>();
builder.Services.AddProblemDetails();

var app = builder.Build();

app.UseExceptionHandler();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    var azureSettings = config.GetSection("AzureAd").Get<AzureModel>();

    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("./v1/swagger.json", "SATT AS2 Api v1");
        c.OAuthClientId(azureSettings.ClientId);
        c.OAuthClientSecret(azureSettings.ClientSecret);
        c.OAuthUseBasicAuthenticationWithAccessCodeGrant();
    });
}

app.UseAuthentication();
app.UseRouting();
app.UseAuthorization();

app.MapControllers();

app.Run();


