#!/bin/bash

# Variables (edit these paths as needed)
INPUT_FILE="test.xml"
CERT_FILE="certificate.crt"
KEY_FILE="crt.pem"
OUTPUT_FILE="signed_output.der"

# Check if input files exist
if [[ ! -f "$INPUT_FILE" ]]; then
  echo "Error: Input file '$INPUT_FILE' not found."
  exit 1
fi

if [[ ! -f "$CERT_FILE" ]]; then
  echo "Error: Certificate file '$CERT_FILE' not found."
  exit 1
fi

if [[ ! -f "$KEY_FILE" ]]; then
  echo "Error: Key file '$KEY_FILE' not found."
  exit 1
fi

# Run the OpenSSL SMIME sign command
openssl smime -sign \
  -in "$INPUT_FILE" \
  -signer "$CERT_FILE" \
  -inkey "$KEY_FILE" \
  -binary \
  -nodetach \
  -outform DER \
  -out "$OUTPUT_FILE"

# Check if OpenSSL succeeded
if [[ $? -eq 0 ]]; then
  echo "Signing successful. Output written to '$OUTPUT_FILE'."
else
  echo "Error: OpenSSL signing failed."
  exit 1
fi
