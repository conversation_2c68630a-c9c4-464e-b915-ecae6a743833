using System.Net.Mime;
using System.Security.Cryptography;
using System.Text;
using MimeKit;

namespace AS2Test.Extensions.Helpers
{
    /// <summary>
    /// Provides extension methods for string manipulation.
    /// </summary>
    public static class ExtensionMethods
    {

        public static Tuple<TransferEncoding, ContentEncoding> TransferEncodingConvert(string transferEncoding)
        {
            var encoding = new TransferEncoding();
            var mimeKitEncoding = new ContentEncoding();

            if (transferEncoding == "Base64")
            {
                encoding = TransferEncoding.Base64;
                mimeKitEncoding = ContentEncoding.Base64;
            }
            if (transferEncoding == "SevenBit")
            {
                encoding = TransferEncoding.SevenBit;
                mimeKitEncoding = ContentEncoding.SevenBit;
            }
            if (transferEncoding == "EightBit")
            {
                encoding = TransferEncoding.EightBit;
                mimeKitEncoding = ContentEncoding.EightBit;
            }
            return new Tuple<TransferEncoding, ContentEncoding>(encoding, mimeKitEncoding);
        }

        public static class EncryptionOids
        {
            public static readonly Dictionary<string, string> OidMap = new()
            {
                { "DES3", "1.2.840.113549.3.7" },            // TripleDES
                { "AES128", "2.16.840.*********.1.2" },       // AES-128-CBC
                { "AES192", "2.16.840.*********.1.22" },      // AES-192-CBC
                { "AES256", "2.16.840.*********.1.42" },      // AES-256-CBC
                { "RC2", "1.2.840.113549.3.2" }               // RC2-CBC
            };
        }

        #region unused helper methods for future implementation

        /// <summary>
        /// Decodes a Base64 encoded string.
        /// </summary>
        /// <param name="value">The Base64 encoded string to decode.</param>
        /// <returns>The decoded string.</returns>
        public static string DecodeBase64(this string value)
        {
            var valueBytes = System.Convert.FromBase64String(value);
            return Encoding.UTF8.GetString(valueBytes);
        }
        /// <summary>
        /// Encodes a string value to Base64.
        /// </summary>
        /// <param name="value">The string value to encode.</param>
        /// <returns>The Base64 encoded string.</returns>
        public static string EncodeBase64(this string value)
        {
            var valueBytes = Encoding.UTF8.GetBytes(value);
            return Convert.ToBase64String(valueBytes);
        }


        /// <summary>
        /// Converts a string to a base64 string with padding.
        /// </summary>
        /// <param name="value">The string value to convert.</param>
        /// <returns>The base64 string with padding.</returns>
        public static string Base64PaddingConverter(this string base64Value)
        {
            var mimeString = base64Value.ToString();

            var mimePayloadStringcontent = base64Value.PadRight(mimeString.Length + (4 - mimeString.Length % 4) % 4, '=');
            if (mimePayloadStringcontent.Length % 4 != 0)
                mimePayloadStringcontent += new String('=', 4 - mimeString.Length % 4);

            switch (base64Value.Length % 4)
            {
                case 2:
                    base64Value += "==";
                    break;
                case 3:
                    base64Value += "=";
                    break;
            }
            /*
            var requiredPadding = 4 - mimeString.Length % 4;
            if (requiredPadding > 0)
            {
                mimeString += new string(Enumerable.Repeat('=', requiredPadding).ToArray());
            }
            var result = Convert.FromBase64String(mimeString);
            */
            return base64Value;
        }

        /// <summary>
        /// Computes the hash sum of a string using the specified hash algorithm.
        /// </summary>
        /// <param name="input">The input string.</param>
        /// <param name="hashAlgorithm">The hash algorithm to use.</param>
        /// <returns>The hexadecimal string representation of the computed hash sum.</returns>
        public static string GetHashSum(this string input, HashAlgorithm hashAlgorithm)
        {
            try
            {
                // Convert the input string to a byte array and compute the hash.
                byte[] data = hashAlgorithm.ComputeHash(Encoding.UTF8.GetBytes(input));

                // Create a new Stringbuilder to collect the bytes
                // and create a string.
                var sBuilder = new StringBuilder();

                // Loop through each byte of the hashed data
                // and format each one as a hexadecimal string.
                for (int i = 0; i < data.Length; i++)
                {
                    sBuilder.Append(data[i].ToString("x2"));
                }
                // Return the hexadecimal string.
                return sBuilder.ToString();
            }
            catch (ArgumentNullException ex)
            {
                throw new("GetHashSum exception: " + ex.Message.ToString());
            }
            catch (ObjectDisposedException ex)
            {
                throw new("GetHashSum exception: " + ex.Message.ToString());
            }
            catch (FormatException ex)
            {
                throw new("GetHashSum exception: " + ex.Message.ToString());
            }
        }

        #endregion 
    }
}