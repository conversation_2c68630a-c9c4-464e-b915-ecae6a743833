﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEbrModels
{
    [Table("BatchSerialsRequest")]
    public class BatchSerialsRequest
    {
        [Key]
        public int IDBatchSerialsRequest { get; set; }

        public int? IDBatchSerialization { get; set; }

        public int? IDBatchAggregation { get; set; }

        [Required]
        public string RequestID { get; set; }

        [Required]
        public int RequestedQuantity { get; set; }

        public string RequestedPoolCode { get; set; }

        public string RequestedAggregationLevel { get; set; }

        public string RequestedSerialType { get; set; }

        [Required]
        public int AllocatedQuantity { get; set; }

        public string AllocatedPoolCode { get; set; }

        [Required]
        public string ResponseID { get; set; }

        public string ElevatedRequestID { get; set; }

        [Required]
        public bool IsRequestValid { get; set; }

        [Required]
        public bool IsOrder { get; set; }

        [StringLength(200)]
        public string Partner { get; set; }

        [StringLength(4000)]
        public string ResultText { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [InverseProperty("BatchSerialsRequests")]
        public BatchSerialization BatchSerialization { get; set; }

        [InverseProperty("BatchSerialsRequests")]
        public BatchAggregation BatchAggregation { get; set; }
    }
}
 


	