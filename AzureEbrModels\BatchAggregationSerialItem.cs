﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEbrModels
{
    [Table("BatchAggregationSerialItem")]
    public class BatchAggregationSerialItem
    {
        [Key]
        public long IDBatchAggregationSerialItem { get; set; }

        [Required]
        public long IDBatchAggregationSerial { get; set; }

        [Required]
        public string SerialNumber { get; set; }

        [Required]
        public bool IsSGTIN { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [InverseProperty("BatchAggregationSerialItems")]
        public BatchAggregationSerial BatchAggregationSerial { get; set; }
    }
}

