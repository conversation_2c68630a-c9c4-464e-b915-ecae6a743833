﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\EntityFramework.6.4.4\build\EntityFramework.props" Condition="Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{0903F02D-9450-4A70-8AB9-4732F3E9ABDD}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>AzureCmdModelsStdEdmx</RootNamespace>
    <AssemblyName>AzureCmdModelsStdEdmx</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.4.4\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.4.4\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AdditionalCodeType.cs">
      <DependentUpon>DBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="AuditTrail.cs">
      <DependentUpon>DBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="AuditTrailItem.cs">
      <DependentUpon>DBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="BusinessSegment.cs">
      <DependentUpon>DBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Class1.cs" />
    <Compile Include="CommunicationChannel.cs">
      <DependentUpon>DBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Contract.cs">
      <DependentUpon>DBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="ContractStatu.cs">
      <DependentUpon>DBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="ContractType.cs">
      <DependentUpon>DBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Country.cs">
      <DependentUpon>DBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Customer.cs">
      <DependentUpon>DBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="CustomerAggregationLevel.cs">
      <DependentUpon>DBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="CustomerContract.cs">
      <DependentUpon>DBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="CustomerLabelDataSource.cs">
      <DependentUpon>DBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="CustomerPartner.cs">
      <DependentUpon>DBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="CustomerPartnerParameter.cs">
      <DependentUpon>DBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="CustomerService.cs">
      <DependentUpon>DBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="DBModel.Context.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DBModel.Context.tt</DependentUpon>
    </Compile>
    <Compile Include="DBModel.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="DBModel.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DBModel.edmx</DependentUpon>
    </Compile>
    <Compile Include="InternalCodeType.cs">
      <DependentUpon>DBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="LabelTemplate.cs">
      <DependentUpon>DBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="MahType.cs">
      <DependentUpon>DBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Manufacturer.cs">
      <DependentUpon>DBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Product.cs">
      <DependentUpon>DBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="ProductAdditionalCode.cs">
      <DependentUpon>DBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="ProductCodeType.cs">
      <DependentUpon>DBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="ProductLabel.cs">
      <DependentUpon>DBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="SerializationType.cs">
      <DependentUpon>DBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="SerialNumberSourceType.cs">
      <DependentUpon>DBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Service.cs">
      <DependentUpon>DBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="ServiceContract.cs">
      <DependentUpon>DBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="ServiceEnvironment.cs">
      <DependentUpon>DBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="ServiceRole.cs">
      <DependentUpon>DBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="ServiceUserLogin.cs">
      <DependentUpon>DBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="TargetMarket.cs">
      <DependentUpon>DBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="UserLogin.cs">
      <DependentUpon>DBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="UserLoginServiceRole.cs">
      <DependentUpon>DBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="UserPrinterSetting.cs">
      <DependentUpon>DBModel.tt</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
    <EntityDeploy Include="DBModel.edmx">
      <Generator>EntityModelCodeGenerator</Generator>
      <LastGenOutput>DBModel.Designer.cs</LastGenOutput>
    </EntityDeploy>
    <None Include="DBModel.edmx.diagram">
      <DependentUpon>DBModel.edmx</DependentUpon>
    </None>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="DBModel.Context.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>DBModel.edmx</DependentUpon>
      <LastGenOutput>DBModel.Context.cs</LastGenOutput>
    </Content>
    <Content Include="DBModel.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>DBModel.edmx</DependentUpon>
      <LastGenOutput>DBModel.cs</LastGenOutput>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.4.4\build\EntityFramework.props'))" />
    <Error Condition="!Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.4.4\build\EntityFramework.targets'))" />
  </Target>
  <Import Project="..\packages\EntityFramework.6.4.4\build\EntityFramework.targets" Condition="Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.targets')" />
</Project>