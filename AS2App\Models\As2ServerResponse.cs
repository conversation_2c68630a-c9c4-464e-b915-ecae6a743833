namespace AS2Test.Models
{
    /// <summary>
    /// Represents the response from an AS2 server.
    /// </summary>
    /// <typeparam name="T">The type of the MIME message.</typeparam>
    public class As2ServerResponse<T>
    {
        /// <summary>
        /// Gets or sets a value indicating whether the message integrity check is OK.
        /// </summary>
        

        /// <summary>
        /// Gets or sets the MIME message.
        /// </summary>
       
        public string StatusCode { get; set; }
        public string ReasonPhrase { get; set; }
        public string ErrorMessage { get; set; }
        public bool IsSuccessStatusCode { get; set; } 
        public T Message { get; set; } 
    }

    
}