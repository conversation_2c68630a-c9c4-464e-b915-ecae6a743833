//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace AzureCmdModelsStdEdmx
{
    using System;
    using System.Collections.Generic;
    
    public partial class CustomerAggregationLevel
    {
        public int IDCustomerAggregationLevel { get; set; }
        public int IDCustomer { get; set; }
        public Nullable<int> IDProduct { get; set; }
        public string Name { get; set; }
        public string Type { get; set; }
        public string SerialType { get; set; }
        public Nullable<int> UnitCapacity { get; set; }
        public string SubLevel { get; set; }
        public string SubLevelItemType { get; set; }
        public System.DateTime TimestampCreated { get; set; }
        public string UserCreated { get; set; }
        public Nullable<System.DateTime> TimestampUpdated { get; set; }
        public string UserUpdated { get; set; }
    
        public virtual Customer Customer { get; set; }
        public virtual Product Product { get; set; }
    }
}
