﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SGAuditTrail;

namespace AzureCmdModels
{
    [Table("AdditionalCodeType")]
    [Audit(DisplayName = "Additional Code Type")]
    public class AdditionalCodeType
    {        
        public AdditionalCodeType()
        {
            ProductAdditionalCodes = new HashSet<ProductAdditionalCode>();
        }

        [Required]
        [StringLength(255)]
        [Audit(DisplayName = "Name")]
        [Key]
        public string Name { get; set; }

        [InverseProperty("AdditionalCodeType")]
        public ICollection<ProductAdditionalCode> ProductAdditionalCodes { get; set; }

    }
}
