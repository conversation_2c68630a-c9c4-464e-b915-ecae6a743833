using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.Identity.Abstractions;
using Microsoft.Identity.Web;
using Microsoft.Identity.Web.Resource;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace AS2Api
{
    public class Program
    {
        public static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);

            var azureSettings = builder.Configuration.GetSection("AzureAd").Get<Data.AzureModel>();

            builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
                .AddMicrosoftIdentityWebApi(builder.Configuration.GetSection("AzureAd"));

            builder.Services.Configure<JwtBearerOptions>(JwtBearerDefaults.AuthenticationScheme, options =>
            {
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    ValidateIssuer = true,
                    ValidIssuer = $"https://sts.windows.net/{azureSettings.TenantId}/",
                    ValidateAudience = false,
                    //ValidAudience = $"{azureSettings.ClientId}",
                    //ValidateLifetime = true,
                    //ClockSkew = TimeSpan.Zero
                };

                options.Events = new JwtBearerEvents();

                options.Events.OnTokenValidated = async context =>
                {
                    await Task.FromResult(0);
                };
                options.Events.OnAuthenticationFailed = async context =>
                {
                    await Task.CompletedTask;
                };
                options.Events.OnMessageReceived = async context =>
                {
                    await Task.CompletedTask;
                };
            });

            builder.Services.AddControllers();

            builder.Services.AddEndpointsApiExplorer();
            builder.Services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "AS2 Proxy Api", Version = "v1" });
                c.AddSecurityDefinition("oauth2", new OpenApiSecurityScheme
                {
                    Type = SecuritySchemeType.OAuth2,
                    Flows = new OpenApiOAuthFlows()
                    {
                        Implicit = new OpenApiOAuthFlow()
                        {
                            AuthorizationUrl = new Uri($"https://login.microsoftonline.com/{azureSettings.TenantId}/oauth2/v2.0/authorize"),
                            TokenUrl = new Uri($"https://login.microsoftonline.com/{azureSettings.TenantId}/oauth2/v2.0/token"),
                            Scopes = new Dictionary<string, string>
                            {
                                {$"api://{azureSettings.ClientId}/access_as_user/EbrAccessAsUser", "EbrAccessAsUser"}
                            }
                        }
                    }
                });
                c.AddSecurityRequirement(new OpenApiSecurityRequirement()
                {
                    {
                        new OpenApiSecurityScheme {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "oauth2"
                            },
                            Scheme = "oauth2",
                            Name = "oauth2",
                            In = ParameterLocation.Header
                        },
                        new List<string>()
                    }
                });
            });

            builder.Services.AddHttpContextAccessor();
            builder.Services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();

            builder.Services.AddScoped<GlobalVariables>();
            builder.Services.AddSignalR(configure =>
            {
                configure.EnableDetailedErrors = true;
            });

            builder.Services.AddDbContext<AzureCmdModels.DBModel>(options =>
               options.UseSqlServer(builder.Configuration.GetConnectionString("AzureCmd")));

            var app = builder.Build();

            // Configure the HTTP request pipeline.
            if (app.Environment.IsDevelopment())
            {
                app.UseSwagger();
                app.UseSwaggerUI();
            }

            app.UseHttpsRedirection();

            app.UseAuthorization();


            app.MapControllers();

            app.Run();
        }
    }
}
