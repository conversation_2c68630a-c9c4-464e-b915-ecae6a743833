
namespace AS2Test.Models
{
    public class MdnVerificationResult
    {
        public MdnType MdnType { get; set; }
        public bool IsDecrypted { get; set; }
        public bool IsSignatureValid { get; set; }
        public string OriginalMessageId { get; set; }
        public string Disposition { get; set; }
        public string DispositionModifier { get; set; }
        public string MdnText { get; set; }
        public string MDNTextPart { get; set; }
        public string Error { get; set; }
        public bool IsMicValid { get; set; } 

    }
    public enum MdnType
    {
        Plain,
        MultipartSigned,
        TextPart,
        ApplicationPkcs7MimeSigned,
        ApplicationPkcs7MimeEnveloped,
    }
}