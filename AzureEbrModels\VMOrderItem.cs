﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEbrModels
{
    [Table("VMOrderItem")]
    public partial class VMOrderItem
    {
        [Key]
        [DisplayName("ID")]
        public int IDVMOrderItem { get; set; }

        public int IDVMOrder { get; set; }

        public int? IDVMOrderRef { get; set; }

        [StringLength(1000)]
        public string Barcode { get; set; }

        [StringLength(20, ErrorMessage = "The field Serial Number must be a string with a maximum length of 20.")]
        [DisplayName("Serial Number")]
        public string SerialNumber { get; set; }

        [StringLength(50)]
        public string State { get; set; }

        [Required]
        [DisplayName("Timestamp created")]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}")]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        [DisplayName("User Created")]
        public string UserCreated { get; set; }

        [DisplayName("Timestamp Updated")]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}")]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        [DisplayName("User Updated")]
        public string UserUpdated { get; set; }

        [InverseProperty("Items")]
        public VMOrder Order { get; set; }
    }

    public static class PackTypeStatusConst
    {
        public const string ACTIVE = "ACTIVE";
        public const string DESTROYED = "DESTROYED";
        public const string FREESAMPLE = "FREESAMPLE";
        public const string CHECKEDOUT = "CHECKEDOUT";
        public const string EXPORTED = "EXPORTED";
        public const string LOCKED = "LOCKED";
        public const string SAMPLE = "SAMPLE";
        public const string STOLEN = "STOLEN";
        public const string SUPPLIED = "SUPPLIED";
        public const string RECALLED = "RECALLED";
        public const string WITHDRAWN = "WITHDRAWN";
        public const string EXPIRED = "EXPIRED";

    }
}
