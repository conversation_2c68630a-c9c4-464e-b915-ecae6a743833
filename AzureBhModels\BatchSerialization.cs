﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureBhModels
{
    [Table("BatchSerialization")]
    public class BatchSerialization
    {
        public BatchSerialization()
        {
        }

        [Key]
        public long IDBatchSerialization { get; set; }

        [Required]
        public int? IDBatch { get; set; }

        [Required]
        [StringLength(100)]
        public string SerialNumber { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [InverseProperty("BatchSerializations")]
        public Batch Batch { get; set; }

    }
}
