﻿<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
  <!-- EF Runtime content -->
  <edmx:Runtime>
    <!-- SSDL content -->
    <edmx:StorageModels>
      <Schema Namespace="CloudMasterData.Store" Provider="System.Data.SqlClient" ProviderManifestToken="2012.Azure" Alias="Self" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
        <EntityType Name="AdditionalCodeType">
          <Key>
            <PropertyRef Name="Name" />
          </Key>
          <Property Name="Name" Type="nvarchar" MaxLength="50" Nullable="false" />
        </EntityType>
        <EntityType Name="AuditTrail">
          <Key>
            <PropertyRef Name="IDAuditTrail" />
          </Key>
          <Property Name="IDAuditTrail" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Action" Type="nvarchar" MaxLength="100" />
          <Property Name="IDTable" Type="nvarchar" MaxLength="50" />
          <Property Name="TableName" Type="nvarchar" MaxLength="100" />
          <Property Name="Timestamp" Type="datetime" />
          <Property Name="User" Type="nvarchar" MaxLength="128" />
          <Property Name="DisplayName" Type="nvarchar" MaxLength="100" />
          <Property Name="IDTableDetail" Type="nvarchar" MaxLength="50" />
        </EntityType>
        <EntityType Name="AuditTrailItem">
          <Key>
            <PropertyRef Name="IDAuditTrailItem" />
          </Key>
          <Property Name="IDAuditTrailItem" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="IDAuditTrail" Type="int" Nullable="false" />
          <Property Name="FieldName" Type="nvarchar" MaxLength="100" />
          <Property Name="OldValue" Type="nvarchar(max)" />
          <Property Name="NewValue" Type="nvarchar(max)" />
          <Property Name="DisplayName" Type="nvarchar" MaxLength="100" />
        </EntityType>
        <EntityType Name="BusinessSegment">
          <Key>
            <PropertyRef Name="IDBusinessSegment" />
          </Key>
          <Property Name="IDBusinessSegment" Type="int" Nullable="false" />
          <Property Name="Segment" Type="nvarchar" MaxLength="50" />
        </EntityType>
        <EntityType Name="CommunicationChannel">
          <Key>
            <PropertyRef Name="Type" />
          </Key>
          <Property Name="Type" Type="nvarchar" MaxLength="50" Nullable="false" />
        </EntityType>
        <EntityType Name="Contract">
          <Key>
            <PropertyRef Name="IDContract" />
          </Key>
          <Property Name="IDContract" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="IDCustomer" Type="int" Nullable="false" />
          <Property Name="Name" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="StartDate" Type="date" Nullable="false" />
          <Property Name="EndDate" Type="date" Nullable="false" />
          <Property Name="IDContractStatus" Type="int" Nullable="false" />
          <Property Name="IDContractType" Type="int" Nullable="false" />
          <Property Name="MaxUsers" Type="int" Nullable="false" />
          <Property Name="ExpirationWarningDays" Type="int" Nullable="false" />
          <Property Name="SerialNumberCount" Type="int" Nullable="false" />
          <Property Name="ContractNumber" Type="nvarchar" MaxLength="100" />
          <Property Name="TimestampCreated" Type="datetime" Nullable="false" />
          <Property Name="UserCreated" Type="nvarchar" MaxLength="128" Nullable="false" />
          <Property Name="TimestampUpdated" Type="datetime" />
          <Property Name="UserUpdated" Type="nvarchar" MaxLength="128" />
        </EntityType>
        <EntityType Name="ContractStatus">
          <Key>
            <PropertyRef Name="IDContractStatus" />
          </Key>
          <Property Name="IDContractStatus" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Name" Type="nvarchar" MaxLength="100" Nullable="false" />
        </EntityType>
        <EntityType Name="ContractType">
          <Key>
            <PropertyRef Name="IDContractType" />
          </Key>
          <Property Name="IDContractType" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Name" Type="nvarchar" MaxLength="128" Nullable="false" />
        </EntityType>
        <EntityType Name="Country">
          <Key>
            <PropertyRef Name="Code" />
          </Key>
          <Property Name="Code" Type="char" MaxLength="2" Nullable="false" />
          <Property Name="Name" Type="nvarchar" MaxLength="50" Nullable="false" />
          <Property Name="NameSecLng" Type="nvarchar" MaxLength="50" />
          <Property Name="GS1RNCode" Type="int" />
        </EntityType>
        <EntityType Name="Customer">
          <Key>
            <PropertyRef Name="IDCustomer" />
          </Key>
          <Property Name="IDCustomer" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Name" Type="nvarchar" MaxLength="255" />
          <Property Name="TimestampCreated" Type="datetime" Nullable="false" />
          <Property Name="UserCreated" Type="nvarchar" MaxLength="128" Nullable="false" />
          <Property Name="TimestampUpdated" Type="datetime" />
          <Property Name="UserUpdated" Type="nvarchar" MaxLength="128" />
          <Property Name="GS1CompanyPrefix" Type="varchar" MaxLength="100" />
          <Property Name="SGLN" Type="varchar" MaxLength="20" />
          <Property Name="GLN" Type="varchar" MaxLength="13" />
          <Property Name="CustomerStreet" Type="nvarchar" MaxLength="255" />
          <Property Name="CustomerStreet2" Type="nvarchar" MaxLength="255" />
          <Property Name="CustomerCity" Type="nvarchar" MaxLength="255" />
          <Property Name="CustomerPostCode" Type="nvarchar" MaxLength="30" />
          <Property Name="CustomerCountryCode" Type="char" MaxLength="2" />
          <Property Name="Code" Type="nvarchar" MaxLength="50" />
          <Property Name="IDBusinessSegment" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="CustomerAggregationLevel">
          <Key>
            <PropertyRef Name="IDCustomerAggregationLevel" />
          </Key>
          <Property Name="IDCustomerAggregationLevel" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="IDCustomer" Type="int" Nullable="false" />
          <Property Name="IDProduct" Type="int" />
          <Property Name="Name" Type="nvarchar" MaxLength="20" Nullable="false" />
          <Property Name="Type" Type="nvarchar" MaxLength="20" Nullable="false" />
          <Property Name="SerialType" Type="nvarchar" MaxLength="10" Nullable="false" />
          <Property Name="UnitCapacity" Type="int" />
          <Property Name="SubLevel" Type="nvarchar" MaxLength="20" Nullable="false" />
          <Property Name="SubLevelItemType" Type="nvarchar" MaxLength="20" Nullable="false" />
          <Property Name="TimestampCreated" Type="datetime" Nullable="false" />
          <Property Name="UserCreated" Type="nvarchar" MaxLength="128" Nullable="false" />
          <Property Name="TimestampUpdated" Type="datetime" />
          <Property Name="UserUpdated" Type="nvarchar" MaxLength="128" />
        </EntityType>
        <EntityType Name="CustomerContract">
          <Key>
            <PropertyRef Name="IDCustomerContract" />
          </Key>
          <Property Name="IDCustomerContract" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="IDCustomer" Type="int" Nullable="false" />
          <Property Name="Name" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="StartDate" Type="date" Nullable="false" />
          <Property Name="EndDate" Type="date" />
          <Property Name="IsTrial" Type="bit" Nullable="false" />
          <Property Name="TrialEndDate" Type="datetime" />
          <Property Name="IsContract" Type="bit" Nullable="false" />
          <Property Name="ContractTerm" Type="date" />
          <Property Name="ContractData" Type="nvarchar" MaxLength="100" />
          <Property Name="ContractNotifyEndDays" Type="int" Nullable="false" />
          <Property Name="IsPayed" Type="bit" Nullable="false" />
          <Property Name="PaymentTerm" Type="date" />
          <Property Name="PaymentNotifyDays" Type="int" Nullable="false" />
          <Property Name="InvoiceData" Type="nvarchar" MaxLength="100" />
          <Property Name="IsCanceled" Type="bit" Nullable="false" />
          <Property Name="CancelReason" Type="nvarchar" MaxLength="200" />
          <Property Name="TimestampCreated" Type="datetime" Nullable="false" />
          <Property Name="UserCreated" Type="nvarchar" MaxLength="128" Nullable="false" />
          <Property Name="TimestampUpdated" Type="datetime" />
          <Property Name="UserUpdated" Type="nvarchar" MaxLength="128" />
        </EntityType>
        <EntityType Name="CustomerLabelDataSource">
          <Key>
            <PropertyRef Name="IDCustomerDataSource" />
          </Key>
          <Property Name="IDCustomerDataSource" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="IDCustomer" Type="int" Nullable="false" />
          <Property Name="Name" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="DataSource" Type="nvarchar(max)" />
          <Property Name="TimestampCreated" Type="datetime" Nullable="false" />
          <Property Name="UserCreated" Type="nvarchar" MaxLength="128" Nullable="false" />
          <Property Name="TimestampUpdated" Type="datetime" />
          <Property Name="UserUpdated" Type="nvarchar" MaxLength="128" />
          <Property Name="SrcSystemName" Type="nvarchar" MaxLength="200" />
          <Property Name="SrcSystem" Type="nvarchar" MaxLength="2" />
          <Property Name="Unit" Type="nvarchar" MaxLength="100" />
          <Property Name="SerialType" Type="nvarchar" MaxLength="20" />
          <Property Name="Item" Type="nvarchar" MaxLength="100" />
          <Property Name="ItemSerialType" Type="nvarchar" MaxLength="20" />
        </EntityType>
        <EntityType Name="CustomerPartner">
          <Key>
            <PropertyRef Name="IDCustomerPartner" />
          </Key>
          <Property Name="IDCustomerPartner" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="IDCustomer" Type="int" Nullable="false" />
          <Property Name="Name" Type="nvarchar" MaxLength="255" Nullable="false" />
          <Property Name="SystemName" Type="nvarchar" MaxLength="255" Nullable="false" />
          <Property Name="SNXRequest" Type="nvarchar" MaxLength="50" />
          <Property Name="SSCCRequest" Type="nvarchar" MaxLength="50" />
          <Property Name="EBRImport" Type="nvarchar" MaxLength="50" />
          <Property Name="BatchReport" Type="nvarchar" MaxLength="50" />
          <Property Name="ShipmentReport" Type="nvarchar" MaxLength="50" />
          <Property Name="IsActive" Type="bit" Nullable="false" />
        </EntityType>
        <EntityType Name="CustomerPartnerParameter">
          <Key>
            <PropertyRef Name="IDCustomerPartnerParameter" />
          </Key>
          <Property Name="IDCustomerPartnerParameter" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="IDCustomerPartner" Type="int" Nullable="false" />
          <Property Name="Name" Type="nvarchar" MaxLength="255" Nullable="false" />
          <Property Name="Value" Type="nvarchar" MaxLength="255" Nullable="false" />
          <Property Name="TimestampCreated" Type="datetime" Nullable="false" />
          <Property Name="UserCreated" Type="nvarchar" MaxLength="128" Nullable="false" />
        </EntityType>
        <EntityType Name="CustomerService">
          <Key>
            <PropertyRef Name="IDCustomerService" />
          </Key>
          <Property Name="IDCustomerService" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="IDCustomer" Type="int" Nullable="false" />
          <Property Name="IDService" Type="int" Nullable="false" />
          <Property Name="IsDatabaseCustomerSeparation" Type="bit" Nullable="false" />
          <Property Name="DatabaseConnectionString" Type="nvarchar(max)" />
        </EntityType>
        <EntityType Name="InternalCodeType">
          <Key>
            <PropertyRef Name="Name" />
          </Key>
          <Property Name="Name" Type="nvarchar" MaxLength="50" Nullable="false" />
        </EntityType>
        <EntityType Name="LabelTemplate">
          <Key>
            <PropertyRef Name="IDLabelTemplate" />
          </Key>
          <Property Name="IDLabelTemplate" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="IDCustomer" Type="int" Nullable="false" />
          <Property Name="Unit" Type="nvarchar" MaxLength="50" />
          <Property Name="Label" Type="nvarchar(max)" Nullable="false" />
          <Property Name="LabelName" Type="nvarchar" MaxLength="255" Nullable="false" />
          <Property Name="TimestampCreated" Type="datetime" Nullable="false" />
          <Property Name="UserCreated" Type="nvarchar" MaxLength="128" Nullable="false" />
        </EntityType>
        <EntityType Name="MahType">
          <Key>
            <PropertyRef Name="IDMahType" />
          </Key>
          <Property Name="IDMahType" Type="int" Nullable="false" />
          <Property Name="Type" Type="nvarchar" MaxLength="50" />
        </EntityType>
        <EntityType Name="Manufacturer">
          <Key>
            <PropertyRef Name="IDMah" />
          </Key>
          <Property Name="IDMah" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="IDCustomer" Type="int" Nullable="false" />
          <Property Name="IDMahType" Type="int" />
          <Property Name="MahID" Type="nvarchar" MaxLength="50" />
          <Property Name="MahName" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="MahStreet" Type="nvarchar" MaxLength="255" />
          <Property Name="MahStreet2" Type="nvarchar" MaxLength="255" />
          <Property Name="MahCity" Type="nvarchar" MaxLength="255" />
          <Property Name="MahPostCode" Type="nvarchar" MaxLength="30" />
          <Property Name="MahCountryCode" Type="char" MaxLength="2" />
          <Property Name="TimeStampCreated" Type="datetime" Nullable="false" />
          <Property Name="UserCreated" Type="nvarchar" MaxLength="128" Nullable="false" />
          <Property Name="TimeStampUpdated" Type="datetime" />
          <Property Name="UserUpdated" Type="nvarchar" MaxLength="128" />
          <Property Name="GS1CompanyPrefix" Type="varchar" MaxLength="100" />
          <Property Name="GLN" Type="varchar" MaxLength="13" />
          <Property Name="SGLN" Type="varchar" MaxLength="20" />
          <Property Name="IsActive" Type="bit" Nullable="false" />
        </EntityType>
        <EntityType Name="Product">
          <Key>
            <PropertyRef Name="IDProduct" />
          </Key>
          <Property Name="IDProduct" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="IDServiceMasterRecord" Type="int" Nullable="false" />
          <Property Name="IDCustomer" Type="int" Nullable="false" />
          <Property Name="CodeType" Type="nvarchar" MaxLength="10" Nullable="false" />
          <Property Name="Code" Type="nvarchar" MaxLength="14" Nullable="false" />
          <Property Name="InternalCodeType" Type="nvarchar" MaxLength="50" />
          <Property Name="InternalCode" Type="nvarchar" MaxLength="50" />
          <Property Name="Name" Type="nvarchar" MaxLength="255" Nullable="false" />
          <Property Name="CommonName" Type="nvarchar" MaxLength="255" />
          <Property Name="Form" Type="nvarchar" MaxLength="100" />
          <Property Name="PackType" Type="nvarchar" MaxLength="50" />
          <Property Name="PackSize" Type="int" />
          <Property Name="Strength" Type="nvarchar" MaxLength="100" />
          <Property Name="IsActive" Type="bit" Nullable="false" />
          <Property Name="TimestampCreated" Type="datetime" Nullable="false" />
          <Property Name="UserCreated" Type="nvarchar" MaxLength="128" Nullable="false" />
          <Property Name="TimestampUpdated" Type="datetime" />
          <Property Name="UserUpdated" Type="nvarchar" MaxLength="128" />
          <Property Name="SerialNumberSourceType" Type="nvarchar" MaxLength="50" />
          <Property Name="SerializationType" Type="varchar" MaxLength="128" />
          <Property Name="IDCustomerPartner" Type="int" />
        </EntityType>
        <EntityType Name="ProductAdditionalCode">
          <Key>
            <PropertyRef Name="IDProductAdditionalCode" />
          </Key>
          <Property Name="IDProductAdditionalCode" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="IDProduct" Type="int" Nullable="false" />
          <Property Name="CodeType" Type="nvarchar" MaxLength="50" Nullable="false" />
          <Property Name="Code" Type="nvarchar" MaxLength="50" Nullable="false" />
          <Property Name="UserCreated" Type="nvarchar" MaxLength="128" Nullable="false" />
          <Property Name="TimestampCreated" Type="datetime" Nullable="false" />
          <Property Name="UserUpdated" Type="nvarchar" MaxLength="128" />
          <Property Name="TimestampUpdated" Type="datetime" />
        </EntityType>
        <EntityType Name="ProductCodeType">
          <Key>
            <PropertyRef Name="CodeType" />
          </Key>
          <Property Name="CodeType" Type="nvarchar" MaxLength="10" Nullable="false" />
          <Property Name="Length" Type="int" />
          <Property Name="Regex" Type="nvarchar" MaxLength="128" />
        </EntityType>
        <EntityType Name="ProductLabel">
          <Key>
            <PropertyRef Name="IDProductLabel" />
          </Key>
          <Property Name="IDProductLabel" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="IDCustomer" Type="int" Nullable="false" />
          <Property Name="IDProduct" Type="int" Nullable="false" />
          <Property Name="LabelName" Type="nvarchar" MaxLength="255" Nullable="false" />
          <Property Name="Label" Type="nvarchar(max)" Nullable="false" />
          <Property Name="Level" Type="nvarchar" MaxLength="20" Nullable="false" />
          <Property Name="IsDefault" Type="bit" Nullable="false" />
          <Property Name="IDTargetMarket" Type="int" />
          <Property Name="TimestampCreated" Type="datetime" Nullable="false" />
          <Property Name="UserCreated" Type="nvarchar" MaxLength="128" Nullable="false" />
          <Property Name="PrintingSrcSystem" Type="nvarchar" MaxLength="2" />
          <Property Name="PrintingSrcSystemName" Type="nvarchar" MaxLength="100" />
        </EntityType>
        <EntityType Name="SerializationType">
          <Key>
            <PropertyRef Name="IDSerializationType" />
          </Key>
          <Property Name="IDSerializationType" Type="nvarchar" MaxLength="30" Nullable="false" />
          <Property Name="SerializationType" Type="nvarchar" MaxLength="50" Nullable="false" />
          <Property Name="Regex" Type="nvarchar" MaxLength="250" />
          <Property Name="OrderDisplayList" Type="int" />
          <Property Name="IsActive" Type="bit" Nullable="false" />
          <Property Name="IsCarton" Type="bit" Nullable="false" />
          <Property Name="IsCase" Type="bit" Nullable="false" />
          <Property Name="IsPallet" Type="bit" Nullable="false" />
          <Property Name="IsSerialsExpected" Type="bit" />
        </EntityType>
        <EntityType Name="SerialNumberSourceType">
          <Key>
            <PropertyRef Name="IDSerialNumberSourceType" />
          </Key>
          <Property Name="IDSerialNumberSourceType" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="IDService" Type="int" Nullable="false" />
          <Property Name="Code" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="Name" Type="varchar" MaxLength="50" />
        </EntityType>
        <EntityType Name="Service">
          <Key>
            <PropertyRef Name="IDService" />
          </Key>
          <Property Name="IDService" Type="int" Nullable="false" />
          <Property Name="Name" Type="varchar" MaxLength="255" Nullable="false" />
          <Property Name="SrcSystem" Type="nvarchar" MaxLength="2" />
          <Property Name="SerializationType" Type="nvarchar" MaxLength="30" />
          <Property Name="IsCMD" Type="bit" Nullable="false" />
          <Property Name="IsServiceOwnProduct" Type="bit" Nullable="false" />
          <Property Name="IsServiceOwnCustomer" Type="bit" Nullable="false" />
          <Property Name="ADURI" Type="nvarchar" MaxLength="255" />
          <Property Name="ProductUpdateSql" Type="nvarchar" MaxLength="255" />
          <Property Name="CustomerUpdateSql" Type="nvarchar" MaxLength="255" />
          <Property Name="IsServiceOwnManufacturer" Type="bit" Nullable="false" />
          <Property Name="ManufacturerUpdateSql" Type="nvarchar" MaxLength="255" />
          <Property Name="UserUpdateSql" Type="nvarchar" MaxLength="255" />
          <Property Name="IsExternalCodeAccepted" Type="bit" Nullable="false" />
          <Property Name="ProductRegex" Type="nvarchar" MaxLength="500" />
          <Property Name="IsUserDeleteAllowed" Type="bit" Nullable="false" />
          <Property Name="IsServiceOwnLabel" Type="bit" Nullable="false" />
        </EntityType>
        <EntityType Name="ServiceContract">
          <Key>
            <PropertyRef Name="IDServiceContract" />
          </Key>
          <Property Name="IDServiceContract" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="IDContract" Type="int" Nullable="false" />
          <Property Name="IDService" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="ServiceEnvironment">
          <Key>
            <PropertyRef Name="IDServiceEnvironment" />
          </Key>
          <Property Name="IDServiceEnvironment" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="IDService" Type="int" Nullable="false" />
          <Property Name="Name" Type="varchar" MaxLength="255" />
        </EntityType>
        <EntityType Name="ServiceRole">
          <Key>
            <PropertyRef Name="IDServiceRole" />
          </Key>
          <Property Name="IDServiceRole" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="RoleName" Type="nvarchar" MaxLength="128" Nullable="false" />
          <Property Name="GroupName" Type="nvarchar" MaxLength="128" />
        </EntityType>
        <EntityType Name="ServiceUserLogin">
          <Key>
            <PropertyRef Name="IDServiceUserLogin" />
          </Key>
          <Property Name="IDServiceUserLogin" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="IDService" Type="int" Nullable="false" />
          <Property Name="LgnName" Type="nvarchar" MaxLength="50" Nullable="false" />
          <Property Name="ExternalSenderCode" Type="nvarchar" MaxLength="250" />
          <Property Name="ExternalRecieverCode" Type="nvarchar" MaxLength="250" />
          <Property Name="IDManufacturer" Type="int" />
        </EntityType>
        <EntityType Name="TargetMarket">
          <Key>
            <PropertyRef Name="IDTargetMarket" />
          </Key>
          <Property Name="IDTargetMarket" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Name" Type="nvarchar" MaxLength="128" Nullable="false" />
          <Property Name="ShortName" Type="nvarchar" MaxLength="4" Nullable="false" />
          <Property Name="GS1RNCode" Type="int" />
          <Property Name="TimestampCreated" Type="datetime" Nullable="false" />
          <Property Name="UserCreated" Type="nvarchar" MaxLength="128" Nullable="false" />
          <Property Name="TimestampUpdated" Type="datetime" />
          <Property Name="UserUpdated" Type="nvarchar" MaxLength="128" />
          <Property Name="SerializationType" Type="nvarchar" MaxLength="50" />
          <Property Name="SrcSystem" Type="nvarchar" MaxLength="2" />
        </EntityType>
        <EntityType Name="UserLogin">
          <Key>
            <PropertyRef Name="LgnName" />
          </Key>
          <Property Name="LgnName" Type="nvarchar" MaxLength="50" Nullable="false" />
          <Property Name="IDCustomer" Type="int" Nullable="false" />
          <Property Name="IDManufacturer" Type="int" />
          <Property Name="IsADUser" Type="bit" Nullable="false" />
          <Property Name="TimestampCreated" Type="datetime" Nullable="false" />
          <Property Name="UserCreated" Type="nvarchar" MaxLength="128" Nullable="false" />
          <Property Name="TimestampUpdated" Type="datetime" />
          <Property Name="UserUpdated" Type="nvarchar" MaxLength="128" />
        </EntityType>
        <EntityType Name="UserLoginServiceRole">
          <Key>
            <PropertyRef Name="IDUserLoginServiceRole" />
          </Key>
          <Property Name="IDUserLoginServiceRole" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="LgnName" Type="nvarchar" MaxLength="50" Nullable="false" />
          <Property Name="IDServiceRole" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="UserPrinterSetting">
          <Key>
            <PropertyRef Name="LgnName" />
          </Key>
          <Property Name="LgnName" Type="nvarchar" MaxLength="50" Nullable="false" />
          <Property Name="UseNetworkPrinter" Type="bit" Nullable="false" />
          <Property Name="NetworkPrinterIP" Type="nvarchar" MaxLength="20" />
          <Property Name="NetworkPrinterPort" Type="int" />
          <Property Name="PrinterDriverName" Type="nvarchar" MaxLength="250" />
          <Property Name="PrinterLanguageType" Type="nvarchar" MaxLength="8" />
          <Property Name="TimestampCreated" Type="datetime" Nullable="false" />
          <Property Name="TimestampUpdated" Type="datetime" />
          <Property Name="UserCreated" Type="nvarchar" MaxLength="50" />
          <Property Name="UserUpdated" Type="nvarchar" MaxLength="50" />
        </EntityType>
        <Association Name="FK_CustomerAggregationLevel_Customer">
          <End Role="Customer" Type="Self.Customer" Multiplicity="1" />
          <End Role="CustomerAggregationLevel" Type="Self.CustomerAggregationLevel" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Customer">
              <PropertyRef Name="IDCustomer" />
            </Principal>
            <Dependent Role="CustomerAggregationLevel">
              <PropertyRef Name="IDCustomer" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_CustomerAggregationLevel_Product">
          <End Role="Product" Type="Self.Product" Multiplicity="0..1" />
          <End Role="CustomerAggregationLevel" Type="Self.CustomerAggregationLevel" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Product">
              <PropertyRef Name="IDProduct" />
            </Principal>
            <Dependent Role="CustomerAggregationLevel">
              <PropertyRef Name="IDProduct" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_ProductLabel_Customer">
          <End Role="Customer" Type="Self.Customer" Multiplicity="1" />
          <End Role="ProductLabel" Type="Self.ProductLabel" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Customer">
              <PropertyRef Name="IDCustomer" />
            </Principal>
            <Dependent Role="ProductLabel">
              <PropertyRef Name="IDCustomer" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_ProductLabel_Product">
          <End Role="Product" Type="Self.Product" Multiplicity="1" />
          <End Role="ProductLabel" Type="Self.ProductLabel" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Product">
              <PropertyRef Name="IDProduct" />
            </Principal>
            <Dependent Role="ProductLabel">
              <PropertyRef Name="IDProduct" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_ProductLabel_TargetMarket">
          <End Role="TargetMarket" Type="Self.TargetMarket" Multiplicity="0..1" />
          <End Role="ProductLabel" Type="Self.ProductLabel" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="TargetMarket">
              <PropertyRef Name="IDTargetMarket" />
            </Principal>
            <Dependent Role="ProductLabel">
              <PropertyRef Name="IDTargetMarket" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityContainer Name="CloudMasterDataStoreContainer">
          <EntitySet Name="AdditionalCodeType" EntityType="Self.AdditionalCodeType" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="AuditTrail" EntityType="Self.AuditTrail" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="AuditTrailItem" EntityType="Self.AuditTrailItem" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="BusinessSegment" EntityType="Self.BusinessSegment" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="CommunicationChannel" EntityType="Self.CommunicationChannel" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Contract" EntityType="Self.Contract" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="ContractStatus" EntityType="Self.ContractStatus" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="ContractType" EntityType="Self.ContractType" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Country" EntityType="Self.Country" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Customer" EntityType="Self.Customer" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="CustomerAggregationLevel" EntityType="Self.CustomerAggregationLevel" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="CustomerContract" EntityType="Self.CustomerContract" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="CustomerLabelDataSource" EntityType="Self.CustomerLabelDataSource" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="CustomerPartner" EntityType="Self.CustomerPartner" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="CustomerPartnerParameter" EntityType="Self.CustomerPartnerParameter" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="CustomerService" EntityType="Self.CustomerService" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="InternalCodeType" EntityType="Self.InternalCodeType" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="LabelTemplate" EntityType="Self.LabelTemplate" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="MahType" EntityType="Self.MahType" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Manufacturer" EntityType="Self.Manufacturer" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Product" EntityType="Self.Product" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="ProductAdditionalCode" EntityType="Self.ProductAdditionalCode" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="ProductCodeType" EntityType="Self.ProductCodeType" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="ProductLabel" EntityType="Self.ProductLabel" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="SerializationType" EntityType="Self.SerializationType" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="SerialNumberSourceType" EntityType="Self.SerialNumberSourceType" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Service" EntityType="Self.Service" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="ServiceContract" EntityType="Self.ServiceContract" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="ServiceEnvironment" EntityType="Self.ServiceEnvironment" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="ServiceRole" EntityType="Self.ServiceRole" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="ServiceUserLogin" EntityType="Self.ServiceUserLogin" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="TargetMarket" EntityType="Self.TargetMarket" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="UserLogin" EntityType="Self.UserLogin" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="UserLoginServiceRole" EntityType="Self.UserLoginServiceRole" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="UserPrinterSetting" EntityType="Self.UserPrinterSetting" Schema="dbo" store:Type="Tables" />
          <AssociationSet Name="FK_CustomerAggregationLevel_Customer" Association="Self.FK_CustomerAggregationLevel_Customer">
            <End Role="Customer" EntitySet="Customer" />
            <End Role="CustomerAggregationLevel" EntitySet="CustomerAggregationLevel" />
          </AssociationSet>
          <AssociationSet Name="FK_CustomerAggregationLevel_Product" Association="Self.FK_CustomerAggregationLevel_Product">
            <End Role="Product" EntitySet="Product" />
            <End Role="CustomerAggregationLevel" EntitySet="CustomerAggregationLevel" />
          </AssociationSet>
          <AssociationSet Name="FK_ProductLabel_Customer" Association="Self.FK_ProductLabel_Customer">
            <End Role="Customer" EntitySet="Customer" />
            <End Role="ProductLabel" EntitySet="ProductLabel" />
          </AssociationSet>
          <AssociationSet Name="FK_ProductLabel_Product" Association="Self.FK_ProductLabel_Product">
            <End Role="Product" EntitySet="Product" />
            <End Role="ProductLabel" EntitySet="ProductLabel" />
          </AssociationSet>
          <AssociationSet Name="FK_ProductLabel_TargetMarket" Association="Self.FK_ProductLabel_TargetMarket">
            <End Role="TargetMarket" EntitySet="TargetMarket" />
            <End Role="ProductLabel" EntitySet="ProductLabel" />
          </AssociationSet>
        </EntityContainer>
      </Schema>
    </edmx:StorageModels>
    <!-- CSDL content -->
    <edmx:ConceptualModels>
      <Schema Namespace="CloudMasterData" Alias="Self" annotation:UseStrongSpatialTypes="false" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
        <EntityType Name="AdditionalCodeType">
          <Key>
            <PropertyRef Name="Name" />
          </Key>
          <Property Name="Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" Nullable="false" />
        </EntityType>
        <EntityType Name="AuditTrail">
          <Key>
            <PropertyRef Name="IDAuditTrail" />
          </Key>
          <Property Name="IDAuditTrail" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Action" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="IDTable" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="TableName" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="Timestamp" Type="DateTime" Precision="3" />
          <Property Name="User" Type="String" MaxLength="128" FixedLength="false" Unicode="true" />
          <Property Name="DisplayName" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="IDTableDetail" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="AuditTrailItem">
          <Key>
            <PropertyRef Name="IDAuditTrailItem" />
          </Key>
          <Property Name="IDAuditTrailItem" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="IDAuditTrail" Type="Int32" Nullable="false" />
          <Property Name="FieldName" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="OldValue" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="NewValue" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="DisplayName" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="BusinessSegment">
          <Key>
            <PropertyRef Name="IDBusinessSegment" />
          </Key>
          <Property Name="IDBusinessSegment" Type="Int32" Nullable="false" />
          <Property Name="Segment" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="CommunicationChannel">
          <Key>
            <PropertyRef Name="Type" />
          </Key>
          <Property Name="Type" Type="String" MaxLength="50" FixedLength="false" Unicode="true" Nullable="false" />
        </EntityType>
        <EntityType Name="Contract">
          <Key>
            <PropertyRef Name="IDContract" />
          </Key>
          <Property Name="IDContract" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="IDCustomer" Type="Int32" Nullable="false" />
          <Property Name="Name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="StartDate" Type="DateTime" Nullable="false" Precision="0" />
          <Property Name="EndDate" Type="DateTime" Nullable="false" Precision="0" />
          <Property Name="IDContractStatus" Type="Int32" Nullable="false" />
          <Property Name="IDContractType" Type="Int32" Nullable="false" />
          <Property Name="MaxUsers" Type="Int32" Nullable="false" />
          <Property Name="ExpirationWarningDays" Type="Int32" Nullable="false" />
          <Property Name="SerialNumberCount" Type="Int32" Nullable="false" />
          <Property Name="ContractNumber" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="TimestampCreated" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="UserCreated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="TimestampUpdated" Type="DateTime" Precision="3" />
          <Property Name="UserUpdated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="ContractStatu">
          <Key>
            <PropertyRef Name="IDContractStatus" />
          </Key>
          <Property Name="IDContractStatus" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
        </EntityType>
        <EntityType Name="ContractType">
          <Key>
            <PropertyRef Name="IDContractType" />
          </Key>
          <Property Name="IDContractType" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Name" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
        </EntityType>
        <EntityType Name="Country">
          <Key>
            <PropertyRef Name="Code" />
          </Key>
          <Property Name="Code" Type="String" MaxLength="2" FixedLength="true" Unicode="false" Nullable="false" />
          <Property Name="Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="NameSecLng" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="GS1RNCode" Type="Int32" />
        </EntityType>
        <EntityType Name="Customer">
          <Key>
            <PropertyRef Name="IDCustomer" />
          </Key>
          <Property Name="IDCustomer" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Name" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="TimestampCreated" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="UserCreated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="TimestampUpdated" Type="DateTime" Precision="3" />
          <Property Name="UserUpdated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" />
          <Property Name="GS1CompanyPrefix" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="SGLN" Type="String" MaxLength="20" FixedLength="false" Unicode="false" />
          <Property Name="GLN" Type="String" MaxLength="13" FixedLength="false" Unicode="false" />
          <Property Name="CustomerStreet" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="CustomerStreet2" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="CustomerCity" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="CustomerPostCode" Type="String" MaxLength="30" FixedLength="false" Unicode="true" />
          <Property Name="CustomerCountryCode" Type="String" MaxLength="2" FixedLength="true" Unicode="false" />
          <Property Name="Code" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="IDBusinessSegment" Type="Int32" Nullable="false" />
          <NavigationProperty Name="CustomerAggregationLevels" Relationship="Self.FK_CustomerAggregationLevel_Customer" FromRole="Customer" ToRole="CustomerAggregationLevel" />
          <NavigationProperty Name="ProductLabels" Relationship="Self.FK_ProductLabel_Customer" FromRole="Customer" ToRole="ProductLabel" />
        </EntityType>
        <EntityType Name="CustomerAggregationLevel">
          <Key>
            <PropertyRef Name="IDCustomerAggregationLevel" />
          </Key>
          <Property Name="IDCustomerAggregationLevel" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="IDCustomer" Type="Int32" Nullable="false" />
          <Property Name="IDProduct" Type="Int32" />
          <Property Name="Name" Type="String" MaxLength="20" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="Type" Type="String" MaxLength="20" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="SerialType" Type="String" MaxLength="10" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="UnitCapacity" Type="Int32" />
          <Property Name="SubLevel" Type="String" MaxLength="20" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="SubLevelItemType" Type="String" MaxLength="20" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="TimestampCreated" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="UserCreated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="TimestampUpdated" Type="DateTime" Precision="3" />
          <Property Name="UserUpdated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="Customer" Relationship="Self.FK_CustomerAggregationLevel_Customer" FromRole="CustomerAggregationLevel" ToRole="Customer" />
          <NavigationProperty Name="Product" Relationship="Self.FK_CustomerAggregationLevel_Product" FromRole="CustomerAggregationLevel" ToRole="Product" />
        </EntityType>
        <EntityType Name="CustomerContract">
          <Key>
            <PropertyRef Name="IDCustomerContract" />
          </Key>
          <Property Name="IDCustomerContract" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="IDCustomer" Type="Int32" Nullable="false" />
          <Property Name="Name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="StartDate" Type="DateTime" Nullable="false" Precision="0" />
          <Property Name="EndDate" Type="DateTime" Precision="0" />
          <Property Name="IsTrial" Type="Boolean" Nullable="false" />
          <Property Name="TrialEndDate" Type="DateTime" Precision="3" />
          <Property Name="IsContract" Type="Boolean" Nullable="false" />
          <Property Name="ContractTerm" Type="DateTime" Precision="0" />
          <Property Name="ContractData" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="ContractNotifyEndDays" Type="Int32" Nullable="false" />
          <Property Name="IsPayed" Type="Boolean" Nullable="false" />
          <Property Name="PaymentTerm" Type="DateTime" Precision="0" />
          <Property Name="PaymentNotifyDays" Type="Int32" Nullable="false" />
          <Property Name="InvoiceData" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="IsCanceled" Type="Boolean" Nullable="false" />
          <Property Name="CancelReason" Type="String" MaxLength="200" FixedLength="false" Unicode="true" />
          <Property Name="TimestampCreated" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="UserCreated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="TimestampUpdated" Type="DateTime" Precision="3" />
          <Property Name="UserUpdated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="CustomerLabelDataSource">
          <Key>
            <PropertyRef Name="IDCustomerDataSource" />
          </Key>
          <Property Name="IDCustomerDataSource" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="IDCustomer" Type="Int32" Nullable="false" />
          <Property Name="Name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="DataSource" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="TimestampCreated" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="UserCreated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="TimestampUpdated" Type="DateTime" Precision="3" />
          <Property Name="UserUpdated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" />
          <Property Name="SrcSystemName" Type="String" MaxLength="200" FixedLength="false" Unicode="true" />
          <Property Name="SrcSystem" Type="String" MaxLength="2" FixedLength="false" Unicode="true" />
          <Property Name="Unit" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="SerialType" Type="String" MaxLength="20" FixedLength="false" Unicode="true" />
          <Property Name="Item" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="ItemSerialType" Type="String" MaxLength="20" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="CustomerPartner">
          <Key>
            <PropertyRef Name="IDCustomerPartner" />
          </Key>
          <Property Name="IDCustomerPartner" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="IDCustomer" Type="Int32" Nullable="false" />
          <Property Name="Name" Type="String" MaxLength="255" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="SystemName" Type="String" MaxLength="255" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="SNXRequest" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="SSCCRequest" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="EBRImport" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="BatchReport" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="ShipmentReport" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="IsActive" Type="Boolean" Nullable="false" />
        </EntityType>
        <EntityType Name="CustomerPartnerParameter">
          <Key>
            <PropertyRef Name="IDCustomerPartnerParameter" />
          </Key>
          <Property Name="IDCustomerPartnerParameter" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="IDCustomerPartner" Type="Int32" Nullable="false" />
          <Property Name="Name" Type="String" MaxLength="255" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="Value" Type="String" MaxLength="255" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="TimestampCreated" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="UserCreated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
        </EntityType>
        <EntityType Name="CustomerService">
          <Key>
            <PropertyRef Name="IDCustomerService" />
          </Key>
          <Property Name="IDCustomerService" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="IDCustomer" Type="Int32" Nullable="false" />
          <Property Name="IDService" Type="Int32" Nullable="false" />
          <Property Name="IsDatabaseCustomerSeparation" Type="Boolean" Nullable="false" />
          <Property Name="DatabaseConnectionString" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="InternalCodeType">
          <Key>
            <PropertyRef Name="Name" />
          </Key>
          <Property Name="Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" Nullable="false" />
        </EntityType>
        <EntityType Name="LabelTemplate">
          <Key>
            <PropertyRef Name="IDLabelTemplate" />
          </Key>
          <Property Name="IDLabelTemplate" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="IDCustomer" Type="Int32" Nullable="false" />
          <Property Name="Unit" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Label" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="LabelName" Type="String" MaxLength="255" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="TimestampCreated" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="UserCreated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
        </EntityType>
        <EntityType Name="MahType">
          <Key>
            <PropertyRef Name="IDMahType" />
          </Key>
          <Property Name="IDMahType" Type="Int32" Nullable="false" />
          <Property Name="Type" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="Manufacturer">
          <Key>
            <PropertyRef Name="IDMah" />
          </Key>
          <Property Name="IDMah" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="IDCustomer" Type="Int32" Nullable="false" />
          <Property Name="IDMahType" Type="Int32" />
          <Property Name="MahID" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="MahName" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="MahStreet" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="MahStreet2" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="MahCity" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="MahPostCode" Type="String" MaxLength="30" FixedLength="false" Unicode="true" />
          <Property Name="MahCountryCode" Type="String" MaxLength="2" FixedLength="true" Unicode="false" />
          <Property Name="TimeStampCreated" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="UserCreated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="TimeStampUpdated" Type="DateTime" Precision="3" />
          <Property Name="UserUpdated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" />
          <Property Name="GS1CompanyPrefix" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="GLN" Type="String" MaxLength="13" FixedLength="false" Unicode="false" />
          <Property Name="SGLN" Type="String" MaxLength="20" FixedLength="false" Unicode="false" />
          <Property Name="IsActive" Type="Boolean" Nullable="false" />
        </EntityType>
        <EntityType Name="Product">
          <Key>
            <PropertyRef Name="IDProduct" />
          </Key>
          <Property Name="IDProduct" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="IDServiceMasterRecord" Type="Int32" Nullable="false" />
          <Property Name="IDCustomer" Type="Int32" Nullable="false" />
          <Property Name="CodeType" Type="String" MaxLength="10" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="Code" Type="String" MaxLength="14" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="InternalCodeType" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="InternalCode" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Name" Type="String" MaxLength="255" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="CommonName" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="Form" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="PackType" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="PackSize" Type="Int32" />
          <Property Name="Strength" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="IsActive" Type="Boolean" Nullable="false" />
          <Property Name="TimestampCreated" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="UserCreated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="TimestampUpdated" Type="DateTime" Precision="3" />
          <Property Name="UserUpdated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" />
          <Property Name="SerialNumberSourceType" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="SerializationType" Type="String" MaxLength="128" FixedLength="false" Unicode="false" />
          <Property Name="IDCustomerPartner" Type="Int32" />
          <NavigationProperty Name="CustomerAggregationLevels" Relationship="Self.FK_CustomerAggregationLevel_Product" FromRole="Product" ToRole="CustomerAggregationLevel" />
          <NavigationProperty Name="ProductLabels" Relationship="Self.FK_ProductLabel_Product" FromRole="Product" ToRole="ProductLabel" />
        </EntityType>
        <EntityType Name="ProductAdditionalCode">
          <Key>
            <PropertyRef Name="IDProductAdditionalCode" />
          </Key>
          <Property Name="IDProductAdditionalCode" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="IDProduct" Type="Int32" Nullable="false" />
          <Property Name="CodeType" Type="String" MaxLength="50" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="Code" Type="String" MaxLength="50" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="UserCreated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="TimestampCreated" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="UserUpdated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" />
          <Property Name="TimestampUpdated" Type="DateTime" Precision="3" />
        </EntityType>
        <EntityType Name="ProductCodeType">
          <Key>
            <PropertyRef Name="CodeType" />
          </Key>
          <Property Name="CodeType" Type="String" MaxLength="10" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="Length" Type="Int32" />
          <Property Name="Regex" Type="String" MaxLength="128" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="ProductLabel">
          <Key>
            <PropertyRef Name="IDProductLabel" />
          </Key>
          <Property Name="IDProductLabel" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="IDCustomer" Type="Int32" Nullable="false" />
          <Property Name="IDProduct" Type="Int32" Nullable="false" />
          <Property Name="LabelName" Type="String" MaxLength="255" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="Label" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="Level" Type="String" MaxLength="20" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="IsDefault" Type="Boolean" Nullable="false" />
          <Property Name="IDTargetMarket" Type="Int32" />
          <Property Name="TimestampCreated" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="UserCreated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="PrintingSrcSystem" Type="String" MaxLength="2" FixedLength="false" Unicode="true" />
          <Property Name="PrintingSrcSystemName" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="Customer" Relationship="Self.FK_ProductLabel_Customer" FromRole="ProductLabel" ToRole="Customer" />
          <NavigationProperty Name="Product" Relationship="Self.FK_ProductLabel_Product" FromRole="ProductLabel" ToRole="Product" />
          <NavigationProperty Name="TargetMarket" Relationship="Self.FK_ProductLabel_TargetMarket" FromRole="ProductLabel" ToRole="TargetMarket" />
        </EntityType>
        <EntityType Name="SerializationType">
          <Key>
            <PropertyRef Name="IDSerializationType" />
          </Key>
          <Property Name="IDSerializationType" Type="String" MaxLength="30" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="SerializationType1" Type="String" MaxLength="50" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="Regex" Type="String" MaxLength="250" FixedLength="false" Unicode="true" />
          <Property Name="OrderDisplayList" Type="Int32" />
          <Property Name="IsActive" Type="Boolean" Nullable="false" />
          <Property Name="IsCarton" Type="Boolean" Nullable="false" />
          <Property Name="IsCase" Type="Boolean" Nullable="false" />
          <Property Name="IsPallet" Type="Boolean" Nullable="false" />
          <Property Name="IsSerialsExpected" Type="Boolean" />
        </EntityType>
        <EntityType Name="SerialNumberSourceType">
          <Key>
            <PropertyRef Name="IDSerialNumberSourceType" />
          </Key>
          <Property Name="IDSerialNumberSourceType" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="IDService" Type="Int32" Nullable="false" />
          <Property Name="Code" Type="String" MaxLength="50" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="Name" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="Service">
          <Key>
            <PropertyRef Name="IDService" />
          </Key>
          <Property Name="IDService" Type="Int32" Nullable="false" />
          <Property Name="Name" Type="String" MaxLength="255" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="SrcSystem" Type="String" MaxLength="2" FixedLength="false" Unicode="true" />
          <Property Name="SerializationType" Type="String" MaxLength="30" FixedLength="false" Unicode="true" />
          <Property Name="IsCMD" Type="Boolean" Nullable="false" />
          <Property Name="IsServiceOwnProduct" Type="Boolean" Nullable="false" />
          <Property Name="IsServiceOwnCustomer" Type="Boolean" Nullable="false" />
          <Property Name="ADURI" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="ProductUpdateSql" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="CustomerUpdateSql" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="IsServiceOwnManufacturer" Type="Boolean" Nullable="false" />
          <Property Name="ManufacturerUpdateSql" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="UserUpdateSql" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="IsExternalCodeAccepted" Type="Boolean" Nullable="false" />
          <Property Name="ProductRegex" Type="String" MaxLength="500" FixedLength="false" Unicode="true" />
          <Property Name="IsUserDeleteAllowed" Type="Boolean" Nullable="false" />
          <Property Name="IsServiceOwnLabel" Type="Boolean" Nullable="false" />
        </EntityType>
        <EntityType Name="ServiceContract">
          <Key>
            <PropertyRef Name="IDServiceContract" />
          </Key>
          <Property Name="IDServiceContract" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="IDContract" Type="Int32" Nullable="false" />
          <Property Name="IDService" Type="Int32" Nullable="false" />
        </EntityType>
        <EntityType Name="ServiceEnvironment">
          <Key>
            <PropertyRef Name="IDServiceEnvironment" />
          </Key>
          <Property Name="IDServiceEnvironment" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="IDService" Type="Int32" Nullable="false" />
          <Property Name="Name" Type="String" MaxLength="255" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="ServiceRole">
          <Key>
            <PropertyRef Name="IDServiceRole" />
          </Key>
          <Property Name="IDServiceRole" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="RoleName" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="GroupName" Type="String" MaxLength="128" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="ServiceUserLogin">
          <Key>
            <PropertyRef Name="IDServiceUserLogin" />
          </Key>
          <Property Name="IDServiceUserLogin" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="IDService" Type="Int32" Nullable="false" />
          <Property Name="LgnName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="ExternalSenderCode" Type="String" MaxLength="250" FixedLength="false" Unicode="true" />
          <Property Name="ExternalRecieverCode" Type="String" MaxLength="250" FixedLength="false" Unicode="true" />
          <Property Name="IDManufacturer" Type="Int32" />
        </EntityType>
        <EntityType Name="TargetMarket">
          <Key>
            <PropertyRef Name="IDTargetMarket" />
          </Key>
          <Property Name="IDTargetMarket" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Name" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="ShortName" Type="String" MaxLength="4" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="GS1RNCode" Type="Int32" />
          <Property Name="TimestampCreated" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="UserCreated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="TimestampUpdated" Type="DateTime" Precision="3" />
          <Property Name="UserUpdated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" />
          <Property Name="SerializationType" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="SrcSystem" Type="String" MaxLength="2" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="ProductLabels" Relationship="Self.FK_ProductLabel_TargetMarket" FromRole="TargetMarket" ToRole="ProductLabel" />
        </EntityType>
        <EntityType Name="UserLogin">
          <Key>
            <PropertyRef Name="LgnName" />
          </Key>
          <Property Name="LgnName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="IDCustomer" Type="Int32" Nullable="false" />
          <Property Name="IDManufacturer" Type="Int32" />
          <Property Name="IsADUser" Type="Boolean" Nullable="false" />
          <Property Name="TimestampCreated" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="UserCreated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="TimestampUpdated" Type="DateTime" Precision="3" />
          <Property Name="UserUpdated" Type="String" MaxLength="128" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="UserLoginServiceRole">
          <Key>
            <PropertyRef Name="IDUserLoginServiceRole" />
          </Key>
          <Property Name="IDUserLoginServiceRole" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="LgnName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="IDServiceRole" Type="Int32" Nullable="false" />
        </EntityType>
        <EntityType Name="UserPrinterSetting">
          <Key>
            <PropertyRef Name="LgnName" />
          </Key>
          <Property Name="LgnName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="UseNetworkPrinter" Type="Boolean" Nullable="false" />
          <Property Name="NetworkPrinterIP" Type="String" MaxLength="20" FixedLength="false" Unicode="true" />
          <Property Name="NetworkPrinterPort" Type="Int32" />
          <Property Name="PrinterDriverName" Type="String" MaxLength="250" FixedLength="false" Unicode="true" />
          <Property Name="PrinterLanguageType" Type="String" MaxLength="8" FixedLength="false" Unicode="true" />
          <Property Name="TimestampCreated" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="TimestampUpdated" Type="DateTime" Precision="3" />
          <Property Name="UserCreated" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="UserUpdated" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
        </EntityType>
        <Association Name="FK_CustomerAggregationLevel_Customer">
          <End Role="Customer" Type="Self.Customer" Multiplicity="1" />
          <End Role="CustomerAggregationLevel" Type="Self.CustomerAggregationLevel" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Customer">
              <PropertyRef Name="IDCustomer" />
            </Principal>
            <Dependent Role="CustomerAggregationLevel">
              <PropertyRef Name="IDCustomer" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_ProductLabel_Customer">
          <End Role="Customer" Type="Self.Customer" Multiplicity="1" />
          <End Role="ProductLabel" Type="Self.ProductLabel" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Customer">
              <PropertyRef Name="IDCustomer" />
            </Principal>
            <Dependent Role="ProductLabel">
              <PropertyRef Name="IDCustomer" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_CustomerAggregationLevel_Product">
          <End Role="Product" Type="Self.Product" Multiplicity="0..1" />
          <End Role="CustomerAggregationLevel" Type="Self.CustomerAggregationLevel" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Product">
              <PropertyRef Name="IDProduct" />
            </Principal>
            <Dependent Role="CustomerAggregationLevel">
              <PropertyRef Name="IDProduct" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_ProductLabel_Product">
          <End Role="Product" Type="Self.Product" Multiplicity="1" />
          <End Role="ProductLabel" Type="Self.ProductLabel" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Product">
              <PropertyRef Name="IDProduct" />
            </Principal>
            <Dependent Role="ProductLabel">
              <PropertyRef Name="IDProduct" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_ProductLabel_TargetMarket">
          <End Role="TargetMarket" Type="Self.TargetMarket" Multiplicity="0..1" />
          <End Role="ProductLabel" Type="Self.ProductLabel" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="TargetMarket">
              <PropertyRef Name="IDTargetMarket" />
            </Principal>
            <Dependent Role="ProductLabel">
              <PropertyRef Name="IDTargetMarket" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityContainer Name="DBModel" annotation:LazyLoadingEnabled="true">
          <EntitySet Name="AdditionalCodeTypes" EntityType="Self.AdditionalCodeType" />
          <EntitySet Name="AuditTrails" EntityType="Self.AuditTrail" />
          <EntitySet Name="AuditTrailItems" EntityType="Self.AuditTrailItem" />
          <EntitySet Name="BusinessSegments" EntityType="Self.BusinessSegment" />
          <EntitySet Name="CommunicationChannels" EntityType="Self.CommunicationChannel" />
          <EntitySet Name="Contracts" EntityType="Self.Contract" />
          <EntitySet Name="ContractStatus" EntityType="Self.ContractStatu" />
          <EntitySet Name="ContractTypes" EntityType="Self.ContractType" />
          <EntitySet Name="Countries" EntityType="Self.Country" />
          <EntitySet Name="Customers" EntityType="Self.Customer" />
          <EntitySet Name="CustomerAggregationLevels" EntityType="Self.CustomerAggregationLevel" />
          <EntitySet Name="CustomerContracts" EntityType="Self.CustomerContract" />
          <EntitySet Name="CustomerLabelDataSources" EntityType="Self.CustomerLabelDataSource" />
          <EntitySet Name="CustomerPartners" EntityType="Self.CustomerPartner" />
          <EntitySet Name="CustomerPartnerParameters" EntityType="Self.CustomerPartnerParameter" />
          <EntitySet Name="CustomerServices" EntityType="Self.CustomerService" />
          <EntitySet Name="InternalCodeTypes" EntityType="Self.InternalCodeType" />
          <EntitySet Name="LabelTemplates" EntityType="Self.LabelTemplate" />
          <EntitySet Name="MahTypes" EntityType="Self.MahType" />
          <EntitySet Name="Manufacturers" EntityType="Self.Manufacturer" />
          <EntitySet Name="Products" EntityType="Self.Product" />
          <EntitySet Name="ProductAdditionalCodes" EntityType="Self.ProductAdditionalCode" />
          <EntitySet Name="ProductCodeTypes" EntityType="Self.ProductCodeType" />
          <EntitySet Name="ProductLabels" EntityType="Self.ProductLabel" />
          <EntitySet Name="SerializationTypes" EntityType="Self.SerializationType" />
          <EntitySet Name="SerialNumberSourceTypes" EntityType="Self.SerialNumberSourceType" />
          <EntitySet Name="Services" EntityType="Self.Service" />
          <EntitySet Name="ServiceContracts" EntityType="Self.ServiceContract" />
          <EntitySet Name="ServiceEnvironments" EntityType="Self.ServiceEnvironment" />
          <EntitySet Name="ServiceRoles" EntityType="Self.ServiceRole" />
          <EntitySet Name="ServiceUserLogins" EntityType="Self.ServiceUserLogin" />
          <EntitySet Name="TargetMarkets" EntityType="Self.TargetMarket" />
          <EntitySet Name="UserLogins" EntityType="Self.UserLogin" />
          <EntitySet Name="UserLoginServiceRoles" EntityType="Self.UserLoginServiceRole" />
          <EntitySet Name="UserPrinterSettings" EntityType="Self.UserPrinterSetting" />
          <AssociationSet Name="FK_CustomerAggregationLevel_Customer" Association="Self.FK_CustomerAggregationLevel_Customer">
            <End Role="Customer" EntitySet="Customers" />
            <End Role="CustomerAggregationLevel" EntitySet="CustomerAggregationLevels" />
          </AssociationSet>
          <AssociationSet Name="FK_ProductLabel_Customer" Association="Self.FK_ProductLabel_Customer">
            <End Role="Customer" EntitySet="Customers" />
            <End Role="ProductLabel" EntitySet="ProductLabels" />
          </AssociationSet>
          <AssociationSet Name="FK_CustomerAggregationLevel_Product" Association="Self.FK_CustomerAggregationLevel_Product">
            <End Role="Product" EntitySet="Products" />
            <End Role="CustomerAggregationLevel" EntitySet="CustomerAggregationLevels" />
          </AssociationSet>
          <AssociationSet Name="FK_ProductLabel_Product" Association="Self.FK_ProductLabel_Product">
            <End Role="Product" EntitySet="Products" />
            <End Role="ProductLabel" EntitySet="ProductLabels" />
          </AssociationSet>
          <AssociationSet Name="FK_ProductLabel_TargetMarket" Association="Self.FK_ProductLabel_TargetMarket">
            <End Role="TargetMarket" EntitySet="TargetMarkets" />
            <End Role="ProductLabel" EntitySet="ProductLabels" />
          </AssociationSet>
        </EntityContainer>
      </Schema>
    </edmx:ConceptualModels>
    <!-- C-S mapping content -->
    <edmx:Mappings>
      <Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
        <EntityContainerMapping StorageEntityContainer="CloudMasterDataStoreContainer" CdmEntityContainer="DBModel">
          <EntitySetMapping Name="AdditionalCodeTypes">
            <EntityTypeMapping TypeName="CloudMasterData.AdditionalCodeType">
              <MappingFragment StoreEntitySet="AdditionalCodeType">
                <ScalarProperty Name="Name" ColumnName="Name" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="AuditTrails">
            <EntityTypeMapping TypeName="CloudMasterData.AuditTrail">
              <MappingFragment StoreEntitySet="AuditTrail">
                <ScalarProperty Name="IDAuditTrail" ColumnName="IDAuditTrail" />
                <ScalarProperty Name="Action" ColumnName="Action" />
                <ScalarProperty Name="IDTable" ColumnName="IDTable" />
                <ScalarProperty Name="TableName" ColumnName="TableName" />
                <ScalarProperty Name="Timestamp" ColumnName="Timestamp" />
                <ScalarProperty Name="User" ColumnName="User" />
                <ScalarProperty Name="DisplayName" ColumnName="DisplayName" />
                <ScalarProperty Name="IDTableDetail" ColumnName="IDTableDetail" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="AuditTrailItems">
            <EntityTypeMapping TypeName="CloudMasterData.AuditTrailItem">
              <MappingFragment StoreEntitySet="AuditTrailItem">
                <ScalarProperty Name="IDAuditTrailItem" ColumnName="IDAuditTrailItem" />
                <ScalarProperty Name="IDAuditTrail" ColumnName="IDAuditTrail" />
                <ScalarProperty Name="FieldName" ColumnName="FieldName" />
                <ScalarProperty Name="OldValue" ColumnName="OldValue" />
                <ScalarProperty Name="NewValue" ColumnName="NewValue" />
                <ScalarProperty Name="DisplayName" ColumnName="DisplayName" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="BusinessSegments">
            <EntityTypeMapping TypeName="CloudMasterData.BusinessSegment">
              <MappingFragment StoreEntitySet="BusinessSegment">
                <ScalarProperty Name="IDBusinessSegment" ColumnName="IDBusinessSegment" />
                <ScalarProperty Name="Segment" ColumnName="Segment" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="CommunicationChannels">
            <EntityTypeMapping TypeName="CloudMasterData.CommunicationChannel">
              <MappingFragment StoreEntitySet="CommunicationChannel">
                <ScalarProperty Name="Type" ColumnName="Type" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Contracts">
            <EntityTypeMapping TypeName="CloudMasterData.Contract">
              <MappingFragment StoreEntitySet="Contract">
                <ScalarProperty Name="IDContract" ColumnName="IDContract" />
                <ScalarProperty Name="IDCustomer" ColumnName="IDCustomer" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="StartDate" ColumnName="StartDate" />
                <ScalarProperty Name="EndDate" ColumnName="EndDate" />
                <ScalarProperty Name="IDContractStatus" ColumnName="IDContractStatus" />
                <ScalarProperty Name="IDContractType" ColumnName="IDContractType" />
                <ScalarProperty Name="MaxUsers" ColumnName="MaxUsers" />
                <ScalarProperty Name="ExpirationWarningDays" ColumnName="ExpirationWarningDays" />
                <ScalarProperty Name="SerialNumberCount" ColumnName="SerialNumberCount" />
                <ScalarProperty Name="ContractNumber" ColumnName="ContractNumber" />
                <ScalarProperty Name="TimestampCreated" ColumnName="TimestampCreated" />
                <ScalarProperty Name="UserCreated" ColumnName="UserCreated" />
                <ScalarProperty Name="TimestampUpdated" ColumnName="TimestampUpdated" />
                <ScalarProperty Name="UserUpdated" ColumnName="UserUpdated" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ContractStatus">
            <EntityTypeMapping TypeName="CloudMasterData.ContractStatu">
              <MappingFragment StoreEntitySet="ContractStatus">
                <ScalarProperty Name="IDContractStatus" ColumnName="IDContractStatus" />
                <ScalarProperty Name="Name" ColumnName="Name" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ContractTypes">
            <EntityTypeMapping TypeName="CloudMasterData.ContractType">
              <MappingFragment StoreEntitySet="ContractType">
                <ScalarProperty Name="IDContractType" ColumnName="IDContractType" />
                <ScalarProperty Name="Name" ColumnName="Name" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Countries">
            <EntityTypeMapping TypeName="CloudMasterData.Country">
              <MappingFragment StoreEntitySet="Country">
                <ScalarProperty Name="Code" ColumnName="Code" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="NameSecLng" ColumnName="NameSecLng" />
                <ScalarProperty Name="GS1RNCode" ColumnName="GS1RNCode" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Customers">
            <EntityTypeMapping TypeName="CloudMasterData.Customer">
              <MappingFragment StoreEntitySet="Customer">
                <ScalarProperty Name="IDCustomer" ColumnName="IDCustomer" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="TimestampCreated" ColumnName="TimestampCreated" />
                <ScalarProperty Name="UserCreated" ColumnName="UserCreated" />
                <ScalarProperty Name="TimestampUpdated" ColumnName="TimestampUpdated" />
                <ScalarProperty Name="UserUpdated" ColumnName="UserUpdated" />
                <ScalarProperty Name="GS1CompanyPrefix" ColumnName="GS1CompanyPrefix" />
                <ScalarProperty Name="SGLN" ColumnName="SGLN" />
                <ScalarProperty Name="GLN" ColumnName="GLN" />
                <ScalarProperty Name="CustomerStreet" ColumnName="CustomerStreet" />
                <ScalarProperty Name="CustomerStreet2" ColumnName="CustomerStreet2" />
                <ScalarProperty Name="CustomerCity" ColumnName="CustomerCity" />
                <ScalarProperty Name="CustomerPostCode" ColumnName="CustomerPostCode" />
                <ScalarProperty Name="CustomerCountryCode" ColumnName="CustomerCountryCode" />
                <ScalarProperty Name="Code" ColumnName="Code" />
                <ScalarProperty Name="IDBusinessSegment" ColumnName="IDBusinessSegment" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="CustomerAggregationLevels">
            <EntityTypeMapping TypeName="CloudMasterData.CustomerAggregationLevel">
              <MappingFragment StoreEntitySet="CustomerAggregationLevel">
                <ScalarProperty Name="IDCustomerAggregationLevel" ColumnName="IDCustomerAggregationLevel" />
                <ScalarProperty Name="IDCustomer" ColumnName="IDCustomer" />
                <ScalarProperty Name="IDProduct" ColumnName="IDProduct" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="Type" ColumnName="Type" />
                <ScalarProperty Name="SerialType" ColumnName="SerialType" />
                <ScalarProperty Name="UnitCapacity" ColumnName="UnitCapacity" />
                <ScalarProperty Name="SubLevel" ColumnName="SubLevel" />
                <ScalarProperty Name="SubLevelItemType" ColumnName="SubLevelItemType" />
                <ScalarProperty Name="TimestampCreated" ColumnName="TimestampCreated" />
                <ScalarProperty Name="UserCreated" ColumnName="UserCreated" />
                <ScalarProperty Name="TimestampUpdated" ColumnName="TimestampUpdated" />
                <ScalarProperty Name="UserUpdated" ColumnName="UserUpdated" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="CustomerContracts">
            <EntityTypeMapping TypeName="CloudMasterData.CustomerContract">
              <MappingFragment StoreEntitySet="CustomerContract">
                <ScalarProperty Name="IDCustomerContract" ColumnName="IDCustomerContract" />
                <ScalarProperty Name="IDCustomer" ColumnName="IDCustomer" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="StartDate" ColumnName="StartDate" />
                <ScalarProperty Name="EndDate" ColumnName="EndDate" />
                <ScalarProperty Name="IsTrial" ColumnName="IsTrial" />
                <ScalarProperty Name="TrialEndDate" ColumnName="TrialEndDate" />
                <ScalarProperty Name="IsContract" ColumnName="IsContract" />
                <ScalarProperty Name="ContractTerm" ColumnName="ContractTerm" />
                <ScalarProperty Name="ContractData" ColumnName="ContractData" />
                <ScalarProperty Name="ContractNotifyEndDays" ColumnName="ContractNotifyEndDays" />
                <ScalarProperty Name="IsPayed" ColumnName="IsPayed" />
                <ScalarProperty Name="PaymentTerm" ColumnName="PaymentTerm" />
                <ScalarProperty Name="PaymentNotifyDays" ColumnName="PaymentNotifyDays" />
                <ScalarProperty Name="InvoiceData" ColumnName="InvoiceData" />
                <ScalarProperty Name="IsCanceled" ColumnName="IsCanceled" />
                <ScalarProperty Name="CancelReason" ColumnName="CancelReason" />
                <ScalarProperty Name="TimestampCreated" ColumnName="TimestampCreated" />
                <ScalarProperty Name="UserCreated" ColumnName="UserCreated" />
                <ScalarProperty Name="TimestampUpdated" ColumnName="TimestampUpdated" />
                <ScalarProperty Name="UserUpdated" ColumnName="UserUpdated" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="CustomerLabelDataSources">
            <EntityTypeMapping TypeName="CloudMasterData.CustomerLabelDataSource">
              <MappingFragment StoreEntitySet="CustomerLabelDataSource">
                <ScalarProperty Name="IDCustomerDataSource" ColumnName="IDCustomerDataSource" />
                <ScalarProperty Name="IDCustomer" ColumnName="IDCustomer" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="DataSource" ColumnName="DataSource" />
                <ScalarProperty Name="TimestampCreated" ColumnName="TimestampCreated" />
                <ScalarProperty Name="UserCreated" ColumnName="UserCreated" />
                <ScalarProperty Name="TimestampUpdated" ColumnName="TimestampUpdated" />
                <ScalarProperty Name="UserUpdated" ColumnName="UserUpdated" />
                <ScalarProperty Name="SrcSystemName" ColumnName="SrcSystemName" />
                <ScalarProperty Name="SrcSystem" ColumnName="SrcSystem" />
                <ScalarProperty Name="Unit" ColumnName="Unit" />
                <ScalarProperty Name="SerialType" ColumnName="SerialType" />
                <ScalarProperty Name="Item" ColumnName="Item" />
                <ScalarProperty Name="ItemSerialType" ColumnName="ItemSerialType" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="CustomerPartners">
            <EntityTypeMapping TypeName="CloudMasterData.CustomerPartner">
              <MappingFragment StoreEntitySet="CustomerPartner">
                <ScalarProperty Name="IDCustomerPartner" ColumnName="IDCustomerPartner" />
                <ScalarProperty Name="IDCustomer" ColumnName="IDCustomer" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="SystemName" ColumnName="SystemName" />
                <ScalarProperty Name="SNXRequest" ColumnName="SNXRequest" />
                <ScalarProperty Name="SSCCRequest" ColumnName="SSCCRequest" />
                <ScalarProperty Name="EBRImport" ColumnName="EBRImport" />
                <ScalarProperty Name="BatchReport" ColumnName="BatchReport" />
                <ScalarProperty Name="ShipmentReport" ColumnName="ShipmentReport" />
                <ScalarProperty Name="IsActive" ColumnName="IsActive" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="CustomerPartnerParameters">
            <EntityTypeMapping TypeName="CloudMasterData.CustomerPartnerParameter">
              <MappingFragment StoreEntitySet="CustomerPartnerParameter">
                <ScalarProperty Name="IDCustomerPartnerParameter" ColumnName="IDCustomerPartnerParameter" />
                <ScalarProperty Name="IDCustomerPartner" ColumnName="IDCustomerPartner" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="Value" ColumnName="Value" />
                <ScalarProperty Name="TimestampCreated" ColumnName="TimestampCreated" />
                <ScalarProperty Name="UserCreated" ColumnName="UserCreated" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="CustomerServices">
            <EntityTypeMapping TypeName="CloudMasterData.CustomerService">
              <MappingFragment StoreEntitySet="CustomerService">
                <ScalarProperty Name="IDCustomerService" ColumnName="IDCustomerService" />
                <ScalarProperty Name="IDCustomer" ColumnName="IDCustomer" />
                <ScalarProperty Name="IDService" ColumnName="IDService" />
                <ScalarProperty Name="IsDatabaseCustomerSeparation" ColumnName="IsDatabaseCustomerSeparation" />
                <ScalarProperty Name="DatabaseConnectionString" ColumnName="DatabaseConnectionString" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="InternalCodeTypes">
            <EntityTypeMapping TypeName="CloudMasterData.InternalCodeType">
              <MappingFragment StoreEntitySet="InternalCodeType">
                <ScalarProperty Name="Name" ColumnName="Name" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="LabelTemplates">
            <EntityTypeMapping TypeName="CloudMasterData.LabelTemplate">
              <MappingFragment StoreEntitySet="LabelTemplate">
                <ScalarProperty Name="IDLabelTemplate" ColumnName="IDLabelTemplate" />
                <ScalarProperty Name="IDCustomer" ColumnName="IDCustomer" />
                <ScalarProperty Name="Unit" ColumnName="Unit" />
                <ScalarProperty Name="Label" ColumnName="Label" />
                <ScalarProperty Name="LabelName" ColumnName="LabelName" />
                <ScalarProperty Name="TimestampCreated" ColumnName="TimestampCreated" />
                <ScalarProperty Name="UserCreated" ColumnName="UserCreated" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="MahTypes">
            <EntityTypeMapping TypeName="CloudMasterData.MahType">
              <MappingFragment StoreEntitySet="MahType">
                <ScalarProperty Name="IDMahType" ColumnName="IDMahType" />
                <ScalarProperty Name="Type" ColumnName="Type" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Manufacturers">
            <EntityTypeMapping TypeName="CloudMasterData.Manufacturer">
              <MappingFragment StoreEntitySet="Manufacturer">
                <ScalarProperty Name="IDMah" ColumnName="IDMah" />
                <ScalarProperty Name="IDCustomer" ColumnName="IDCustomer" />
                <ScalarProperty Name="IDMahType" ColumnName="IDMahType" />
                <ScalarProperty Name="MahID" ColumnName="MahID" />
                <ScalarProperty Name="MahName" ColumnName="MahName" />
                <ScalarProperty Name="MahStreet" ColumnName="MahStreet" />
                <ScalarProperty Name="MahStreet2" ColumnName="MahStreet2" />
                <ScalarProperty Name="MahCity" ColumnName="MahCity" />
                <ScalarProperty Name="MahPostCode" ColumnName="MahPostCode" />
                <ScalarProperty Name="MahCountryCode" ColumnName="MahCountryCode" />
                <ScalarProperty Name="TimeStampCreated" ColumnName="TimeStampCreated" />
                <ScalarProperty Name="UserCreated" ColumnName="UserCreated" />
                <ScalarProperty Name="TimeStampUpdated" ColumnName="TimeStampUpdated" />
                <ScalarProperty Name="UserUpdated" ColumnName="UserUpdated" />
                <ScalarProperty Name="GS1CompanyPrefix" ColumnName="GS1CompanyPrefix" />
                <ScalarProperty Name="GLN" ColumnName="GLN" />
                <ScalarProperty Name="SGLN" ColumnName="SGLN" />
                <ScalarProperty Name="IsActive" ColumnName="IsActive" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Products">
            <EntityTypeMapping TypeName="CloudMasterData.Product">
              <MappingFragment StoreEntitySet="Product">
                <ScalarProperty Name="IDProduct" ColumnName="IDProduct" />
                <ScalarProperty Name="IDServiceMasterRecord" ColumnName="IDServiceMasterRecord" />
                <ScalarProperty Name="IDCustomer" ColumnName="IDCustomer" />
                <ScalarProperty Name="CodeType" ColumnName="CodeType" />
                <ScalarProperty Name="Code" ColumnName="Code" />
                <ScalarProperty Name="InternalCodeType" ColumnName="InternalCodeType" />
                <ScalarProperty Name="InternalCode" ColumnName="InternalCode" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="CommonName" ColumnName="CommonName" />
                <ScalarProperty Name="Form" ColumnName="Form" />
                <ScalarProperty Name="PackType" ColumnName="PackType" />
                <ScalarProperty Name="PackSize" ColumnName="PackSize" />
                <ScalarProperty Name="Strength" ColumnName="Strength" />
                <ScalarProperty Name="IsActive" ColumnName="IsActive" />
                <ScalarProperty Name="TimestampCreated" ColumnName="TimestampCreated" />
                <ScalarProperty Name="UserCreated" ColumnName="UserCreated" />
                <ScalarProperty Name="TimestampUpdated" ColumnName="TimestampUpdated" />
                <ScalarProperty Name="UserUpdated" ColumnName="UserUpdated" />
                <ScalarProperty Name="SerialNumberSourceType" ColumnName="SerialNumberSourceType" />
                <ScalarProperty Name="SerializationType" ColumnName="SerializationType" />
                <ScalarProperty Name="IDCustomerPartner" ColumnName="IDCustomerPartner" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ProductAdditionalCodes">
            <EntityTypeMapping TypeName="CloudMasterData.ProductAdditionalCode">
              <MappingFragment StoreEntitySet="ProductAdditionalCode">
                <ScalarProperty Name="IDProductAdditionalCode" ColumnName="IDProductAdditionalCode" />
                <ScalarProperty Name="IDProduct" ColumnName="IDProduct" />
                <ScalarProperty Name="CodeType" ColumnName="CodeType" />
                <ScalarProperty Name="Code" ColumnName="Code" />
                <ScalarProperty Name="UserCreated" ColumnName="UserCreated" />
                <ScalarProperty Name="TimestampCreated" ColumnName="TimestampCreated" />
                <ScalarProperty Name="UserUpdated" ColumnName="UserUpdated" />
                <ScalarProperty Name="TimestampUpdated" ColumnName="TimestampUpdated" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ProductCodeTypes">
            <EntityTypeMapping TypeName="CloudMasterData.ProductCodeType">
              <MappingFragment StoreEntitySet="ProductCodeType">
                <ScalarProperty Name="CodeType" ColumnName="CodeType" />
                <ScalarProperty Name="Length" ColumnName="Length" />
                <ScalarProperty Name="Regex" ColumnName="Regex" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ProductLabels">
            <EntityTypeMapping TypeName="CloudMasterData.ProductLabel">
              <MappingFragment StoreEntitySet="ProductLabel">
                <ScalarProperty Name="IDProductLabel" ColumnName="IDProductLabel" />
                <ScalarProperty Name="IDCustomer" ColumnName="IDCustomer" />
                <ScalarProperty Name="IDProduct" ColumnName="IDProduct" />
                <ScalarProperty Name="LabelName" ColumnName="LabelName" />
                <ScalarProperty Name="Label" ColumnName="Label" />
                <ScalarProperty Name="Level" ColumnName="Level" />
                <ScalarProperty Name="IsDefault" ColumnName="IsDefault" />
                <ScalarProperty Name="IDTargetMarket" ColumnName="IDTargetMarket" />
                <ScalarProperty Name="TimestampCreated" ColumnName="TimestampCreated" />
                <ScalarProperty Name="UserCreated" ColumnName="UserCreated" />
                <ScalarProperty Name="PrintingSrcSystem" ColumnName="PrintingSrcSystem" />
                <ScalarProperty Name="PrintingSrcSystemName" ColumnName="PrintingSrcSystemName" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="SerializationTypes">
            <EntityTypeMapping TypeName="CloudMasterData.SerializationType">
              <MappingFragment StoreEntitySet="SerializationType">
                <ScalarProperty Name="IDSerializationType" ColumnName="IDSerializationType" />
                <ScalarProperty Name="SerializationType1" ColumnName="SerializationType" />
                <ScalarProperty Name="Regex" ColumnName="Regex" />
                <ScalarProperty Name="OrderDisplayList" ColumnName="OrderDisplayList" />
                <ScalarProperty Name="IsActive" ColumnName="IsActive" />
                <ScalarProperty Name="IsCarton" ColumnName="IsCarton" />
                <ScalarProperty Name="IsCase" ColumnName="IsCase" />
                <ScalarProperty Name="IsPallet" ColumnName="IsPallet" />
                <ScalarProperty Name="IsSerialsExpected" ColumnName="IsSerialsExpected" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="SerialNumberSourceTypes">
            <EntityTypeMapping TypeName="CloudMasterData.SerialNumberSourceType">
              <MappingFragment StoreEntitySet="SerialNumberSourceType">
                <ScalarProperty Name="IDSerialNumberSourceType" ColumnName="IDSerialNumberSourceType" />
                <ScalarProperty Name="IDService" ColumnName="IDService" />
                <ScalarProperty Name="Code" ColumnName="Code" />
                <ScalarProperty Name="Name" ColumnName="Name" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Services">
            <EntityTypeMapping TypeName="CloudMasterData.Service">
              <MappingFragment StoreEntitySet="Service">
                <ScalarProperty Name="IDService" ColumnName="IDService" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="SrcSystem" ColumnName="SrcSystem" />
                <ScalarProperty Name="SerializationType" ColumnName="SerializationType" />
                <ScalarProperty Name="IsCMD" ColumnName="IsCMD" />
                <ScalarProperty Name="IsServiceOwnProduct" ColumnName="IsServiceOwnProduct" />
                <ScalarProperty Name="IsServiceOwnCustomer" ColumnName="IsServiceOwnCustomer" />
                <ScalarProperty Name="ADURI" ColumnName="ADURI" />
                <ScalarProperty Name="ProductUpdateSql" ColumnName="ProductUpdateSql" />
                <ScalarProperty Name="CustomerUpdateSql" ColumnName="CustomerUpdateSql" />
                <ScalarProperty Name="IsServiceOwnManufacturer" ColumnName="IsServiceOwnManufacturer" />
                <ScalarProperty Name="ManufacturerUpdateSql" ColumnName="ManufacturerUpdateSql" />
                <ScalarProperty Name="UserUpdateSql" ColumnName="UserUpdateSql" />
                <ScalarProperty Name="IsExternalCodeAccepted" ColumnName="IsExternalCodeAccepted" />
                <ScalarProperty Name="ProductRegex" ColumnName="ProductRegex" />
                <ScalarProperty Name="IsUserDeleteAllowed" ColumnName="IsUserDeleteAllowed" />
                <ScalarProperty Name="IsServiceOwnLabel" ColumnName="IsServiceOwnLabel" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ServiceContracts">
            <EntityTypeMapping TypeName="CloudMasterData.ServiceContract">
              <MappingFragment StoreEntitySet="ServiceContract">
                <ScalarProperty Name="IDServiceContract" ColumnName="IDServiceContract" />
                <ScalarProperty Name="IDContract" ColumnName="IDContract" />
                <ScalarProperty Name="IDService" ColumnName="IDService" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ServiceEnvironments">
            <EntityTypeMapping TypeName="CloudMasterData.ServiceEnvironment">
              <MappingFragment StoreEntitySet="ServiceEnvironment">
                <ScalarProperty Name="IDServiceEnvironment" ColumnName="IDServiceEnvironment" />
                <ScalarProperty Name="IDService" ColumnName="IDService" />
                <ScalarProperty Name="Name" ColumnName="Name" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ServiceRoles">
            <EntityTypeMapping TypeName="CloudMasterData.ServiceRole">
              <MappingFragment StoreEntitySet="ServiceRole">
                <ScalarProperty Name="IDServiceRole" ColumnName="IDServiceRole" />
                <ScalarProperty Name="RoleName" ColumnName="RoleName" />
                <ScalarProperty Name="GroupName" ColumnName="GroupName" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ServiceUserLogins">
            <EntityTypeMapping TypeName="CloudMasterData.ServiceUserLogin">
              <MappingFragment StoreEntitySet="ServiceUserLogin">
                <ScalarProperty Name="IDServiceUserLogin" ColumnName="IDServiceUserLogin" />
                <ScalarProperty Name="IDService" ColumnName="IDService" />
                <ScalarProperty Name="LgnName" ColumnName="LgnName" />
                <ScalarProperty Name="ExternalSenderCode" ColumnName="ExternalSenderCode" />
                <ScalarProperty Name="ExternalRecieverCode" ColumnName="ExternalRecieverCode" />
                <ScalarProperty Name="IDManufacturer" ColumnName="IDManufacturer" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="TargetMarkets">
            <EntityTypeMapping TypeName="CloudMasterData.TargetMarket">
              <MappingFragment StoreEntitySet="TargetMarket">
                <ScalarProperty Name="IDTargetMarket" ColumnName="IDTargetMarket" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="ShortName" ColumnName="ShortName" />
                <ScalarProperty Name="GS1RNCode" ColumnName="GS1RNCode" />
                <ScalarProperty Name="TimestampCreated" ColumnName="TimestampCreated" />
                <ScalarProperty Name="UserCreated" ColumnName="UserCreated" />
                <ScalarProperty Name="TimestampUpdated" ColumnName="TimestampUpdated" />
                <ScalarProperty Name="UserUpdated" ColumnName="UserUpdated" />
                <ScalarProperty Name="SerializationType" ColumnName="SerializationType" />
                <ScalarProperty Name="SrcSystem" ColumnName="SrcSystem" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="UserLogins">
            <EntityTypeMapping TypeName="CloudMasterData.UserLogin">
              <MappingFragment StoreEntitySet="UserLogin">
                <ScalarProperty Name="LgnName" ColumnName="LgnName" />
                <ScalarProperty Name="IDCustomer" ColumnName="IDCustomer" />
                <ScalarProperty Name="IDManufacturer" ColumnName="IDManufacturer" />
                <ScalarProperty Name="IsADUser" ColumnName="IsADUser" />
                <ScalarProperty Name="TimestampCreated" ColumnName="TimestampCreated" />
                <ScalarProperty Name="UserCreated" ColumnName="UserCreated" />
                <ScalarProperty Name="TimestampUpdated" ColumnName="TimestampUpdated" />
                <ScalarProperty Name="UserUpdated" ColumnName="UserUpdated" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="UserLoginServiceRoles">
            <EntityTypeMapping TypeName="CloudMasterData.UserLoginServiceRole">
              <MappingFragment StoreEntitySet="UserLoginServiceRole">
                <ScalarProperty Name="IDUserLoginServiceRole" ColumnName="IDUserLoginServiceRole" />
                <ScalarProperty Name="LgnName" ColumnName="LgnName" />
                <ScalarProperty Name="IDServiceRole" ColumnName="IDServiceRole" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="UserPrinterSettings">
            <EntityTypeMapping TypeName="CloudMasterData.UserPrinterSetting">
              <MappingFragment StoreEntitySet="UserPrinterSetting">
                <ScalarProperty Name="LgnName" ColumnName="LgnName" />
                <ScalarProperty Name="UseNetworkPrinter" ColumnName="UseNetworkPrinter" />
                <ScalarProperty Name="NetworkPrinterIP" ColumnName="NetworkPrinterIP" />
                <ScalarProperty Name="NetworkPrinterPort" ColumnName="NetworkPrinterPort" />
                <ScalarProperty Name="PrinterDriverName" ColumnName="PrinterDriverName" />
                <ScalarProperty Name="PrinterLanguageType" ColumnName="PrinterLanguageType" />
                <ScalarProperty Name="TimestampCreated" ColumnName="TimestampCreated" />
                <ScalarProperty Name="TimestampUpdated" ColumnName="TimestampUpdated" />
                <ScalarProperty Name="UserCreated" ColumnName="UserCreated" />
                <ScalarProperty Name="UserUpdated" ColumnName="UserUpdated" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
        </EntityContainerMapping>
      </Mapping>
    </edmx:Mappings>
  </edmx:Runtime>
  <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <Connection>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="MetadataArtifactProcessing" Value="EmbedInOutputAssembly" />
      </DesignerInfoPropertySet>
    </Connection>
    <Options>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="ValidateOnBuild" Value="true" />
        <DesignerProperty Name="EnablePluralization" Value="true" />
        <DesignerProperty Name="IncludeForeignKeysInModel" Value="true" />
        <DesignerProperty Name="UseLegacyProvider" Value="false" />
        <DesignerProperty Name="CodeGenerationStrategy" Value="None" />
      </DesignerInfoPropertySet>
    </Options>
    <!-- Diagram content (shape and connector positions) -->
    <Diagrams></Diagrams>
  </Designer>
</edmx:Edmx>