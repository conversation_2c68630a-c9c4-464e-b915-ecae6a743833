﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureAmModels
{
    [Table("BufferHistory")]
    public class BufferHistory
    {

        public BufferHistory()
        {
            PoolInfos = new HashSet<PoolInfo>();
        }

        [Key]
        public int IDBufferHistory { get; set; }

        [Required]
        public int IDOrderHistory { get; set; }

        [DisplayName("Code")]
        public string GTIN { get; set; }

        public string OmsID { get; set; }

        public string OrderID { get; set; }

        [Required]
        [DisplayName("Buffer status")]
        [StringLength(50)]
        public BufferStatusEnum BufferStatus { get; set; }

        public bool? PoolExhausted { get; set; }

        [DisplayName("Total codes")]
        public int? TotalCodes { get; set; }

        [DisplayName("Unavailable codes")]
        public int? UnavailableCodes { get; set; }

        [DisplayName("Available codes")]
        public int? AvailableCodes { get; set; }

        [DisplayName("Left in buffer")]
        public int? LeftInBuffer { get; set; }

        [DisplayName("Total passed")]
        public int? TotalPassed { get; set; }

        [DisplayName("Rejection reason")]
        public string RejectionReason { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        [DisplayName("Created")]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        [DisplayName("User Created")]
        public string UserCreated { get; set; }

        [InverseProperty("BuffersHistory")]
        public OrderHistory OrderHistory { get; set; }

        [InverseProperty("BufferHistory")]
        public ICollection<PoolInfo> PoolInfos { get; set; }
    }
}
