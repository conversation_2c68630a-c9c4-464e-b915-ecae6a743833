﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SGAuditTrail;

namespace AzureCmdModels
{
    [Table("UserLogin")]
    [Audit(DisplayName = "User")]
    public class UserLogin
    {
        public UserLogin()
        {
            UserLoginServiceRoles = new HashSet<UserLoginServiceRole>();
            ServiceUserLogins = new HashSet<ServiceUserLogin>();
        }

        [Key]
        [StringLength(50)]
        [Audit(DisplayName = "Login Name")]
        [Required]
        [DisplayName("Login name")]
        public string LgnName { get; set; }

        [Required]
        [Column("IDCustomer")]
        [DisplayName("Customer")]
        [Audit(DisplayName = "Customer", InversePropertyName = "Customer", InversePropertyValue = "Name")]
        public int IDCustomer { get; set; }

        [Column("IDManufacturer")]
        [DisplayName("Manufacturer")]
        [Audit(DisplayName = "Manufacturer", InversePropertyName = "Manufacturer", InversePropertyValue = "MahName", NullValue = "No Manufacturer")]
        public int? IDManufacturer { get; set; } //todo del

        [DisplayName("Active Directory User")]
        [Audit(DisplayName = "IsADUser", ValueTrue = "Yes", ValueFalse = "No")]
        public bool IsADUser { get; set; }

        /*  [DisplayName("Service Account")]
          [Audit(DisplayName = "Service Account", ValueTrue = "Yes", ValueFalse = "No")]
          public bool IsServiceAccount { get; set; } */

        [Column(TypeName = "datetime")]
        [DisplayName("Created")]
        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampCreated { get; set; }

        [StringLength(128)]
        [DisplayName("User Created")]
        public string UserCreated { get; set; }

        [Column(TypeName = "datetime")]
        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        [InverseProperty("UserLogins")]
        public Customer Customer { get; set; }

        [InverseProperty("UserLogins")]
        public Manufacturer Manufacturer { get; set; }

        [InverseProperty("UserLogin")]
        public ICollection<UserLoginServiceRole> UserLoginServiceRoles { get; set; }

        [InverseProperty("UserLogin")]
        public ICollection<ServiceUserLogin> ServiceUserLogins { get; set; }




        public AuditTrailHeader GetAuditTrailHeader(string user, int page, int pages)
        {
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

            AuditTrailHeader header = new AuditTrailHeader()
            {
                Controler = "UserLogins",
                ID = LgnName,
                UserName = user,
                Page = page,
                Pages = pages
            };

            header.AddItem("Login Name", LgnName);
            header.AddItem("User", user);
            header.AddItem("Timestamp", timestamp);

            return header;
        }


        private string _principalError { get; set; }
        private bool _hasPrincipalError { get; set; }

        public bool HasPrincipleError
        {
            get
            {
                return _hasPrincipalError;
            }

        }

        public string PrincipalError
        {
            get
            {
                if (_hasPrincipalError)
                    return _principalError;
                else
                    return "No Error";
            }
        }



        public void SetValuesFromViewModel(UserLoginServiceRoleViewModel model, string lgnName)
        {
            this.TimestampUpdated = DateTime.Now;
            this.UserUpdated = lgnName;
            this.IsADUser = model.UserLogin.IsADUser;
            this.IDManufacturer = model.UserLogin.IDManufacturer;

        }
    }
}
