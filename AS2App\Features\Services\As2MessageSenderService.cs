using AS2Test_master.Models;
using AS2Test_master.Features.Services.MimePayload;
using Microsoft.Extensions.Options;
using AS2Test.Models;
using AS2Test.Features.Services.MimePayload;
using AS2Test.Features.Services.MdnHandler;
using AS2Test.Features.Services;
using System.Text;
using AS2Test.Features.Services.MessageCreator;
using AS2Test_master.Features.Services.Certificates;
using AS2App.Models.ConfigModels;
using AS2App.Features.Services.MicHandler;

namespace AS2App.Features.Services
{
    /// <summary>
    /// Service for sending AS2 messages.
    /// </summary>
    public class As2MessageSenderService : IAs2MessageSenderService
    {
        private readonly IMdnDecryptionVerificationService _mdnDecryptionVerificationService;
        private readonly IMessageCreatorService _messageCreatorService;
        private readonly ILogger<As2MessageSenderService> _logger;
        private readonly HttpClient _httpClient;
        private readonly IMicHandlerService _micHandlerService;


        /// <summary>
        /// Initializes a new instance of the AS2MessageSendService class.
        /// </summary>
        /// <param name="mdnDecryptionVerificationService">The MDN decryption and verification service.</param>
        /// <param name="logger">The logger.</param>
        /// <param name="httpClient">The HTTP client.</param>
        public As2MessageSenderService(IMdnDecryptionVerificationService mdnDecryptionVerificationService,
            IMessageCreatorService messageCreatorService,
            ILogger<As2MessageSenderService> logger,
            HttpClient httpClient,
            IMicHandlerService micHandlerService)
        {
            _mdnDecryptionVerificationService = mdnDecryptionVerificationService ?? throw new ArgumentNullException(nameof(mdnDecryptionVerificationService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            _messageCreatorService = messageCreatorService ?? throw new ArgumentNullException(nameof(messageCreatorService));
            _micHandlerService = micHandlerService ?? throw new ArgumentNullException(nameof(micHandlerService));
        }

        /// <summary>
        /// Sends a file asynchronously using AS2 protocol.
        /// </summary>
        /// <param name="messageRequest">The message request.</param>
        /// <returns>A task representing the asynchronous operation with a service response.</returns>
        public async Task<ServiceResponse<As2ServerResponse<MdnVerificationResult>>> SendFileAsync(MessageRequest messageRequest)
        {
            var serviceResponse = new ServiceResponse<As2ServerResponse<MdnVerificationResult>>();

            try
            {
                ValidateMessageRequest(messageRequest);


                ByteArrayContent httpPayload = await _messageCreatorService.CreateAs2MessageAsync(messageRequest);

                if (httpPayload == null)
                {
                    throw new InvalidOperationException("Failed to create AS2 message payload.");
                }


                return await SendRequestAndProcessResponse(httpPayload, messageRequest);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending AS2 message: {ErrorMessage}", ex.Message);
                serviceResponse.IsSuccess = false;
                serviceResponse.ErrorMessage = ex.Message;
                return serviceResponse;
            }
        }

        /// <summary>
        /// Validates the message request.
        /// </summary>
        /// <param name="messageRequest">The message request to validate.</param>
        /// <exception cref="ArgumentNullException">Thrown when required elements are null.</exception>
        /// <exception cref="ArgumentException">Thrown when the request doesn't meet requirements.</exception>
        private void ValidateMessageRequest(MessageRequest messageRequest)
        {
            // Check if message request is null
            if (messageRequest == null)
            {
                throw new ArgumentNullException(nameof(messageRequest));
            }

            // Validate required fields
            if (string.IsNullOrEmpty(messageRequest.AsFrom))
            {
                throw new ArgumentException("AsFrom cannot be null or empty", nameof(messageRequest));
            }

            if (string.IsNullOrEmpty(messageRequest.AsTo))
            {
                throw new ArgumentException("AsTo cannot be null or empty", nameof(messageRequest));
            }

            if (string.IsNullOrEmpty(messageRequest.MessageId))
            {
                throw new ArgumentException("MessageId cannot be null or empty", nameof(messageRequest));
            }

            // Validate signing requirements
            if (messageRequest.IsSigned)
            {
                if (!messageRequest.SigningAlgorithm.HasValue)
                {
                    throw new ArgumentException("SigningAlgorithm must be specified when IsSigned is true",
                        nameof(messageRequest.SigningAlgorithm));
                }

                if (string.IsNullOrEmpty(messageRequest.SenderCert))
                {
                    throw new ArgumentException(
                        "Either SigningCertFilename or SenderCert must be specified when IsSigned is true",
                        nameof(messageRequest));
                }
            }

            // Validate encryption requirements
            if (messageRequest.IsEncrypted)
            {
                if (!messageRequest.EncryptionAlgorithm.HasValue)
                {
                    throw new ArgumentException("EncryptionAlgorithm must be specified when IsEncrypted is true",
                        nameof(messageRequest.EncryptionAlgorithm));
                }

                if (string.IsNullOrEmpty(messageRequest.ReceiverCert))
                {
                    throw new ArgumentException(
                        " ReceiverCert must be specified when IsEncrypted is true",
                        nameof(messageRequest));
                }
                if (string.IsNullOrEmpty(messageRequest.PrivateCertificatePassword))
                {
                    throw new ArgumentException(
                    "PrivateCertificatePassword must be specified when IsEncrypted is true",
                        nameof(messageRequest));
                }

            }

            // Validate compression requirements
            if (messageRequest.IsCompressed && !messageRequest.CompressionType.HasValue)
            {
                throw new ArgumentException("CompressionType must be specified when IsCompressed is true",
                    nameof(messageRequest.CompressionType));
            }

            // Validate MDN options if required
            if (messageRequest.MdnOption?.IsMdnRequired == true)
            {
                if (messageRequest.MdnOption.IsSignedReceiptProtocolRequired &&
                    messageRequest.MdnOption.MessageIntegrityCheckAlgorithm == default)
                {
                    throw new ArgumentException(
                        "MessageIntegrityCheckAlgorithm must be specified when IsSignedReceiptProtocolRequired is true",
                        nameof(messageRequest.MdnOption));
                }
            }
        }

        private async Task<ServiceResponse<As2ServerResponse<MdnVerificationResult>>> SendRequestAndProcessResponse(ByteArrayContent httpPayload, MessageRequest messageRequest)
        {
            var serviceResponse = new ServiceResponse<As2ServerResponse<MdnVerificationResult>>();

            if (httpPayload == null)
            {
                throw new ArgumentNullException(nameof(httpPayload));
            }

            if (messageRequest == null)
            {
                throw new ArgumentNullException(nameof(messageRequest));
            }

            try
            {
                HttpRequestMessage request = CreateHttpRequest(httpPayload, messageRequest);

                // Send the HTTP request and process the response
                var httpMessageLog = await LogHttpRequestMessageAsync(request);


                _logger.LogInformation("======= Http Request Message ======= \n {HttpMessageLog}", httpMessageLog);


                var httpResponse = await _httpClient.SendAsync(request);

                string responseContent = await httpResponse.Content.ReadAsStringAsync();

                if (httpResponse.IsSuccessStatusCode)
                {

                    var decryptedMdn = await _mdnDecryptionVerificationService.DecryptAndVerify(httpResponse, messageRequest.ReceiverCert, messageRequest.SenderCert)
                        .ConfigureAwait(false);


                    serviceResponse.As2ServerResponse = new As2ServerResponse<MdnVerificationResult>
                    {
                        StatusCode = httpResponse.StatusCode.ToString(),
                        ReasonPhrase = httpResponse.ReasonPhrase,
                        IsSuccessStatusCode = httpResponse.IsSuccessStatusCode,
                        Message = decryptedMdn
                        
                    };

                    _logger.LogInformation("\n ======AS2 server response =========:  \n {StatusCode}, Reason: {ReasonPhrase}, Content: {Content}",
                      httpResponse.StatusCode, httpResponse.ReasonPhrase, responseContent);

                    serviceResponse.Message = responseContent;
                    serviceResponse.IsSuccess = true;

                    return serviceResponse;
                }
                else
                {
 
                    var decryptedMdn = await _mdnDecryptionVerificationService.DecryptAndVerify(httpResponse, messageRequest.ReceiverCert, messageRequest.SenderCert)
                        .ConfigureAwait(false);

                    var problem = await httpResponse.Content.ReadAsStringAsync();

                    _logger.LogWarning($"as2 server headers response : {GetHttpResponseHeaders(request, httpResponse)}");
                    _logger.LogWarning($"as2 server content response : {problem}");

                    serviceResponse.IsSuccess = false;
                    serviceResponse.ErrorMessage = problem;
                    _logger.LogWarning("AS2 server returned non-success status code: {StatusCode}, Reason: {ReasonPhrase}, Content: {Content}",
                      httpResponse.StatusCode, httpResponse.ReasonPhrase, responseContent);

                    return serviceResponse;
                }
            }

            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "HTTP request failed: {ErrorMessage}", ex.Message);
                serviceResponse.IsSuccess = false;
                serviceResponse.ErrorMessage = $"HTTP request failed: {ex.Message}";
                return serviceResponse;
            }
            catch (TaskCanceledException ex)
            {
                _logger.LogError(ex, "Request timed out: {ErrorMessage}", ex.Message);
                serviceResponse.IsSuccess = false;
                serviceResponse.ErrorMessage = "Request timed out";
                return serviceResponse;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error during HTTP request: {ErrorMessage}", ex.Message);
                serviceResponse.IsSuccess = false;
                serviceResponse.ErrorMessage = $"Unexpected error: {ex.Message}";
                return serviceResponse;
            }

        }

        private static HttpRequestMessage CreateHttpRequest(ByteArrayContent httpPayload, MessageRequest messageRequest)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, messageRequest.ServerUri)
            {
                Content = httpPayload
            };

            request.Headers.ExpectContinue = true;

            request.Headers.TryAddWithoutValidation("AS2-Version", "1.2");
            request.Headers.TryAddWithoutValidation("Mime-Version", "1.0");
            request.Headers.TryAddWithoutValidation("AS2-From", messageRequest.AsFrom);
            request.Headers.TryAddWithoutValidation("AS2-To", messageRequest.AsTo);
            request.Headers.TryAddWithoutValidation("Subject", messageRequest.Subject ?? "AS2 message");
            request.Headers.TryAddWithoutValidation("Message-Id", $"<{messageRequest.MessageId}>");
            request.Headers.TryAddWithoutValidation("Recipient-Address", messageRequest.ServerUri.ToString());
            request.Headers.TryAddWithoutValidation("From", "<EMAIL>");
            request.Headers.TryAddWithoutValidation("Host", $"{messageRequest.ServerUri.Host}:{messageRequest.ServerUri.Port}");
            request.Headers.TryAddWithoutValidation("Connection", "close");
            request.Headers.TryAddWithoutValidation("User-Agent", "AS2 Client");

            // Optional multipart header
            if (messageRequest.IsMultipart)
            {
                request.Headers.TryAddWithoutValidation("EDIINT-Features", "multiple-attachments");
            }

            // Required if expecting an MDN
            request.Headers.TryAddWithoutValidation("Disposition-Notification-To", messageRequest.AsFrom);

            // Match signature algorithm if signed
            if (messageRequest.IsSigned)
            {
                var mdnOption = messageRequest.MdnOption;

                string mdnRequired = mdnOption.IsSignedReceiptProtocolRequired ? "required" : "optional";
                string signedReceiptMicAlg = mdnOption.IsSignedReceiptProtocolRequired
                    ? $"required,{mdnOption.MessageIntegrityCheckAlgorithm.ToString().ToLower()}"
                    : $"optional,{mdnOption.MessageIntegrityCheckAlgorithm.ToString().ToLower()}";

                request.Headers.Add("Disposition-Notification-Options",
                    $"signed-receipt-protocol={mdnRequired}, pkcs7-signature; signed-receipt-micalg={signedReceiptMicAlg}");


            }
            return request;

        }

        private string GetHttpResponseHeaders(HttpRequestMessage request, HttpResponseMessage httpResponse)
        {
            try
            {
                var sb = new StringBuilder();

                // Request line
                sb.AppendLine($"{request.Method} {request.RequestUri} HTTP/{request.Version}");

                // Headers
                foreach (var header in httpResponse.Headers)
                {
                    sb.AppendLine($"{header.Key}: {string.Join(", ", header.Value)}");
                }

                return sb.ToString();
            }
            catch (Exception e)
            {
                _logger.LogError("GetHttpResponseHeaders Error: " + e.Message + " " + e.InnerException?.Message);
                throw;
            }
        }

        public async Task<string> LogHttpRequestMessageAsync(HttpRequestMessage request)
        {
            try
            {
                var sb = new StringBuilder();

                // Request line
                sb.AppendLine($"{request.Method} {request.RequestUri} HTTP/{request.Version}");

                // Headers
                foreach (var header in request.Headers)
                {
                    sb.AppendLine($"{header.Key}: {string.Join(", ", header.Value)}");
                }

                foreach (var header in _httpClient.DefaultRequestHeaders)
                {
                    sb.AppendLine($"{header.Key}: {string.Join(", ", header.Value)}");
                }


                if (request.Content != null)
                {
                    foreach (var header in request.Content.Headers)
                    {
                        sb.AppendLine($"{header.Key}: {string.Join(", ", header.Value)}");
                    }

                    // Content
                    string body = await request.Content.ReadAsStringAsync();
                    sb.AppendLine();
                    sb.AppendLine(body);
                }

                return sb.ToString();
            }
            catch (Exception e)
            {
                _logger.LogError("HttpLogHelper Error: " + e.Message + " " + e.InnerException?.Message);
                throw;
            }
        }

    }
}