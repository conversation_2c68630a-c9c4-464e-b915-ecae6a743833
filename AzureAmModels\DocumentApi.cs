﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureAmModels
{
    public class ApiUtilisationReportResult
    {
        //public List<UZB_PharmaConnector.Json.UtilisationResponse> OmsResponses { get; set; }
        public List<ApiError> Errors { get; set; }
    }
    public class ApiError
    {
        public string Level { get; set; }

        public string Error { get; set; }

        public ApiError GetException(Exception exception = null, string level = "Exception")
        {
            ApiError result = new ApiError
            {
                Level = level,
                Error = "Unknown error"
            };

            if (exception != null)
            {
                result.Error = exception.Message;
            }

            return result;
        }

        public ApiError Create(string error = "Unknown error", string level = "General")
        {
            return new ApiError
            {
                Error = error,
                Level = level
            };
        }
    }
}
