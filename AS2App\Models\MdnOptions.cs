
using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using AS2Test_master.Models;

namespace AS2Test.Models
{
    /// <summary>
    /// Represents the options for Message Disposition Notification (MDN).
    /// </summary>
    public class MdnOptions
    {
        [Required]
        public bool IsMdnRequired { get; set; }
        public bool IsMdnAsync { get; set; }
        public bool IsSignedReceiptProtocolRequired { get; set; }
        public bool IsSignedReceiptMicAlgRequired { get; set; }
        /// <summary>
        /// Gets or sets the message integrity check algorithm for the MDN options.
        /// </summary>
        [AllowNull]
        [JsonConverter(typeof(JsonStringEnumConverter))] 
        public MessageIntegrityCheckAlgorithms MessageIntegrityCheckAlgorithm { get; set; }
    }
}