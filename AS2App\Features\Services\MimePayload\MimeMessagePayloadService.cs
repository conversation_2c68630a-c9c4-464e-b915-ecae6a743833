using AS2Test_master.Models;
using MimeKit;

namespace AS2Test.Features.Services.MimePayload
{
    public class MimeMessagePayloadService : IMimeMessagePayloadService
    {

    /// <summary>
    /// Initializes a new instance of the <see cref="MimeMessagePayloadService"/> class.
    /// </summary>
    /// <param name="encryptionService">The encryption service used for AS2 encryption operations.</param>
    /// <exception cref="ArgumentNullException">Thrown when the <paramref name="encryptionService"/> is null.</exception>
    public MimeMessagePayloadService()
    {
    }

    public async Task<MimeMessage> CreateMultipartMessage(MessageRequest messageRequest)
    {
        return await new MimeMessageBuilder(messageRequest)
            .AddCommonHeaders()
            .AddMdnOptions()
            .AddMultipartContent()
            .BuildAsync();
    }

    public async Task<MimeMessage> CreateMimeContent(MessageRequest messageRequest, string fileName, TransferEncodings transferEncoding)
    {
        return await new MimeMessageBuilder(messageRequest)
            .AddCommonHeaders()
            .AddMdnOptions()
            .AddSingleContent()
            .BuildAsync();
    }
    }
}