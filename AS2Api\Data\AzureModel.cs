﻿namespace AS2Api.Data
{
    public static class ApiScopes
    {
        public static readonly string[] EbrScopesRequiredByApi = new string[]
        {
            "EbrAccessAsUser",
            "access_as_service" //TODO fix when available
        };

    }

    public class ADGroups
    {
        public const string Admin = @"SATT\Group SGCloud Admin,87be55f7-cef8-4d74-8942-3ac0dff8890b,SG\Department RND";
        public const string User = @"SATT\Group SGCloud User,70d39629-2c73-4cc0-80e5-52525bb70195,SG\Department RND";
        public const string WebApi = @"SATT\Group SGCloud WebAPI,e3f7c22b-61db-4e81-87ef-f39a0e762c00,SG\Department RND";
    }

    public class AzureModel
    {
        public string Instance { get; set; }
        public string ClientId { get; set; }
        public string ClientSecret { get; set; }
        public string Domain { get; set; }
        public string TenantId { get; set; }
    }
}
