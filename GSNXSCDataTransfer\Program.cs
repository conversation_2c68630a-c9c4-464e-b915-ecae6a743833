using GSNXSCDataTransfer.CustomModels;
using Microsoft.Data.SqlClient;
using System.Xml.Serialization;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace GSNXSCDataTransfer
{
    class Program
    {
        public static void Main(string[] args)
        {
            try
            {
                Main main = new();

                main.TransferSCSerials(args).Wait();
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
                if (e.InnerException != null)
                    Console.WriteLine(e.InnerException.Message);

            }

        }

    }
}
