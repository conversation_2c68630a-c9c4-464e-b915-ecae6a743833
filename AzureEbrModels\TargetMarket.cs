﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEbrModels
{
    [Table("TargetMarket")]
    public class TargetMarket
    {
        public TargetMarket()
        {
            Batches = new HashSet<Batch>();
        }

        [Key]
        public int IDTargetMarket { get; set; }

        [Required]
        public int? IDCMDTargetMarket { get; set; }

        [Required]
        [StringLength(128)]
        public string Name { get; set; }

        [Required]
        [StringLength(4)]
        public string ShortName { get; set; }

        public int? GS1RNCode { get; set; }

        [StringLength(50)]
        public string SerializationType { get; set; }

        [StringLength(2)]
        public string SrcSystem { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        [InverseProperty("TargetMarket")]
        public ICollection<Batch> Batches { get; set; }
    }
}
