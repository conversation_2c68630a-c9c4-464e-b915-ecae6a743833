{"version": 2, "dgSpecHash": "gvfKlSS/87s=", "success": true, "projectFilePath": "S:\\EUHub\\Dev\\SATT_PLATFORM\\AS2Api\\AS2Api.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\azure.core\\1.35.0\\azure.core.1.35.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.identity\\1.10.3\\azure.identity.1.10.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.security.keyvault.certificates\\4.1.0\\azure.security.keyvault.certificates.4.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.security.keyvault.secrets\\4.1.0\\azure.security.keyvault.secrets.4.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authentication.jwtbearer\\8.0.7\\microsoft.aspnetcore.authentication.jwtbearer.8.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authentication.openidconnect\\8.0.7\\microsoft.aspnetcore.authentication.openidconnect.8.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.cryptography.internal\\8.0.0-rc.2.23480.2\\microsoft.aspnetcore.cryptography.internal.8.0.0-rc.2.23480.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.dataprotection\\8.0.0-rc.2.23480.2\\microsoft.aspnetcore.dataprotection.8.0.0-rc.2.23480.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.dataprotection.abstractions\\8.0.0-rc.2.23480.2\\microsoft.aspnetcore.dataprotection.abstractions.8.0.0-rc.2.23480.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.asyncinterfaces\\1.1.1\\microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.data.sqlclient\\5.1.5\\microsoft.data.sqlclient.5.1.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.data.sqlclient.sni.runtime\\5.1.1\\microsoft.data.sqlclient.sni.runtime.5.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore\\8.0.7\\microsoft.entityframeworkcore.8.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.abstractions\\8.0.7\\microsoft.entityframeworkcore.abstractions.8.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.analyzers\\8.0.7\\microsoft.entityframeworkcore.analyzers.8.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.relational\\8.0.7\\microsoft.entityframeworkcore.relational.8.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.sqlserver\\8.0.7\\microsoft.entityframeworkcore.sqlserver.8.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.apidescription.server\\6.0.5\\microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.abstractions\\8.0.0\\microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.memory\\8.0.0\\microsoft.extensions.caching.memory.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\8.0.0\\microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.binder\\8.0.0-rc.2.23479.6\\microsoft.extensions.configuration.binder.8.0.0-rc.2.23479.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\8.0.0\\microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\8.0.0\\microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.diagnostics.abstractions\\8.0.0-rc.2.23479.6\\microsoft.extensions.diagnostics.abstractions.8.0.0-rc.2.23479.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.abstractions\\8.0.0-rc.2.23479.6\\microsoft.extensions.fileproviders.abstractions.8.0.0-rc.2.23479.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.hosting.abstractions\\8.0.0-rc.2.23479.6\\microsoft.extensions.hosting.abstractions.8.0.0-rc.2.23479.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.http\\3.1.3\\microsoft.extensions.http.3.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging\\8.0.0\\microsoft.extensions.logging.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\8.0.0\\microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\8.0.0\\microsoft.extensions.options.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options.configurationextensions\\8.0.0-rc.2.23479.6\\microsoft.extensions.options.configurationextensions.8.0.0-rc.2.23479.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\8.0.0\\microsoft.extensions.primitives.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identity.abstractions\\5.0.0\\microsoft.identity.abstractions.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identity.client\\4.65.0\\microsoft.identity.client.4.65.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identity.client.extensions.msal\\4.56.0\\microsoft.identity.client.extensions.msal.4.56.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identity.web\\2.15.2\\microsoft.identity.web.2.15.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identity.web.certificate\\2.15.2\\microsoft.identity.web.certificate.2.15.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identity.web.certificateless\\2.15.2\\microsoft.identity.web.certificateless.2.15.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identity.web.diagnostics\\2.15.2\\microsoft.identity.web.diagnostics.2.15.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identity.web.downstreamapi\\2.15.2\\microsoft.identity.web.downstreamapi.2.15.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identity.web.tokenacquisition\\2.15.2\\microsoft.identity.web.tokenacquisition.2.15.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identity.web.tokencache\\2.15.2\\microsoft.identity.web.tokencache.2.15.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.abstractions\\7.1.2\\microsoft.identitymodel.abstractions.7.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.jsonwebtokens\\7.1.2\\microsoft.identitymodel.jsonwebtokens.7.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.logging\\7.1.2\\microsoft.identitymodel.logging.7.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.loggingextensions\\7.0.3\\microsoft.identitymodel.loggingextensions.7.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols\\7.1.2\\microsoft.identitymodel.protocols.7.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols.openidconnect\\7.1.2\\microsoft.identitymodel.protocols.openidconnect.7.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.tokens\\7.1.2\\microsoft.identitymodel.tokens.7.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.validators\\7.0.3\\microsoft.identitymodel.validators.7.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.openapi\\1.2.3\\microsoft.openapi.1.2.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.sqlserver.server\\1.0.0\\microsoft.sqlserver.server.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.systemevents\\6.0.0\\microsoft.win32.systemevents.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore\\6.4.0\\swashbuckle.aspnetcore.6.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore.swagger\\6.4.0\\swashbuckle.aspnetcore.swagger.6.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore.swaggergen\\6.4.0\\swashbuckle.aspnetcore.swaggergen.6.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore.swaggerui\\6.4.0\\swashbuckle.aspnetcore.swaggerui.6.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.configuration.configurationmanager\\6.0.1\\system.configuration.configurationmanager.6.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.diagnosticsource\\8.0.0-rc.2.23479.6\\system.diagnostics.diagnosticsource.8.0.0-rc.2.23479.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.drawing.common\\6.0.0\\system.drawing.common.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.formats.asn1\\8.0.0-rc.2.23479.6\\system.formats.asn1.8.0.0-rc.2.23479.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.identitymodel.tokens.jwt\\7.1.2\\system.identitymodel.tokens.jwt.7.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.filesystem.accesscontrol\\5.0.0\\system.io.filesystem.accesscontrol.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.4\\system.memory.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory.data\\1.0.2\\system.memory.data.1.0.2.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.numerics.vectors\\4.5.0\\system.numerics.vectors.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.caching\\6.0.0\\system.runtime.caching.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\6.0.0\\system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.accesscontrol\\6.0.0\\system.security.accesscontrol.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.cng\\5.0.0\\system.security.cryptography.cng.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.pkcs\\8.0.0-rc.2.23479.6\\system.security.cryptography.pkcs.8.0.0-rc.2.23479.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.protecteddata\\6.0.0\\system.security.cryptography.protecteddata.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.xml\\8.0.0-rc.2.23479.6\\system.security.cryptography.xml.8.0.0-rc.2.23479.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.permissions\\6.0.0\\system.security.permissions.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal.windows\\5.0.0\\system.security.principal.windows.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding.codepages\\6.0.0\\system.text.encoding.codepages.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encodings.web\\8.0.0-rc.2.23479.6\\system.text.encodings.web.8.0.0-rc.2.23479.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\4.7.2\\system.text.json.4.7.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.extensions\\4.5.4\\system.threading.tasks.extensions.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.windows.extensions\\6.0.0\\system.windows.extensions.6.0.0.nupkg.sha512"], "logs": []}