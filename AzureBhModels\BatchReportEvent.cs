﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureBhModels
{
    [Table("BatchReportEvent")]
    public class BatchReportEvent
    {
        public BatchReportEvent()
        {
        }

        [Key]
        public int IDBatchReportEvent{ get; set; }

        public int IDBatchReport { get; set; }

        [Required]
        public Guid EventID { get ; set; }

        [Required]
        public EventType EventType { get; set; }

        [StringLength(256)]
        public string EventDescription { get; set; }


        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime EventTimestamp { get; set; }        
        
        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [InverseProperty("BatchReportEvents")]
        public BatchReport BatchReport { get; set; }
    }
    public enum EventType
    {
        Commisioning,
        Aggregation,
        Shipment
    }
}
