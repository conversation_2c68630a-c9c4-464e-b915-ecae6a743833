﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureAmModels
{
    [Table("AuditTrail")]
    public class AuditTrail
    {
        [Key]
        public int IDAuditTrail { get; set; }

        [Required]
        [StringLength(50)]
        public string TableName { get; set; }

        public int IDTable { get; set; }

        [Required]
        [StringLength(50)]
        public string Scope { get; set; }

        [Required]
        [StringLength(50)]
        public string Action { get; set; }

        [StringLength(50)]
        public string Property { get; set; }

        [StringLength(500)]
        public string NewValue { get; set; }

        [StringLength(500)]
        public string OldValue { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

    }
}
