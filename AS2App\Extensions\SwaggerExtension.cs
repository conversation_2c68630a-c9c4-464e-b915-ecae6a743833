using System.Reflection;
using AS2App.Models.ConfigModels;
using AS2Test_master.Helpers;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Microsoft.OpenApi.Models;

namespace AS2Test_master.Extensions
{
    public static class SwaggerExtension
    {
        public static void AddSwagger(this IServiceCollection services, IConfiguration configuration)
        {
            var azureSettings = configuration.GetSection("AzureAd").Get<AzureModel>();

            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "AS2  Api", Version = "v1" });
                c.AddSecurityDefinition("oauth2", new OpenApiSecurityScheme
                {
                    Type = SecuritySchemeType.OAuth2,
                    Flows = new OpenApiOAuthFlows()
                    {
                        Implicit = new OpenApiOAuthFlow()
                        {
                            AuthorizationUrl = new Uri($"https://login.microsoftonline.com/{azureSettings.TenantId}/oauth2/v2.0/authorize"),
                            TokenUrl = new Uri($"https://login.microsoftonline.com/{azureSettings.TenantId}/oauth2/v2.0/token"),
                            Scopes = new Dictionary<string, string>
                            {
                                {$"api://{azureSettings.ClientId}/access_as_user/EbrAccessAsUser", "EbrAccessAsUser"}
                            }
                        }
                    }
                });
                c.AddSecurityRequirement(new OpenApiSecurityRequirement()
                {
                    {
                        new OpenApiSecurityScheme {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "oauth2"
                            },
                            Scheme = "oauth2",
                            Name = "oauth2",
                            In = ParameterLocation.Header
                        },
                        new List<string>()
                    }
                });
            });
            //{
            //    services.AddSwaggerGen(c =>
            //    {
            //        c.SchemaFilter<EnumSchemaFilter>();
            //        c.CustomSchemaIds(id => id.FullName.Replace("+", "."));
            //        c.SwaggerDoc("v1", new OpenApiInfo
            //        {
            //            Title = "AS2Test",
            //            Version = "v1",
            //            Contact = new OpenApiContact
            //            {
            //                Name = "SG Conatcts",
            //                Url = new Uri("https://softgroup.eu.")
            //            },
            //            License = new OpenApiLicense
            //            {
            //                Name = "License",
            //                Url = new Uri("https://softgroup.eu")
            //            }

            //        });
            //        var securityScheme = new OpenApiSecurityScheme()
            //        {
            //            Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
            //            Name = "Authorization",
            //            In = ParameterLocation.Header,
            //            Type = SecuritySchemeType.Http,
            //            Scheme = "bearer",
            //            BearerFormat = "JWT"
            //        };

            //        var securityRequirement = new OpenApiSecurityRequirement
            //        {
            //            {
            //                new OpenApiSecurityScheme
            //                {
            //                    Reference = new OpenApiReference
            //                    {
            //                        Type = ReferenceType.SecurityScheme,
            //                        Id = "bearerAuth"
            //                    }
            //                },
            //             new string[] {}
            //            }
            //        };

            //c.AddSecurityDefinition("bearerAuth", securityScheme);
            //    c.AddSecurityRequirement(securityRequirement);
            //    var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
            //    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
            //    c.IncludeXmlComments(xmlPath);
            //    c.SchemaFilter<EnumSchemaFilter>();
            //});
        }
        public static IApplicationBuilder UseSwaggerDocumentation(this IApplicationBuilder app)
        {

            //app.UseSwagger();
            //app.UseSwaggerUI(c => {c.SwaggerEndpoint("/swagger/v1/swagger.json", "API v1");});


            app.UseSwagger();
            app.UseSwaggerUI(c =>
            {
                c.SwaggerEndpoint("/swagger/v1/swagger.json", "AS2Test API V1");
                c.RoutePrefix = string.Empty; // optional
            });
            return app;
        }
        public static IServiceCollection SwaggerConfig(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "My API", Version = "v1" });
                c.CustomSchemaIds(id => id.FullName!.Replace("+", "-"));
                c.AddSecurityDefinition("Keycloak", new Microsoft.OpenApi.Models.OpenApiSecurityScheme
                {
                    Type = Microsoft.OpenApi.Models.SecuritySchemeType.OAuth2,
                    Flows = new Microsoft.OpenApi.Models.OpenApiOAuthFlows
                    {
                        Implicit = new Microsoft.OpenApi.Models.OpenApiOAuthFlow
                        {
                            AuthorizationUrl = new Uri(configuration["Keycloak:authorizationUrl"]),
                            Scopes = new Dictionary<string, string>
                        {
                                { "openid", "openid" },
                                { "profile", "profile" },
                        }
                        }
                    }
                });

                c.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Id = "Keycloak",
                                Type = ReferenceType.SecurityScheme
                            },
                            In = ParameterLocation.Header,
                            Name = "Bearer",
                            Scheme = "Bearer"
                        },
                        Array.Empty<string>()
                    }
            });
            });

            return services;
        }
    }

}