﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SGAuditTrail;

namespace AzureCmdModels
{
    [Table("CustomerAggregationLevel")]
    [Audit(DisplayName = "Customer Aggregation Level")]
    public class CustomerAggregationLevel
    {
        [Key]
        public int IDCustomerAggregationLevel { get; set; }

        [Required]
        public int? IDCustomer { get; set; }

        [Audit(DisplayName = "Product", InversePropertyName = "Product", InversePropertyValue = "Code")]
        public int? IDProduct { get; set; }

        [Required]
        [StringLength(20)]
        [DisplayName("Name")]
        [Audit(DisplayName = "Name")]
        public string Name { get; set; }

        [Required]
        [StringLength(20)]
        [DisplayName("Type")]
        [Audit(DisplayName = "Type")]
        public string Type { get; set; }

        [Required]
        [StringLength(10)]
        [DisplayName("Serial type")]
        [Audit(DisplayName = "Serial type")]
        public string SerialType { get; set; }

        [DisplayName("Unit capacity")]
        [Audit(DisplayName = "Unit capacity")]
        public int? UnitCapacity { get; set; }

        [Required]
        [StringLength(20)]
        [DisplayName("Sublevel")]
        [Audit(DisplayName = "Sub level")]
        public string SubLevel { get; set; }

        [Required]
        [StringLength(20)]
        [DisplayName("Sublevel item type")]
        [Audit(DisplayName = "Sub level item type")]
        public string SubLevelItemType { get; set; }

        [StringLength(10)]
        [DisplayName("Company prefix")]
        [RegularExpression(@"^\d{4,12}$", ErrorMessage = "Company Prefix must be number between 4 and 12 digits.")]
        public string CompanyPrefix { get; set; }

        [Range(0, 9, ErrorMessage = "Extension digit must be between 0 and 9")]
        public short? ExtensionDigit { get; set; }

        [StringLength(50)]
        public string GTIN { get; set; }

        [DisplayName("Active")]
        [Audit(DisplayName = "Active")]
        public bool IsActive { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        [InverseProperty("CustomerAggregationLevels")]
        public Customer Customer { get; set; }

        [InverseProperty("ProductAggregationLevels")]
        public Product Product { get; set; }

    }
}
