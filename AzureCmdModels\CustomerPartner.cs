﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SGAuditTrail;



namespace AzureCmdModels
{
    [Table("CustomerPartner")]
    [Audit(DisplayName = "Partner")]
    public class CustomerPartner
    {
        public CustomerPartner()
        {
            Products = new HashSet<Product>();
        }

        [Key]
        public int IDCustomerPartner { get; set; }

        [DisplayName("Customer")]
        [Audit(DisplayName = "Customer", InversePropertyName = "Customer", InversePropertyValue = "Name")]
        public int IDCustomer { get; set; }

        [StringLength(255)]
        [Audit(DisplayName = "Name")]
        [DisplayName("Name")]
        [Required]
        public string Name { get; set; }

        [StringLength(255)]
        [Audit(DisplayName = "System Name")]
        [DisplayName("System Name")]
        [Required]
        public string SystemName { get; set; }

        [StringLength(50)]
        [Audit(DisplayName = "SNX Request", NullValue = "N/A")]
        [DisplayName("SNX Request")]
        [DisplayFormat(NullDisplayText = "N/A")]
        public string SNXRequest { get; set; }

        [StringLength(50)]
        [Audit(DisplayName = "SSCC Request", NullValue = "N/A")]
        [DisplayName("SSCC Request")]
        [DisplayFormat(NullDisplayText = "N/A")]
        public string SSCCRequest { get; set; }

        [StringLength(50)]
        [Audit(DisplayName = "EBR Import", NullValue = "N/A")]
        [DisplayName("EBR Import")]
        [DisplayFormat(NullDisplayText = "N/A")]
        public string EBRImport { get; set; }

        [StringLength(50)]
        [Audit(DisplayName = "Batch Report", NullValue = "N/A")]
        [DisplayName("Batch Report")]
        [DisplayFormat(NullDisplayText = "N/A")]
        public string BatchReport { get; set; }

        [StringLength(50)]
        [Audit(DisplayName = "Shipment Report", NullValue = "N/A")]
        [DisplayName("Shipment Report")]
        [DisplayFormat(NullDisplayText = "N/A")]
        public string ShipmentReport { get; set; }

        [DisplayName("Active")]
        [Audit(DisplayName = "Active", ValueTrue = "Yes", ValueFalse = "No")]
        public bool IsActive { get; set; }

        [InverseProperty("CustomerPartners")]
        public Customer Customer { get; set; }

        [InverseProperty("CustomerPartner")]
        public ICollection<Product> Products { get; set; }

        [InverseProperty("CustomerPartner")]
        public ICollection<CustomerPartnerParameter> CustomerPartnerParameters { get; set; }
    }
}
