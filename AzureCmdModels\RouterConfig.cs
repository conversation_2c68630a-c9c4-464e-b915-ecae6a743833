﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureCmdModels
{
    [Table("RouterConfig")]
    public class RouterConfig
    {
        public RouterConfig()
        {
          RouterElements = new HashSet<RouterElement>();      
        }

        [Key]
        public int IDRouterConfig { get; set; }

        [Required (ErrorMessage = "Customer must be selected")]
        public int IDCustomer { get; set; }
        public bool IsActive { get; set; }

        [StringLength(255)]
        public string ElementName { get; set; }

        [Required (ErrorMessage = "Processor type must be selected")]
        public ProcessorTypeEnum ProcessorType { get; set; }

        [Required (ErrorMessage = "Source folder access must be selected")]
        public FolderAccess SourceFolderAccess { get; set; }

        [StringLength(255)]
        public string SourceUrl { get; set; }

        public int? SourcePort { get; set; }

        [StringLength(128)]
        public string SourceUser { get; set; }

        [StringLength(128)]
        public string SourcePassword { get; set; }

        [StringLength(255)]
        public string SourceFolder { get; set; }

        [Required(ErrorMessage = "Target folder access must be selected")]
        public FolderAccess TargetFolderAccess { get; set; }

        [StringLength(255)]
        public string TargetUrl { get; set; }

        public int? TargetPort { get; set; }

        [StringLength(128)]
        public string TargetUser { get; set; }

        [StringLength(128)]
        public string TargetPassword { get; set; }

        [Required (ErrorMessage = "Target folder must be specified")]
        [StringLength(255)]
        public string TargetFolder { get; set; }

        [StringLength(255)]
        public string AlternateTargetFolder { get; set; }

        public bool IsFileArchived { get; set; }

        public int FileDelay { get; set; }

        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        [DisplayName("Created")]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        [DisplayName("User Created")]
        public string UserCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        [NotMapped]
        public int? IDImportConfig { get; set; }

        [InverseProperty("RouterConfig")]
        public ICollection<RouterElement> RouterElements { get; set; }
    }

    public enum ProcessorTypeEnum
    {
        ROUTER,
        MOVE,
        COPY
    }

    public enum FolderAccess
    {
        FILE,
        SFTP
    }
}
