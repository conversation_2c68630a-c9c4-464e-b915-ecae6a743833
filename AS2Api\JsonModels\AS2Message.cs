﻿using System.Text.Json.Serialization;

namespace AS2Api.JsonModels
{
    public class AS2Message
    {
        [JsonPropertyName("IsSigned")]
        public string IsSigned { get; set; }

        [JsonPropertyName("signingAlgorithm")]
        public string SigningAlgorithm { get; set; }

        [JsonPropertyName("encryptionAlgorithm")]
        public string EncryptionAlgorithm { get; set; }

        [JsonPropertyName("isCompressed")]
        public string IsCompressed { get; set; }

        [JsonPropertyName("compressionType")]
        public string CompressionType { get; set; }

        [JsonPropertyName("transferEncoding")]
        public string TransferEncoding { get; set; }

        [JsonPropertyName("serverUri")]
        public string ServerUri { get; set; }

        [JsonPropertyName("senderCert")]
        public string SenderCert { get; set; }

        [JsonPropertyName("receiverCert")]
        public string ReceiverCert { get; set; }


        [<PERSON>son<PERSON>ropertyName("asFrom")]
        public string SenderGLN { get; set; }

        [JsonPropertyName("asTo")]
        public string RecevierGLN { get; set; }

        [JsonPropertyName("subject")]
        public string Subject { get; set; }

        [JsonPropertyName("messageId")]
        public string MessageId { get; set; }

        [JsonPropertyName("message")]
        public string Message { get; set; }

        [JsonPropertyName("filename")]
        public string Filename { get; set; }
    }
}
