﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SGAuditTrail;

namespace AzureCmdModels
{
    [Table("ProductLabel")]
    [Audit(DisplayName = "Product Label")]
    public class ProductLabel
    {

        [Key]
        public int IDProductLabel { get; set; }

        [Required]
        public int IDCustomer { get; set; }

        [Required]
        public int IDProduct { get; set; }

        [Required]
        [DisplayName("Label name")]
        [StringLength(80)]
        [Audit(DisplayName = "Label name")]
        public string LabelName { get; set; }

        [Required(ErrorMessage = "Label file not uploaded")]
        [Audit(DisplayName = "Label")]
        [StringLength(int.MaxValue)]
        public string Label { get; set; }

        [Required]
        [DisplayName("Unit")]
        [Audit(DisplayName = "Unit")]
        public string Level { get; set; }

        [Required]
        public bool IsDefault { get; set; }

        [DisplayName("Target market")]
        [Audit(DisplayName = "Target market", InversePropertyName = "TargetMarket", InversePropertyValue = "Name")]
        public int? IDTargetMarket { get; set; }


        [Required]
        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        public string UserCreated { get; set; }

        [Column("PrintingSrcSystem")]
        [DisplayName("Source system")]
        public string PrintingSrcSystem { get; set; }

        [Column("PrintingSrcSystemName")]
        [DisplayName("Printing source system")]
        [Audit(DisplayName = "Printing source system")]
        public string PrintingSrcSystemName { get; set; }

        
        //[InverseProperty("ProductLabels")]
        //public Customer Customer { get; set; }

        [InverseProperty("ProductLabels")]
        public Product Product { get; set; }

        [InverseProperty("ProductLabels")]
        public TargetMarket TargetMarket { get; set; }

        [Required (ErrorMessage = "Select label template")]
        [NotMapped]
        public int? IDLabelTemplate { get; set; }

        public AuditTrailHeader GetAuditTrailHeader(string user, int page, int pages)
        {
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

            AuditTrailHeader header = new AuditTrailHeader()
            {
                Controler = "Products",
                ID = IDProductLabel.ToString(),
                UserName = user,
                Page = page,
                Pages = pages
            };

            header.AddItem("ProductLabel", LabelName);
            header.AddItem("User", user);
            header.AddItem("Timestamp", timestamp);

            return header;
        }
    }
}
