﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SGAuditTrail;

namespace AzureCmdModels
{
    [Table("AuditTrail")]
    public class AuditTrail
    {
        public AuditTrail()
        {
            Items = new HashSet<AuditTrailItem>();
        }

        [Key]
        public int IDAuditTrail { get; set; }

        [StringLength(100)]
        public string Action { get; set; }

        [StringLength(50)]
        [DisplayName("ID")]
        public string IDTable { get; set; }

        [StringLength(50)]
        [DisplayName("ID")]
        public string IDTableDetail { get; set; }

        [StringLength(100)]
        [DisplayName("Table")]
        public string TableName { get; set; }

        [StringLength(100)]
        [DisplayName("Table")]
        public string DisplayName { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime Timestamp { get; set; }

        [StringLength(128)]
        public string User { get; set; }

        [InverseProperty("AuditTrail")]
        public ICollection<AuditTrailItem> Items { get; set; }
    }


    [Table("AuditTrailItem")]
    public class AuditTrailItem
    {
        [Key]
        public int IDAuditTrailItem { get; set; }

        public int IDAuditTrail { get; set; }

        [StringLength(100)]
        [DisplayName("Field Name")]
        public string FieldName { get; set; }

        [StringLength(100)]
        [DisplayName("Display Name")]
        public string DisplayName { get; set; }

        [DisplayName("Old Value")]
        public string OldValue { get; set; }

        [DisplayName("New Value")]
        public string NewValue { get; set; }


        [InverseProperty("Items")]
        public AuditTrail AuditTrail { get; set; }


    }

    public class AuditTrailHeader
    {
        public string ID { get; set; }
        public string Controler { get; set; }
        public int Page { get; set; }
        public int Pages { get; set; }
        public string UserName { get; set; }
        public List<AuditTrailHeaderItem> Items { get; set; }

        public AuditTrailHeader()
        {
            Items = new List<AuditTrailHeaderItem>();
        }

        public void AddItem(string caption, string value)
        {
            Items.Add(new AuditTrailHeaderItem(caption, value));
        }
    }

    public class AuditTrailHeaderItem
    {
        public string Caption { get; set; }
        public string Value { get; set; }

        public AuditTrailHeaderItem(string caption, string value)
        {
            Caption = caption;
            Value = value;
        }
    }

}
