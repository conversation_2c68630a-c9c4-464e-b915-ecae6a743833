﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureBhModels
{
    public static class LogisticsReports
    {
        public static List<LogisticsReportType> Reports
        {
            get
            {
                return new List<LogisticsReportType>()
                {
                    new LogisticsReportType
                    {
                        Type = LogisticsReportsEnum.PACK,
                        DisplayOrder = 1,
                        DisplayName = "Pack",
                        EpcisAction = "ADD",
                        EpcisBizStep = "urn:epcglobal:cbv:bizstep:packing",
                        EpcisDisposition = "",
                        EpcisHasChildEPCs = true,
                        EpcisHasSender = true
                    },
                    new LogisticsReportType
                    {
                        Type = LogisticsReportsEnum.UNPACK,
                        DisplayOrder = 2,
                        DisplayName = "Unpack",
                        EpcisAction = "DELETE",
                        EpcisBizStep = "urn:epcglobal:cbv:bizstep:unpacking",
                        EpcisDisposition = "",
                        EpcisHasChildEPCs = true,
                        EpcisHasSender = true
                    },
                    new LogisticsReportType
                    {
                        Type = LogisticsReportsEnum.SHIP,
                        DisplayOrder = 3,
                        DisplayName = "Shipment",
                        EpcisAction = "OBSERVE",
                        EpcisBizStep = "urn:epcglobal:cbv:bizstep:shipping",
                        EpcisDisposition = "urn:epcglobal:cbv:disp:in_transit",
                        EpcisHasReceiver = true,
                        EpcisHasSender = true,
                    },
                    new LogisticsReportType
                    {
                        Type = LogisticsReportsEnum.RECEIVE,
                        DisplayOrder = 4,
                        DisplayName = "Receive",
                        EpcisAction = "OBSERVE",
                        EpcisBizStep = "urn:epcglobal:cbv:bizstep:receiving",
                        EpcisDisposition = "urn:epcglobal:cbv:disp:in_progress",
                        EpcisHasReceiver = true
                    },
                    new LogisticsReportType
                    {
                        Type = LogisticsReportsEnum.RETURN_SHIPPING,
                        DisplayOrder = 5,
                        DisplayName = "Return shipping",
                        EpcisAction = "OBSERVE",
                        EpcisBizStep = "urn:epcglobal:cbv:bizstep:shipping",
                        EpcisDisposition = "urn:epcglobal:cbv:disp:returned",
                        EpcisHasSender = true,
                        EpcisHasReceiver = true,
                        Reason = new List<CodeAndDescriptionType>
                        {
                            new CodeAndDescriptionType("R01", "(R01) Product does not match"),
                            new CodeAndDescriptionType("R02", "(R02) Quantity does not match"),
                            new CodeAndDescriptionType("R03", "(R03) Permit and Delivery Order mismatch"),
                            new CodeAndDescriptionType("R04", "(R04) Serial number mismatch"),
                            new CodeAndDescriptionType("R05", "(R05) Seal broken"),
                            new CodeAndDescriptionType("R06", "(R06) Packaging damaged"),
                            new CodeAndDescriptionType("R07", "(R07) Storage conditions violated"),
                            new CodeAndDescriptionType("R08", "(R08) Multiple supporting documents missing")
                        }
                    },
                    new LogisticsReportType
                    {
                        Type = LogisticsReportsEnum.RETURN_RECEIVING,
                        DisplayOrder = 6,
                        DisplayName = "Return receiving",
                        EpcisAction = "OBSERVE",
                        EpcisBizStep = "urn:epcglobal:cbv:bizstep:receiving",
                        EpcisDisposition = "urn:epcglobal:cbv:disp:returned",
                        EpcisHasSender = true,
                        EpcisHasReceiver = true
                    },
                    new LogisticsReportType
                    {
                        Type = LogisticsReportsEnum.SAMPLE,
                        DisplayOrder = 7,
                        DisplayName = "Sample",
                        EpcisAction = "OBSERVE",
                        EpcisBizStep = "urn:epcglobal:cbv:bizstep:inspecting",
                        EpcisDisposition = "urn:epcglobal:cbv:disp:destroyed",
                        EpcisHasSender = true,
                        Reason = new List<CodeAndDescriptionType>
                        {
                            new CodeAndDescriptionType("S01","(S01) Sample for Doctors"),
                            new CodeAndDescriptionType("S02","(S02) Packaging Review"),
                            new CodeAndDescriptionType("S03","(S03) Laboratory Sample"),
                            new CodeAndDescriptionType("S04","(S04) Criminal Investigation"),
                            new CodeAndDescriptionType("S05","(S05) Prequalification"),
                            new CodeAndDescriptionType("S06","(S06) Retention for future testing"),
                            new CodeAndDescriptionType("S07","(S07) Consumer Report"),
                            new CodeAndDescriptionType("S08","(S08) Product Documentation"),
                            new CodeAndDescriptionType("S09","(S09) Sampling through PMS"),
                            new CodeAndDescriptionType("S10","(S10) Suspect activity"),
                            new CodeAndDescriptionType("S11","(S11) Storing condition"),

                        }
                    },
                    new LogisticsReportType
                    {
                        Type = LogisticsReportsEnum.DAMAGE,
                        DisplayOrder = 8,
                        DisplayName = "Damage",
                        EpcisAction = "DELETE",
                        EpcisBizStep = "urn:epcglobal:cbv:bizstep:decommissioning",
                        EpcisDisposition = "urn:epcglobal:cbv:disp:damaged",
                        EpcisHasSender = true,
                        Reason = new List<CodeAndDescriptionType>
                        {
                            new CodeAndDescriptionType("D01", "(D01) Broken"),
                            new CodeAndDescriptionType("D02", "(D02) Unfolded"),
                            new CodeAndDescriptionType("D03", "(D03) Torn"),
                            new CodeAndDescriptionType("D04", "(D04) 2D Matrix not readable"),
                            new CodeAndDescriptionType("D05", "(D05) Smashed"),
                            new CodeAndDescriptionType("D06", "(D06) Damage due To liquid spill"),
                            new CodeAndDescriptionType("D07", "(D07) Other")
                        }
                    },
                    new LogisticsReportType
                    {
                        Type = LogisticsReportsEnum.STOLEN,
                        DisplayOrder = 9,
                        DisplayName = "Stolen",
                        EpcisAction = "DELETE",
                        EpcisBizStep = "urn:epcglobal:cbv:bizstep:decommissioning",
                        EpcisDisposition = "urn:epcglobal:cbv:disp:stolen",
                        EpcisHasSender = true
                    },
                    new LogisticsReportType
                    {
                        Type = LogisticsReportsEnum.EXPORTED,
                        DisplayOrder = 10,
                        DisplayName = "Exported",
                        EpcisAction = "DELETE",
                        EpcisBizStep = "urn:epcglobal:cbv:bizstep:decommissioning",
                        EpcisDisposition = "urn:epcglobal:cbv:disp:non_sellable_other",
                        EpcisHasSender = true
                    },
                    new LogisticsReportType
                    {
                        Type = LogisticsReportsEnum.LOST,
                        DisplayOrder = 11,
                        DisplayName = "Lost",
                        EpcisAction = "DELETE",
                        EpcisBizStep = "urn:epcglobal:cbv:bizstep:decommissioning",
                        EpcisDisposition = "urn:epcglobal:cbv:disp:inactive",
                        EpcisHasSender = true
                    },
                    new LogisticsReportType
                    {
                        Type = LogisticsReportsEnum.DISPENSE,
                        DisplayOrder = 12,
                        DisplayName = "Dispense",
                        EpcisAction = "OBSERVE",
                        EpcisBizStep = "urn:epcglobal:cbv:bizstep:retail_selling",
                        EpcisDisposition = "urn:epcglobal:cbv:disp:retail_sold",
                        EpcisHasSender = true,
                        EpcisHasBatchData = true
                    },

                };
            }
        }

        public static LogisticsReportType GetReport(LogisticsReportsEnum type)
        {
            return Reports.FirstOrDefault(r => r.Type == type);
        }
    }

    public class LogisticsReportType
    {
        public LogisticsReportsEnum Type { get; set; }

        public int DisplayOrder { get; set; }

        public string DisplayName { get; set; }

        public string EpcisAction { get; set; }

        public string EpcisBizStep { get; set; }

        public string EpcisDisposition { get; set; }

        public bool EpcisHasChildEPCs { get; set; }

        public bool EpcisHasSender { get; set; }

        public bool EpcisHasReceiver { get; set; }

        public bool EpcisHasBatchData { get; set; }
        public List<CodeAndDescriptionType> Reason { get; set; }

    }


    public enum LogisticsReportsEnum
    {
        PACK,
        UNPACK,
        SHIP,
        RECEIVE,
        RETURN_SHIPPING,
        RETURN_RECEIVING,
        SAMPLE,
        DAMAGE,
        STOLEN,
        EXPORTED,
        LOST,
        DISPENSE
    }


    public class CodeAndDescriptionType
    {
        public string Code { get; set; }
        public string Description { get; set; }

        public CodeAndDescriptionType()
        {

        }

        public CodeAndDescriptionType(string code, string description)
        {
            Code = code;
            Description = description;
        }
    }
}
