using System.Security.Cryptography.X509Certificates;

namespace AS2Test_master.Features.Services.Certificates
{
    public interface ICertificateService
    {
         Task<X509Certificate2> ParseSenderCertificate(string base64OrPem, string password = null);
         Task<X509Certificate2> GetPartnerCertificate(string certificateBase64);
         Task<X509Certificate2> GetSenderCertificateAsync(string signerId);
         Task<X509Certificate2> GetSenderCertificate(string certificateBase64, string password);
    }
}