﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEbrModels
{
    [Table("BatchProductAdditionalCode")]
    public class BatchProductAdditionalCode
    {
        [Key]
        public int IDBatchProductAdditionalCode { get; set; }

        [Required]
        public int IDBatchProduct { get; set; }

        [Required]
        [StringLength(50)]
        public string CodeType { get; set; }

        [StringLength(50)]
        public string Code { get; set; }

        [InverseProperty("BatchProductAdditionalCodes")]
        public BatchProduct BatchProduct { get; set; }
    }
}
