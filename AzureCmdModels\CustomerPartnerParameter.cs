﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SGAuditTrail;

namespace AzureCmdModels
{
    [Table("CustomerPartnerParameter")]
    public class CustomerPartnerParameter
    {
        [Key]
        public int IDCustomerPartnerParameter { get; set; }

        [DisplayName("Partner")]
        [Audit(DisplayName = "Partner", InversePropertyName = "CustomerPartner", InversePropertyValue = "Name")]
        public int IDCustomerPartner { get; set; }

        [StringLength(255)]
        [Audit(DisplayName = "Name")]
        [Required]
        public string Name { get; set; }

        [StringLength(255)]
        [Audit(DisplayName = "Value")]
        [Required]
        public string Value { get; set; }

        [Required]
        [DisplayName("Timestamp created")]
        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [DisplayName("User created")]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [InverseProperty("CustomerPartnerParameters")]
        public CustomerPartner CustomerPartner { get; set; }
    }
}
