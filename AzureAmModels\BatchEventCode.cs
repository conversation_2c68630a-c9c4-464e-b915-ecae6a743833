﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureAmModels
{
    [Table("BatchEventCode")]
    public class BatchEventCode
    {
        [Key]
        public long IDBatchEventCode { get; set; }

        [Required]
        public int IDBatchEvent { get; set; }

        [Required]
        public string CryptoKey { get; set; }

        [Required]
        public string CryptoCode { get; set; }

        [Required]
        public string SerialNumber { get; set; }

        [Required]
        [DisplayName("Timestamp created")]
        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [DisplayName("User created")]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [InverseProperty("BatchEventCodes")]
        public BatchEvent BatchEvent { get; set; }

        public string CryptoCodeState { get; set; }


        public override bool Equals(object obj)
        {
            if (obj == null)
                return false;

            if (obj.GetType() == this.GetType())
            {
                if (((BatchEventCode)obj).IDBatchEventCode == this.IDBatchEventCode)
                    return true;
                else
                    return false;
            }
            else
                return false;
        }

        public BatchEventCode()
        {

        }

        public BatchEventCode(string cryptoCode, List<SgXmlEvent.GS1AppIdentifier> identifiers) : base()
        {
            SgXmlEvent.Barcode decoded = new SgXmlEvent.Barcode(cryptoCode.Trim(), identifiers);

            this.CryptoKey = decoded.AI91;
            this.CryptoCode = decoded.AI92;
            this.SerialNumber = decoded.SerialNumber;
        }
    }
}
