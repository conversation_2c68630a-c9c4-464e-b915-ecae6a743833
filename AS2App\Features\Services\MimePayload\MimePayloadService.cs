using System.Diagnostics;
using System.Net.Mime;
using System.Security.Cryptography;
using System.Text;
using AS2Test_master.Models;
using MimeKit;
using static AS2Test.Extensions.Helpers.ExtensionMethods;

namespace AS2Test_master.Features.Services.MimePayload
{
    /// <summary>
    /// Service class responsible for creating MIME payloads for AS2 messages.
    /// </summary>
    public class MimePayloadService : IMimePayloadService
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="MimePayloadService"/> class.
        /// </summary>
        /// <param name="encryptionService">The encryption service.</param>
        public MimePayloadService()
        {
        }

        /// <summary>
        /// Method that handles the AS2 Message common headers and creates MimeMessage object
        /// </summary>
        /// <param name="messageRequest"></param>
        /// <returns>MimeMessage Object with the added common Mime headers</returns>
        private MimeMessage AddMimeOuterCommonHeaders(MimeMessage mimeMessage, MessageRequest messageRequest)
        {
            //var mimeMessage = new MimeMessage();
            mimeMessage.Headers.Remove(HeaderId.From);
            mimeMessage.Headers.Add("AS2-From", messageRequest.AsFrom);
            mimeMessage.Headers.Add("AS2-To", messageRequest.AsTo);
            mimeMessage.Subject = messageRequest.Subject;
            mimeMessage.Date = DateTimeOffset.Now;
            mimeMessage.MessageId = messageRequest.MessageId;
            
            // mimeMessage.Headers.Replace(HeaderId.ContentType, "application/pkcs7-mime; smime-type=enveloped-data; name=smime.p7m");
            // mimeMessage.Headers.Remove(HeaderId.ContentType);
            // mimeMessage.Headers.Add(HeaderId.ContentType, "application/pkcs7-mime; smime-type=enveloped-data; name=smime.p7m");
            //mimeMessage.Headers.Add(HeaderId.ContentTransferEncoding, messageRequest.TransferEncoding.ToString().ToLower());

            return mimeMessage;
        }

        public async Task<MimeMessage> CreateXmlAttachment(MessageRequest messageRequest, string filename,
                    TransferEncodings transferEncodings)
        {
            if (string.IsNullOrEmpty(messageRequest.Message))
            {
                throw new ArgumentException("Message part could not be null");
            }
            var xmlPart = new MimePart("application", "xml")
            {
               // Content = new MimeContent(new MemoryStream(Encoding.UTF8.GetBytes(messageRequest.Message))),
                Content = new MimeContent(new MemoryStream(Encoding.UTF8.GetBytes("<note>test</note>"))),
                ContentDisposition = new MimeKit.ContentDisposition("attachment"),
                ContentTransferEncoding = ContentEncoding.Base64,
                FileName = messageRequest.FileName
            };

            //  var encryptedAndsigned = await _encryptionService.SignAndEcryptGranular(xmlPart, messageRequest.SigningAlgorithm.Value,
            //          messageRequest.EncryptionAlgorithm!.Value, messageRequest);
            // return AddMimeOuterCommonHeaders(encryptedAndsigned, messageRequest);
            return null;
            
        }
        /// <summary>
        /// Creates a multipart MIME message based on the provided message request.
        /// </summary>
        /// <param name="messageRequest">The message request containing the parts of the MIME message.</param>
        /// <returns>The created multipart MIME message.</returns>
        public async Task<MimeMessage> CreateMultipartMessage(MessageRequest messageRequest)
        {
            var mimeMessage = new MimeMessage();
            var multipartReport = new Multipart("mixed");

            foreach (var part in messageRequest.MimeMessageParts)
            {
                var partContentBytes = Encoding.UTF8.GetBytes(part.Message);
                var partContentStream = new MemoryStream(partContentBytes);

                multipartReport.Add(new MimePart(part.PartType.Key, part.PartType.Value)
                {
                    IsAttachment = part.IsAttachment,
                    ContentTransferEncoding = TransferEncodingConvert(messageRequest.TransferEncoding.ToString()).Item2,
                    Content = new MimeContent(partContentStream, TransferEncodingConvert(messageRequest.TransferEncoding.ToString()).Item2),
                    ContentDisposition = new MimeKit.ContentDisposition("filename=" + messageRequest.FileName),
                });
            }
            /*
            foreach(var part in messageRequest.MimeMessageParts)
            {
                multipartReport.Add(new MimePart(part.PartType.Key,part.PartType.Value)
                {
                    IsAttachment =  part.IsAttachment,
                    ContentTransferEncoding = TransferEncodingConvert(messageRequest.TransferEncoding.ToString()).Item2,
                    Content = new MimeContent(new MemoryStream(Encoding.UTF8.GetBytes(part.Mressage)), ContentEncoding.Base64),
                    ContentDisposition = new MimeKit.ContentDisposition("filename=" + messageRequest.FileName),
                    
                });
            } */
            mimeMessage.Body = multipartReport;

            var mimeMessageEncrypted = await MessageEncriptionHandle(mimeMessage, messageRequest);
             return AddMimeOuterCommonHeaders(mimeMessageEncrypted, messageRequest);
        }

        /// <summary>
        /// Creates a MimeMessage with the specified message content, file name, and transfer encoding.
        /// </summary>
        /// <param name="message">The message content.</param>
        /// <param name="fileName">The file name.</param>
        /// <param name="transferEncoding">The transfer encoding.</param>
        /// <returns>The created MimeMessage.</returns>
        public async Task<MimeMessage> CreateMimeContent(MessageRequest message, string fileName,
        TransferEncodings transferEncoding)
        {
            if (string.IsNullOrEmpty(message.Message))
            {
                throw new ArgumentNullException("Message part could not be null or empty");
            }

            // Call MimeMessage crete method with commkn headers
            var mimeMessage = new MimeMessage();
            byte[] mdnHeaderBytes = Encoding.ASCII.GetBytes($"signed-receipt-micalg=optional,{message.SigningAlgorithm.ToString().ToLower()}".Base64PaddingConverter());
            var mdnHeader = Convert.ToBase64String(mdnHeaderBytes);
            //mdnHeader.Base64PaddingConverter()
            //dispositionOptions.SetRawValue("signed-receipt-micalg'='optional,sha256\r\n");
            var dispositionOptions = new Header(HeaderId.DispositionNotificationOptions, mdnHeader);

            var content = new MemoryStream((Convert.FromBase64String(message.Message)));

            var atacchmentPart = new MimePart("application", "xml")
            {
                IsAttachment = true,
                Content = new MimeContent(content, TransferEncodingConvert(message.TransferEncoding.ToString()).Item2),
                ContentTransferEncoding = TransferEncodingConvert(message.TransferEncoding.ToString()).Item2,
                //ContentDisposition = new MimeKit.ContentDisposition("attachment")
            };
            atacchmentPart.Headers.Remove("Content-Diposition");

            if (string.IsNullOrEmpty(Path.GetExtension(atacchmentPart.ContentDisposition.FileName)))
            {
                atacchmentPart.ContentDisposition.FileName += ".xml";
            }

            // mimeMessage.Headers.Add(dispositionOptions); 
            mimeMessage.Body = atacchmentPart;

            return await MessageEncriptionHandle(mimeMessage, message);
        }

        /// <summary>
        /// Creates a MIME message with an attachment based on the provided <paramref name="messageRequest"/>.
        /// </summary>
        /// <param name="messageRequest">The message request containing the necessary information for creating the MIME message.</param>
        /// <returns>A task representing the asynchronous operation. The task result is the created MIME message.</returns>
        public async Task<MimeMessage> CreateAtachmentMime(MessageRequest messageRequest)
        {
            if (string.IsNullOrEmpty(messageRequest.Message))
            {
                throw new ArgumentNullException("Message cannot be null or empty");
            }
            var encoding = TransferEncodingConvert(messageRequest.TransferEncoding.ToString());
            var transferEncoding = messageRequest.TransferEncoding.ToString();

            var mimeMessage = new MimeMessage();
            /*
            mimeMessage.Headers.Add("AS2-From", messageRequest.AsFrom);
            mimeMessage.Headers.Add("AS2-To", messageRequest.AsTo);
            // mimeMessage.Headers.Add("protocol", "micalg=sha256");            
            mimeMessage.Subject = Path.GetFileNameWithoutExtension(messageRequest.FileName);
            mimeMessage.Date = DateTimeOffset.Now;
            mimeMessage.MessageId = Guid.NewGuid().ToString();
            mimeMessage.Headers.Add("Content-Transfer-Encoding", transferEncoding);
            */
            var dispositionOptions = new Header(Encoding.UTF8, HeaderId.DispositionNotificationOptions
             , "signed-receipt-micalg=\"optional\",sha256");
            //dispositionOptions.SetRawValue("signed-receipt-micalg'='optional,sha256\r\n");
            mimeMessage.Headers.Add(dispositionOptions);

            var multipartReport = new Multipart();

            var content = new MemoryStream(Convert.FromBase64String(messageRequest.Message));

            MimeContent mimeContent = new MimeContent(content);

            multipartReport.Add(new MimePart("application", "xml")
            {
                IsAttachment = true,
                Content = mimeContent,
                ContentDisposition = new MimeKit.ContentDisposition(MimeKit.ContentDisposition.Attachment),
                ContentTransferEncoding = TransferEncodingConvert(messageRequest.TransferEncoding.ToString()).Item2
            });
            //multipartReport.ContentType.Parameters.Add("micalg","sha256");

            var part = new MimePart("application", "xml")
            {
                ContentDisposition = new MimeKit.ContentDisposition(messageRequest.FileName),
                ContentTransferEncoding = TransferEncodingConvert(messageRequest.TransferEncoding.ToString()).Item2,
                FileName = Path.GetFileName(messageRequest.FileName),
                Content = new MimeContent(content)
            };

            mimeMessage.Body = multipartReport;
            var mimeMessageWithHeadersAndEncryption = AddMimeOuterCommonHeaders(mimeMessage, messageRequest);

            return await MessageEncriptionHandle(mimeMessageWithHeadersAndEncryption, messageRequest);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="messageRequest"></param>
        /// <param name="fileName"></param>
        /// <param name="transferEncoding"></param>
        /// <returns></returns> <summary>
        public async Task<MimeMessage> CreateMimeMessage(MessageRequest messageRequest, string fileName, string transferEncoding)
        {
            // Create a new message
            var mimeMessage = new MimeMessage();
            var multipartReport = new Multipart();
            var headrsList = new List<Header>();

            // Add MIME Headers              
            mimeMessage.Subject = Path.GetFileNameWithoutExtension(fileName);

            //mimeMessage.Headers.Add("Content-Type","application/pkcs7-signature; " + "name=smime-type=signed data");

            mimeMessage.Headers.Add("Content-Transfer-Encoding", transferEncoding);

            // Create MIME message boby        
            var textPart = new TextPart
            {
                IsAttachment = true,
                ContentTransferEncoding = TransferEncodingConvert(messageRequest.TransferEncoding.ToString()).Item2,
                Content = new MimeContent(
                    new MemoryStream(Encoding.UTF8.GetBytes("Test Message Text Part.")))
            };
            multipartReport.Add(textPart);
            var builder = new BodyBuilder();

            mimeMessage.Body = builder.ToMessageBody();
            var content = new MemoryStream(Convert.FromBase64String(messageRequest.Message!));
            MimeContent mimeContent = new MimeContent(content);

            multipartReport.Add(new MimePart("application", "xml")
            {
                IsAttachment = true,
                ContentTransferEncoding = TransferEncodingConvert(messageRequest.TransferEncoding.ToString()).Item2,
                Content = mimeContent,
                ContentDisposition = new MimeKit.ContentDisposition(("filename=" + messageRequest.FileName).Base64PaddingConverter()),
                //ContentTransferEncoding = ContentEncoding.Base64
            });
            multipartReport.ContentType.Parameters.Add("micalg", "sha256");
            builder.Attachments.Add(fileName, Convert.FromBase64String(messageRequest.Message!));
            mimeMessage.Body = multipartReport;

            mimeMessage.Body = builder.ToMessageBody();
            return await MessageEncriptionHandle(mimeMessage, messageRequest);
        }

        /// <summary>
        /// Creates a MIME message with an attachment.
        /// </summary>
        /// <param name="messageRequest">The message request object.</param>
        /// <param name="message">The message content.</param>
        /// <param name="fileName">The name of the attachment file.</param>
        /// <returns>The created MIME message.</returns>
        public async Task<MimeMessage> CreateAtachmentMimeMessage(MessageRequest messageRequest, string message, string fileName)
        {
            var mimeMessage = new MimeMessage();
            var builder = new BodyBuilder();

            byte[] mdnHeaderBytes = Encoding.ASCII.GetBytes("signed-receipt-micalg=optional, " + messageRequest.MdnOption.MessageIntegrityCheckAlgorithm.ToString().ToLower());
            var mdnHeader = Convert.ToBase64String(mdnHeaderBytes);
            mimeMessage.Headers.Add(HeaderId.ContentTransferEncoding, "base64");
            mimeMessage.Headers.Add(HeaderId.ContentType, "application/xml");
            mimeMessage.Headers.Add(new Header("Content-Disposition", $"filename={fileName}"));
            var dispositionOptions = new Header(HeaderId.DispositionNotificationOptions, mdnHeader);
            //dispositionOptions.SetRawValue("signed-receipt-micalg'='optional,sha256\r\n");
            mimeMessage.Headers.Add(dispositionOptions);
            builder.Attachments.Add(fileName, Encoding.UTF8.GetBytes(message));

            mimeMessage.Body = builder.ToMessageBody();
            var signedAndencryptedMime = await MessageEncriptionHandle(mimeMessage, messageRequest);
            return AddMimeOuterCommonHeaders(signedAndencryptedMime, messageRequest);
        }

        /// <summary>
        /// Handles the encryption of a MimeMessage based on the provided MessageRequest.
        /// </summary>
        /// <param name="mimeMessage">The MimeMessage to be encrypted.</param>
        /// <param name="messageRequest">The MessageRequest containing encryption settings.</param>
        /// <returns>The encrypted MimeMessage.</returns>
        private async Task<MimeMessage> MessageEncriptionHandle(MimeMessage mimeMessage, MessageRequest messageRequest)
        {
            if (!String.IsNullOrEmpty(messageRequest.MdnOption.MessageIntegrityCheckAlgorithm.ToString()))
            {
                HashAlgorithm hashAlgo = null;
                switch (messageRequest.MdnOption.MessageIntegrityCheckAlgorithm.ToString().ToUpper())
                {
                    case "SHA1":
                        hashAlgo = SHA1.Create();
                        break;

                    case "SHA256":
                        hashAlgo = SHA256.Create();
                        break;

                    case "SHA384":
                        hashAlgo = SHA384.Create();
                        break;

                    case "SHA512":
                        hashAlgo = SHA512.Create();
                        break;

                    case "MD5":
                        hashAlgo = MD5.Create();
                        break;
                }

                // Get message payload hash using extension static method
                var mic = mimeMessage.ToString().GetHashSum(hashAlgo!);

                var messageIntegrityCheck = GenerateMic(mimeMessage, hashAlgo);
                // Store the generated mic in MemoryCache
               // _encryptionService.StroreMicInCache(mimeMessage.MessageId, messageIntegrityCheck);
                /// TODO: Calculate MIC From message body  convert it to bas64 and store it //////////////////////

                var micToBase64 = Convert.ToBase64String(Encoding.ASCII.GetBytes(mic));
                var micBody = mimeMessage.Body.ToString().GetHashSum(hashAlgo!);
                var micBodyToBase64 = Convert.ToBase64String(Encoding.ASCII.GetBytes(micBody));

                // Store in memory messageId and hash in order to be compared lately 
               // _encryptionService.StroreMicInCache(messageRequest.MessageId, micBodyToBase64);
            }

            //if (messageRequest.IsEncrypted & messageRequest.IsSigned)
            //{
            //    mimeMessage = await _encryptionService.SignAndEncryptMime(mimeMessage, messageRequest.SigningAlgorithm.Value,
            //        messageRequest.EncryptionAlgorithm!.Value,
            //        messageRequest);

            //    await mimeMessage.WriteToAsync(".\\Output\\" + messageRequest.MessageId + "_AtachmentSignedEncryptedMimeMessage.eml");
            //    return await Task.FromResult(mimeMessage);
            //}
            //if (messageRequest.IsEncrypted && !messageRequest.IsSigned)
            //{
            //    mimeMessage = await _encryptionService.EncryptMime(mimeMessage, messageRequest.EncryptionAlgorithm!.Value,
            //        messageRequest);
            //    await mimeMessage.WriteToAsync(".\\Output\\" + messageRequest.MessageId + "_AtachmentEncryptedMimeMessage.eml");
            //    return await Task.FromResult(mimeMessage);
            //}
            //if (messageRequest.IsSigned && !messageRequest.IsEncrypted)
            //{
            //    mimeMessage = await _encryptionService.SignMime(mimeMessage, messageRequest.SigningAlgorithm!.Value, messageRequest!);
            //    await mimeMessage.WriteToAsync(".\\Output\\" + messageRequest.MessageId + "_AtachmentSignedMimeMessage.eml");
            //    return await Task.FromResult(mimeMessage);
            //}
            //else
            //{
            //    //_encryptionService.StroreMicInCache(messageRequest.MessageId, micBodyToBase64);
            //    await mimeMessage.WriteToAsync(".\\Output\\" + messageRequest.MessageId + ".eml");
            //    return await Task.FromResult(mimeMessage);

            return null; //todo check encryptionService
            //}
        }

        /// <summary>
        /// Converts a string representation of transfer encoding to its corresponding TransferEncoding and ContentEncoding values.
        /// </summary>
        /// <param name="transferEncoding">The string representation of transfer encoding.</param>
        /// <returns>A tuple containing the TransferEncoding and ContentEncoding values.</returns>
        private static Tuple<TransferEncoding, ContentEncoding> TransferEncodingConvert(string transferEncoding)
        {
            var encoding = new TransferEncoding();
            var mimeKitEncoding = new ContentEncoding();

            if (transferEncoding == "Base64")
            {
                encoding = TransferEncoding.Base64;
                mimeKitEncoding = ContentEncoding.Base64;
            }
            if (transferEncoding == "SevenBit")
            {
                encoding = TransferEncoding.SevenBit;
                mimeKitEncoding = ContentEncoding.SevenBit;
            }
            if (transferEncoding == "EightBit")
            {
                encoding = TransferEncoding.EightBit;
                mimeKitEncoding = ContentEncoding.EightBit;
            }
            return new Tuple<TransferEncoding, ContentEncoding>(encoding, mimeKitEncoding);
        }

        /// <summary>
        /// Generates a Message Integrity Check (MIC) for a given MimeMessage using the specified HashAlgorithm.
        /// </summary>
        /// <param name="message">The MimeMessage to generate the MIC for.</param>
        /// <param name="hashAlgorithm">The HashAlgorithm to use for computing the MIC.</param>
        /// <returns>The generated MIC as a Base64 encoded string.</returns>
        private static string GenerateMic(MimeMessage message, HashAlgorithm hashAlgorithm)
        {
            try
            {
                // Convert the MIME message body to a byte array (you can refine this to hash specific parts of the message)
                var messageBytes = Encoding.UTF8.GetBytes(message.Body.ToString());

                HashAlgorithm hash = hashAlgorithm;
                // Compute the hash (digest) for the message content
                byte[] hashBytes = hash.ComputeHash(messageBytes);

                // Convert the hash result to Base64 and return it
                return Convert.ToBase64String(hashBytes);
            }
            catch (ArgumentNullException ex)
            {
                // Handle ArgumentNullException (thrown when message or hashAlgorithm is null)
                Debug.WriteLine($"ArgumentNullException: {ex.Message}");
                throw; // Rethrow the exception to propagate it to the caller
            }
            catch (EncoderFallbackException ex)
            {
                // Handle EncoderFallbackException (thrown when encoding fails)
                Debug.WriteLine($"EncoderFallbackException: {ex.Message}");
                throw; // Rethrow the exception to propagate it to the caller
            }
            catch (Exception ex)
            {
                // Handle any other exception
                Debug.WriteLine($"An error occurred while generating MIC: {ex.Message}");
                throw; // Rethrow the exception to propagate it to the caller
            }
        }
    }
}