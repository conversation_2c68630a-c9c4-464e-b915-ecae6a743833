using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Serialization;

namespace GSNXSCDataTransfer.CustomModels
{
    [XmlRoot(ElementName = "GSNXDataTransformation")]
    public class GSNXDataTransformation
    {
        public GSNXDataTransformation()
        {

        }
        public GSNXDataTransformation(string file)
        {
            try
            {
                if (File.Exists(file))
                {
                    XmlSerializer serializer = new XmlSerializer(typeof(GSNXDataTransformation));
                    using (StreamReader reader = new StreamReader(file))
                    {
                        GSNXDataTransformation gSNXDataTransformation = (GSNXDataTransformation)serializer.Deserialize(reader);
                        if (gSNXDataTransformation != null)
                        {
                            this.ConnectionString = gSNXDataTransformation.ConnectionString;
                            this.GsnxApplicationId = gSNXDataTransformation.GsnxApplicationId;
                            this.GsnxAuthority = gSNXDataTransformation.GsnxAuthority;
                            this.GsnxPassword = gSNXDataTransformation.GsnxPassword;
                            this.GsnxUsername = gSNXDataTransformation.GsnxUsername;
                            this.IDCustomer = gSNXDataTransformation.IDCustomer;
                            this.GsnxScope = gSNXDataTransformation.GsnxScope;
                            this.GsnxUrl = gSNXDataTransformation.GsnxUrl;
                            this.LogDirectory = gSNXDataTransformation.LogDirectory;
                            this.SNXStoredProcedure = gSNXDataTransformation.SNXStoredProcedure;
                            this.ProductsStoredProcedure = gSNXDataTransformation.ProductsStoredProcedure;
                            this.SSCCStoredProcedure = gSNXDataTransformation.SSCCStoredProcedure;
                            this.DefaultSrcSystem = gSNXDataTransformation.DefaultSrcSystem;
                            this.DefaultSrcSystemServiceID = gSNXDataTransformation.DefaultSrcSystemServiceID;
                            this.ProductMaping = gSNXDataTransformation.ProductMaping;
                            this.ProductsMaping = gSNXDataTransformation.ProductsMaping;
                            this.MarkUploadedSerials = gSNXDataTransformation.MarkUploadedSerials;
                            this.MarkUploadedSSCC = gSNXDataTransformation.MarkUploadedSSCC;
                            this.AppTimeout = gSNXDataTransformation.AppTimeout;
                            this.CryptoCodeMapping = gSNXDataTransformation.CryptoCodeMapping;

                            ValidateConfig();
                        }
                        else
                            throw new Exception($"File [{file}] contains invalid data");
                    }

                }
                else
                    throw new Exception($"File not found. [{file}]");
            }
            catch (Exception e)
            {
                var debug = e.Message;
                throw;
            }
        }



        [XmlElement(ElementName = "IDCustomer")]
        public int IDCustomer { get; set; }

        [XmlElement(ElementName = "ConnectionString")]
        public string ConnectionString { get; set; }

        [XmlElement(ElementName = "ProductsStoredProcedure")]
        public string ProductsStoredProcedure { get; set; }

        [XmlElement(ElementName = "SNXStoredProcedure")]
        public string SNXStoredProcedure { get; set; }

        [XmlElement(ElementName = "MarkUploadedSerials")]
        public string MarkUploadedSerials { get; set; }

        [XmlElement(ElementName = "SSCCStoredProcedure")]
        public string SSCCStoredProcedure { get; set; }

        [XmlElement(ElementName = "MarkUploadedSSCC")]
        public string MarkUploadedSSCC { get; set; }

        [XmlElement(ElementName = "GsnxUrl")]
        public string GsnxUrl { get; set; }

        [XmlElement(ElementName = "GsnxApplicationId")]
        public string GsnxApplicationId { get; set; }

        [XmlElement(ElementName = "GsnxAuthority")]
        public string GsnxAuthority { get; set; }

        [XmlElement(ElementName = "GsnxScope")]
        public string GsnxScope { get; set; }

        [XmlElement(ElementName = "GsnxUsername")]
        public string GsnxUsername { get; set; }

        [XmlElement(ElementName = "GsnxPassword")]
        public string GsnxPassword { get; set; }

        [XmlElement(ElementName = "DefaultSrcSystemServiceID")]
        public int DefaultSrcSystemServiceID { get; set; }

        [XmlElement(ElementName = "DefaultSrcSystem")]
        public string DefaultSrcSystem { get; set; }

        [XmlElement(ElementName = "LogDirectory")]
        public string LogDirectory { get; set; }

        [XmlElement(ElementName = "ProductsMaping")]
        public List<ProductsMaping> ProductsMaping { get; set; }

        [XmlElement(ElementName = "ProductMaping")]
        public List<ProductMaping> ProductMaping { get; set; }

        [XmlElement(ElementName = "AppTimeout")]
        public int? AppTimeout { get; set; }

        [XmlElement(ElementName = "CryptoCodeMapping")]
        public CryptoCodeMapping CryptoCodeMapping { get; set; }

        private void ValidateConfig()
        {

            if (IDCustomer == 0)
                throw new Exception("invalid element in xml report for field IDCustomer");

            if (DefaultSrcSystemServiceID == 0)
                throw new Exception("invalid element in xml report for field DefaultSrcSystemServiceID");

            foreach (var productsMaping in ProductsMaping)
            {
                if (productsMaping.GTIN.Any() && productsMaping.SrcSystemServiceID == 0)
                    throw new Exception("invalid element in xml report for field ProductsMaping SrcSystemServiceID");

                if (productsMaping.GTIN.Any() && string.IsNullOrEmpty(productsMaping.SrcSystem))
                    throw new Exception("invalid element in xml report for field ProductsMaping SrcSystem");
            }
            if (string.IsNullOrEmpty(ConnectionString))
                throw new Exception("invalid element in xml report for field ConnectionString");

            if (string.IsNullOrEmpty(DefaultSrcSystem))
                throw new Exception("invalid element in xml report for field DefaultSrcSystem");

            if (string.IsNullOrEmpty(ProductsStoredProcedure))
                throw new Exception("invalid element in xml report for field ProductsStoredProcedure");

            if (string.IsNullOrEmpty(SNXStoredProcedure))
                throw new Exception("invalid element in xml report for field SNXStoredProcedure");

            if (string.IsNullOrEmpty(MarkUploadedSerials))
                throw new Exception("invalid element in xml report for field MarkUploadedSerials");

            if (string.IsNullOrEmpty(GsnxUrl))
                throw new Exception("invalid element in xml report for field GsnxUrl");

            if (string.IsNullOrEmpty(GsnxUsername))
                throw new Exception("invalid element in xml report for field GsnxUsername");

            if (string.IsNullOrEmpty(GsnxPassword))
                throw new Exception("invalid element in xml report for field GsnxPassword");

            if (string.IsNullOrEmpty(GsnxApplicationId))
                throw new Exception("invalid element in xml report for field GsnxApplicationId");


            foreach (var mapping in ProductMaping)
            {
                if (string.IsNullOrEmpty(mapping.IDProduct))
                    throw new Exception($"invalid element in xml report for field ProductMaping IDProduct");

                if (string.IsNullOrEmpty(mapping.GTIN) || Utils.GTIN.Check(mapping.GTIN))
                    throw new Exception($"invalid element in xml report for field ProductMaping GTIN [{mapping.GTIN}]");

                if (string.IsNullOrEmpty(mapping.SrcSystem))
                    throw new Exception($"invalid element in xml report for field ProductMaping SrcSystem");

                if (mapping.SrcSystemServiceID == 0)
                    throw new Exception($"invalid element in xml report for field ProductMaping SrcSystemServiceID");
            }
            if (ProductsMaping != null)
            {
                foreach (var mapping in ProductsMaping)
                {
                    foreach (var gtin in mapping.GTIN)
                    {
                        if (!Utils.GTIN.Check(gtin))
                            throw new Exception($"invalid GTIN for field ProductsMaping.GTIN  [{gtin}]");

                    }
                }
            }
            if (CryptoCodeMapping != null)
            {
                if (CryptoCodeMapping.Issuer.Any(c => string.IsNullOrEmpty(c.SrcSystem)))
                    throw new Exception($"invalid SrcSystem  CryptoCodeMapping.Issuer.SrcSystem ");

                if (CryptoCodeMapping.Issuer.Any(c => c.SrcSystemServiceID == 0))
                    throw new Exception($"invalid SrcSystem  CryptoCodeMapping.Issuer.SrcSystemServiceID ");


                if (CryptoCodeMapping.Issuer.Any(c => c.AI91.Any(a => string.IsNullOrEmpty(a))))
                    throw new Exception($"Empty AI91 code  CryptoCodeMapping.Issuer.AI91 ");

            }

        }
    }


    [XmlRoot(ElementName = "ProductsMaping")]
    public class ProductsMaping
    {

        [XmlElement(ElementName = "SrcSystemServiceID")]
        public int SrcSystemServiceID { get; set; }

        [XmlElement(ElementName = "SrcSystem")]
        public string SrcSystem { get; set; }

        [XmlElement(ElementName = "GTIN")]
        public List<string> GTIN { get; set; }
    }

    [XmlRoot(ElementName = "ProductMaping")]
    public class ProductMaping
    {

        [XmlElement(ElementName = "GTIN")]
        public string GTIN { get; set; }

        [XmlElement(ElementName = "SrcSystemServiceID")]
        public int SrcSystemServiceID { get; set; }

        [XmlElement(ElementName = "SrcSystem")]
        public string SrcSystem { get; set; }

        [XmlElement(ElementName = "IDProduct")]
        public string IDProduct { get; set; }
    }

    [XmlRoot(ElementName = "CryptoCodeMapping")]
    public class CryptoCodeMapping
    {
        [XmlElement(ElementName = "Issuer")]
        public List<Issuer> Issuer { get; set; }
    }
    [XmlRoot(ElementName = "Issuer")]
    public class Issuer
    {
        [XmlElement(ElementName = "SrcSystem")]
        public string SrcSystem { get; set; }

        [XmlElement(ElementName = "SrcSystemServiceID")]
        public int SrcSystemServiceID { get; set; }

        [XmlElement(ElementName = "AI91")]
        public List<string> AI91 { get; set; }
    }

}

