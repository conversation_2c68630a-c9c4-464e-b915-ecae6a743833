﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;

namespace AzureBhModels
{
    [Table("SgFileVerificationError")]
    public class SgFileVerificationError : INotifyPropertyChanged
    {


        private int idSgFileVerificationError;
        [Key]
        public int IDSgFileVerificationError
        {
            get
            {
                return idSgFileVerificationError;
            }
            set
            {
                idSgFileVerificationError = value;
                NotifyPropertyChanged();
            }
        }



        private int idSgFile;
        [Required]
        public int IDSgFile
        {
            get
            {
                return idSgFile;
            }
            set
            {
                idSgFile = value;
                NotifyPropertyChanged();
            }
        }

        private string level;
        public string Level
        {
            get
            {
                return level;
            }
            set
            {
                level = value;
                NotifyPropertyChanged();
            }
        }

        private string error;
        [Required]
        public string Error
        {
            get
            {
                return error;
            }
            set
            {
                error = value;
                NotifyPropertyChanged();
            }
        }

        private string serialNumber;
        [Required]
        public string SerialNumber
        {
            get
            {
                return serialNumber;
            }
            set
            {
                serialNumber = value;
                NotifyPropertyChanged();
            }
        }

        private string serialType;
        [Required]
        public string SerialType
        {
            get
            {
                return serialType;
            }
            set
            {
                serialType = value;
                NotifyPropertyChanged();
            }
        }

        private string itemSerialNumber;
        public string ItemSerialNumber
        {
            get
            {
                return itemSerialNumber;
            }
            set
            {
                itemSerialNumber = value;
                NotifyPropertyChanged();
            }
        }

        private string itemSerialType;
        public string ItemSerialType
        {
            get
            {
                return itemSerialType;
            }
            set
            {
                itemSerialType = value;
                NotifyPropertyChanged();
            }
        }

        private bool isSkipAllowed;
        public bool IsSkipAllowed
        {
            get
            {
                return isSkipAllowed;
            }
            set
            {
                isSkipAllowed = value;
                NotifyPropertyChanged();
            }
        }

        private DateTime timestampCreated;
        [Required]
        [DisplayFormat(DataFormatString = "{0:dd.MM.yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated
        {
            get
            {
                return timestampCreated;
            }
            set
            {
                timestampCreated = value;
                NotifyPropertyChanged();
            }
        }

        private string userCreated;
        [Required]
        public string UserCreated
        {
            get
            {
                return userCreated;
            }
            set
            {
                userCreated = value;
                NotifyPropertyChanged();
            }
        }

        [InverseProperty("SgFileVerificationErrors")]
        public SgFile SgFile { get; set; }

        public event PropertyChangedEventHandler PropertyChanged;
        private void NotifyPropertyChanged([CallerMemberName] string propertyName = "")
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
