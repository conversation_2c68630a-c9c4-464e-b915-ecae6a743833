﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AzureEbrModels
{
    [Table("BatchSerializationSerial")]
    public class BatchSerializationSerial
    {
        [Key]
        public long IDBatchSerializationSerial { get; set; }

        [Required]
        public int IDBatchSerialization { get; set; }

        [Required]
        [StringLength(100)]
        public string RequestId { get; set; }

        [Required]
        [StringLength(100)]
        public string PoolCode { get; set; }

        [Required]
        [StringLength(100)]
        public string SerialNumber { get; set; }

        [StringLength(100)]
        public string AI91 { get; set; }

        [StringLength(100)]
        public string AI92 { get; set; }

        [Required]
        public SerialStateEnum State { get; set; }

        public int? IDLineRequest { get; set; }

        [Required]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime TimestampCreated { get; set; }

        [Required]
        [StringLength(128)]
        public string UserCreated { get; set; }

        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm:ss}", ApplyFormatInEditMode = true)]
        public DateTime? TimestampUpdated { get; set; }

        [StringLength(128)]
        public string UserUpdated { get; set; }

        [InverseProperty("BatchSerializationSerials")]
        public BatchSerialization BatchSerialization { get; set; }
    }
}
