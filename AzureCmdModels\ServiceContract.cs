﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SGAuditTrail;

namespace AzureCmdModels
{
    [Table("ServiceContract")]
    [Audit(DisplayName = "Service Contract")]
    public partial class ServiceContract
    {
        [Key]
        public int IDServiceContract { get; set; }

        [Audit(DisplayName = "Service", InversePropertyName = "Service", InversePropertyValue = "Name")]
        public int IDContract { get; set; }

        [Audit(DisplayName = "Contract", InversePropertyName = "Contract", InversePropertyValue = "Name")]
        public int IDService { get; set; }

        [InverseProperty("ServiceContracts")]
        public Service Service { get; set; }

        [InverseProperty("ServiceContracts")]
        public Contract Contract { get; set; }


    }
}
